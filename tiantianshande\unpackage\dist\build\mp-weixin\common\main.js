(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{5814:function(t,e,a){"use strict";(function(t,o){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={globalData:{pre_url:"",baseurl:"",session_id:"",aid:0,mid:0,pid:0,needAuth:0,platform:"wx",platform2:"",isdouyin:0,sysset:[],indexurl:"/pages/index/index",menudata:[],menu2data:[],currentIndex:-1,initdata:{},textset:[],isinit:!1,socketOpen:!1,socket_token:"",socketMsgQueue:[],socketConnecttimes:0,socketInterval:null,scene:0,homeNavigationCustom:0,usercenterNavigationCustom:0,navigationBarBackgroundColor:"#ffffff",navigationBarTextStyle:"black",rewardedVideoAd:{},uinfo:{bid:0,un:"",id:0}},onLaunch:function(e){var o=t.getExtConfigSync?t.getExtConfigSync():{};if(o&&o.aid)this.globalData.aid=o.aid,this.globalData.pre_url=o.baseurl,this.globalData.homeNavigationCustom=o.homeNavigationCustom||0,this.globalData.usercenterNavigationCustom=o.usercenterNavigationCustom||0,this.globalData.navigationBarBackgroundColor=o.navigationBarBackgroundColor||"#ffffff",this.globalData.navigationBarTextStyle=o.navigationBarTextStyle||"black";else{var n=a("1993");this.globalData.aid=n.uniacid,this.globalData.pre_url=n.siteroot,this.globalData.homeNavigationCustom=n.homeNavigationCustom||0,this.globalData.usercenterNavigationCustom=n.usercenterNavigationCustom||0,this.globalData.navigationBarBackgroundColor=n.navigationBarBackgroundColor||"#ffffff",this.globalData.navigationBarTextStyle=n.navigationBarTextStyle||"black"}this.globalData.baseurl=this.globalData.pre_url+"/?s=/",this.globalData.session_id=t.getStorageSync("session_id");var i=this.getopts(e.query);i&&i.pid&&(this.globalData.pid=i.pid,t.setStorageSync("pid",this.globalData.pid)),i&&i.uid&&(this.globalData.uid=i.uid),this.globalData.platform="wx",this.checkUpdateVersion(),console.log(this.globalData.platform);var l=this;t.onSocketOpen((function(t){l.globalData.socketOpen=!0;for(var e=0;e<l.globalData.socketMsgQueue.length;e++)l.sendSocketMessage(l.globalData.socketMsgQueue[e]);l.globalData.socketMsgQueue=[]})),t.onSocketMessage((function(t){console.log("收到服务器内容："+t.data);try{var e=JSON.parse(t.data),a=!0,o=getCurrentPages(),n=o[o.length-1];if(!n)return;if(n&&n.$vm.hasOwnProperty("receiveMessage")){var i=n.$vm.receiveMessage(e);a=!i}!a||"tokefu"!=e.type&&"tokehu"!=e.type&&"peisong"!=e.type&&"notice"!=e.type||n.$vm.$refs.popmsg.open(e)}catch(l){}}));var s,r=t.getSystemInfoSync(),c=r.statusBarHeight,g=t.getMenuButtonBoundingClientRect();s=g.bottom+g.top-c,this.$store.commit("setStatusBarHeight",c),this.$store.commit("setCustomBarHeight",s),this.$store.commit("setWindowHeight",r.windowHeight),this.$store.commit("setScreenHeight",r.screenHeight)},onShow:function(t){var e=this;console.log("onShow"),console.log(t),console.log("---------xxxx"),t&&t.scene&&(this.globalData.scene=t.scene);if([1007,1008,1014,1044,1045,1046,1047,1048,1049,1073,1154,1155].includes(this.globalData.scene)){console.log("---------00");try{var a=requirePlugin("live-player-plugin");console.log(a),console.log("---------11"),a.getShareParams().then((function(t){var a=t.custom_params;console.log(a),a&&a.pid&&(e.globalData.pid=a.pid)})).catch((function(t){console.log("get share params err ",t)}))}catch(o){console.log("live-player-plugin err ",o)}}if(e.globalData.mid&&e.globalData.socket_token){this.openSocket()}this.checkSystemInfo()},methods:{isPhone:function(t){var e=new RegExp("^1[3456789]\\d{9}$");return e.test(t)},isIdCard:function(t){return/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(t)},getopts:function(t){if(t&&t.scene){var e=t.scene,a=e.split("-"),o=Object.assign({},t);for(var n in a){var i=a[n].split("_");o[i[0]]=i[1]}return delete o.scene,o}return t},alert:function(e,a){t.showModal({title:"提示信息",showCancel:!1,content:e.toString(),success:function(t){t.confirm&&"function"==typeof a&&a()}})},confirm:function(e,a,o){t.showModal({title:"操作确认",content:e.toString(),showCancel:!0,success:function(t){t.confirm?"function"==typeof a&&a():"function"==typeof o&&o()}})},success:function(e,a){void 0==e&&(e="操作成功");e=e.toString();t.showToast({title:e,icon:e.length>8?"none":"success",success:function(t){"function"==typeof a&&a()}})},error:function(e,a){!1===e?t.hideToast():(this.isNull(a)&&(a=2500),void 0==e&&(e="操作失败"),t.showToast({title:e.toString(),icon:"none",duration:a}))},showLoading:function(e){!1===e?t.hideLoading():(void 0==e&&(e="加载中"),t.showLoading({title:e.toString(),mask:!0}))},inArray:function(t,e){for(var a in e)if(e[a]==t)return!0;return!1},isNull:function(t){return void 0==t||"undefined"==t||null==t||""==t},parseJSON:function(t){try{return JSON.parse(t)}catch(e){return}},getparams:function(t){if(-1===t.indexOf("?"))return{};for(var e=t.split("?")[1],a=e.split("&"),o={},n=0;n<a.length;n++){var i=a[n].split("=");o[i[0]]=i[1]}return o},goto:function(e,n){var i=this,l=i.getparams(e);if(console.log(l),l&&1==l.reloadthispage){var s=getCurrentPages().pop();return s.$vm.opt=l,void s.$vm.getdata()}if(i.isNull(n)&&e){var r=i.globalData.menudata["list"];if(r)for(var c=0;c<r.length;c++)r[c]["pagePath"]==e&&(n="reLaunch")}if(0===e.indexOf("pages/")&&(e="/"+e),0===e.indexOf("/pages/commission/")&&(e=e.replace("/pages/commission/","/activity/commission/")),0===e.indexOf("/pages/sign/")&&(e=e.replace("/pages/sign/","/pagesExt/sign/")),0===e.indexOf("/pages/business/")&&(e=e.replace("/pages/business/","/pagesExt/business/")),0===e.indexOf("/pages/lipin/")&&(e=e.replace("/pages/lipin/","/pagesExt/lipin/")),0===e.indexOf("/pages/order/")&&(e=e.replace("/pages/order/","/pagesExt/order/")),1==i.globalData.isdouyin&&0===e.indexOf("/shopPackage/shop/product"))return i.showLoading("加载中"),void i.post("ApiShop/getDouyinProductId",{proid:l.id},(function(t){i.showLoading(!1),1==t.status?tt.openEcGood({promotionId:t.douyin_product_id,fail:function(t){i.alert(t.errMsg)}}):i.alert(t.msg)}));if("wx"!=i.globalData.platform||0!==e.indexOf("https://work.weixin.qq.com/kfid/"))if("scan::"!=e){if("share::"==e)return"h5"!=i.globalData.platform&&"mp"!=i.globalData.platform||i.error("点击右上角发送给好友或分享到朋友圈"),void("app"==i.globalData.platform&&t.showActionSheet({itemList:["发送给微信好友","分享到微信朋友圈"],success:function(e){if(e.tapIndex>=0){var a="WXSceneSession";1==e.tapIndex&&(a="WXSenceTimeline");var o={provider:"weixin",type:0};o.scene=a,o.title=i.globalData.initdata.name;var n=i._fullurl();i.globalData.mid>0&&(n+=(-1===n.indexOf("?")?"?":"&")+"pid="+i.globalData.mid),o.href=i.globalData.pre_url+"/h5/"+i.globalData.aid+".html#"+n,o.imageUrl=i.globalData.initdata.logo,o.summary=i.globalData.initdata.desc;var l=i.globalData.initdata.sharelist;if(l)for(var s=0;s<l.length;s++)if(l[s]["indexurl"]==i._fullurl()&&(o.title=l[s].title,o.summary=l[s].desc,o.imageUrl=l[s].pic,l[s].url)){var r=l[s].url;0===r.indexOf("/")&&(r=i.globalData.pre_url+"/h5/"+i.globalData.aid+".html#"+r),i.globalData.mid>0&&(r+=(-1===r.indexOf("?")?"?":"&")+"pid="+i.globalData.mid),o.href=r}t.share(o)}}}));if(e&&"contact::"!=e&&"share::"!=e){if(0===e.indexOf("tel::"))return e=e.slice(5),void t.makePhoneCall({phoneNumber:e});if(0===e.indexOf("tel:"))return e=e.slice(4),void t.makePhoneCall({phoneNumber:e});if(0===e.indexOf("url::")&&("h5"==i.globalData.platform||"mp"==i.globalData.platform?location.href=e.slice(5):e="/pages/index/webView?url="+encodeURIComponent(e.slice(5))),0!==e.indexOf("https://")&&0!==e.indexOf("http://")||("h5"==i.globalData.platform||"mp"==i.globalData.platform?location.href=e:e="/pages/index/webView?url="+encodeURIComponent(e)),0!==e.indexOf("miniProgram::"))if("getmembercard::"!=e)if(0!==e.indexOf("location::"))if(0===e.indexOf("plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin")&&(e=e+"&custom_params="+encodeURIComponent(JSON.stringify({pid:i.globalData.mid}))),0!==e.indexOf("copy::")){if(0===e.indexOf("rewardedVideoAd::")){console.log("rewardedVideoAd"),e=e.slice(17);x=e.split("|");return o.createRewardedVideoAd&&(i.showLoading(),i.post("ApiRewardVideoAd/getunitid",{hid:x[0]},(function(t){if(0!=t.status){i.globalData.rewardedVideoAd[t.unitid]||(i.globalData.rewardedVideoAd[t.unitid]=o.createRewardedVideoAd({adUnitId:t.unitid}));var e=i.globalData.rewardedVideoAd[t.unitid];e.load().then((function(){i.showLoading(!1),e.show()})).catch((function(t){i.alert("加载失败")})),e.onError((function(t){i.showLoading(!1),i.alert(t),console.log("onError event emit",t),e.offLoad(),e.offClose()})),e.onClose((function(t){t&&t.isEnded?i.post("ApiRewardVideoAd/givereward",{hid:x[0]},(function(t){0==t.status?i.alert(t.msg):i.success(t.msg)})):console.log("播放中途退出，不下发奖励"),e.offLoad(),e.offClose()}))}else i.alert(t.msg)}))),!1}if(i.isNull(n)){var g=e.split("?"),u=g[0];n="h5"==i.globalData.platform&&u==i._fullurl()?"reLaunch":"navigate"}if(i.globalData.rewardedVideoAd={},"switchTab"==n){var d,p,f=new Object,h=e,m=h.indexOf("?");if(m>=0){var b=h.substr(0,m),v=h.substr(m+1),D=v.split("&");for(c=0;c<D.length;c++)m=D[c].indexOf("="),m>0&&(d=D[c].substring(0,m),p=D[c].substr(m+1),f[d]=p);t.switchTab({url:b,success:function(t){var e=getCurrentPages().pop();void 0!=e&&null!=e&&e.onLoad(f)}})}else t.switchTab({url:e})}else if("redirect"==n||"redirectTo"==n)t.redirectTo({url:e});else if("reLaunch"==n)t.reLaunch({url:e});else{var y=getCurrentPages();y.length>=10?t.redirectTo({url:e}):t.navigateTo({url:e})}}else{e=e.slice(6);x=e.split("|");t.setClipboardData({data:x[0],showToast:!1,success:function(){"复制成功"!=x[1]?i.alert(x[1]):x[1]&&i.error(x[1])}})}else{e=e.slice(10);var x=e.split("|"),w=x[1].split(",");t.openLocation({latitude:parseFloat(w[1]),longitude:parseFloat(w[0]),name:x[0],scale:13})}else i.post("ApiCoupon/getmembercardparam",{card_id:""},(function(e){0!=e.status?"wx"==i.globalData.platform?t.navigateToMiniProgram({appId:"wxeb490c6f9b154ef9",extraData:e.extraData,success:function(){},fail:function(){},complete:function(){}}):location.href=e.ret_url:i.alert(e.msg)}));else{e=e.slice(13);var x=e.split("|");"app"==i.globalData.platform?plus.share.getServices((function(t){for(var e=null,a=0;a<t.length;a++){var o=t[a];"weixin"==o.id&&(e=o)}e&&e.launchMiniProgram({id:x[2],type:0,path:x[1]?x[1]:""})}),(function(t){console.log(JSON.stringify(t))})):t.navigateToMiniProgram({appId:x[0],path:x[1]?x[1]:"",complete:function(){x[2]&&i.goto(x[2])}})}}}else{if("h5"==i.globalData.platform)return void i.alert("请使用微信扫一扫功能扫码");if("mp"==i.globalData.platform){var _=a("6fcc");_.ready((function(){_.scanQRCode({needResult:0,scanType:["qrCode","barCode"],success:function(t){t.resultStr}})}))}else t.scanCode({success:function(t){if(console.log(t),t.path)i.goto("/"+t.path);else{var e=t.result;i.goto(e)}}})}else o.openCustomerServiceChat({extInfo:{url:e},corpId:i.globalData.initdata.corpid})},goback:function(e){var a=this,o=getCurrentPages();if(e&&o.length>1){var n=o[o.length-2];n.$vm.getdata()}1==o.length?a.goto(a.globalData.indexurl,"reLaunch"):t.navigateBack({fail:function(){a.goto(a.globalData.indexurl,"reLaunch")}})},post:function(t,e,a){this.request("POST",t,e,a)},get:function(t,e,a){this.request("GET",t,e,a)},request:function(e,a,o,n){var i=a,l=this;"https://"!=a.substring(0,8)&&(a=l.globalData.baseurl+a,a+=(a.indexOf("?")>0?"&":"?")+"aid="+l.globalData.aid+"&platform="+l.globalData.platform+"&session_id="+l.globalData.session_id+"&pid="+l.globalData.pid,1==l.globalData.isdouyin&&(a+="&isdouyin=1"),l.globalData.isinit||(a+="&needinit=1",l.globalData.uid&&(a+="&uid="+l.globalData.uid))),t.request({url:a,data:o,method:e,success:function(t){if(l.setinitdata(t),t.data&&-1==t.data.status){l.showLoading(!1);var e=l.globalData.initdata.logintype&&l.globalData.initdata.logintype.indexOf&&-1!==l.globalData.initdata.logintype.indexOf("3");if(e&&l.inArray(l.globalData.platform,["wx","mp","baidu","qq","toutiao","alipay"]))return;var a=getCurrentPages(),o=a[a.length-1];o.$vm.loading=!1;var i="",s=l._url(),r="reLaunch";if("baidu"==l.globalData.platform&&(r="/pages/my/usercenter"==s?"redirect":"navigate"),"/pagesB/login/login"!=s&&"/pages/index/reg"!=s&&"/pages/index/getpwd"!=s)i=encodeURIComponent(l._fullurl());if(1!=t.data.authlogin)return void l.goto("/pagesB/login/login?frompage="+i,r);l.authlogin((function(t){1==t.status?(t.msg&&l.success(t.msg),o.$vm.getdata()):2==t.status?l.goto("/pagesB/login/login?frompage="+i+"&logintype=4&login_bind=1",r):(console.log(t),l.goto("/pagesB/login/login?frompage="+i,r))}))}else if(t.data&&-10==t.data.status){l.showLoading(!1);a=getCurrentPages(),o=a[a.length-1];o.$vm.loading=!1,l.goto("/admin/index/login","redirect")}else if(t.data&&-2==t.data.status){l.showLoading(!1);a=getCurrentPages(),o=a[a.length-1];o.$vm.loading=!1,location.href=t.data.url}else if(t.data&&-3==t.data.status){l.showLoading(!1);a=getCurrentPages(),o=a[a.length-1];o.$vm.loading=!1,l.goto(t.data.url,"redirect")}else if(t.data&&-4==t.data.status){l.showLoading(!1);a=getCurrentPages(),o=a[a.length-1];o.$vm.loading=!1,l.alert(t.data.msg,(function(){t.data.url&&l.goto(t.data.url,"redirect")}))}else"function"==typeof n&&n(t.data)},fail:function(t){try{"ApiIndex/linked"!=i&&"request:fail timeout"!=t.errMsg&&console.log(t)}catch(e){}}})},baselogin:function(t){console.log("baselogin");var e=this;"wx"==e.globalData.platform?o.login({success:function(a){var o=a.code;e.post("ApiIndex/wxbaselogin",{code:o,pid:e.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}}):"baidu"==e.globalData.platform?swan.getLoginCode({success:function(a){console.log(a);var o=a.code;e.post("ApiIndex/baidulogin",{code:o,pid:e.globalData.pid},(function(e){"function"==typeof t&&t(e)}))},fail:function(t){console.log(t)}}):"qq"==e.globalData.platform?qq.login({success:function(a){e.post("ApiIndex/qqlogin",{code:a.code,pid:e.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}}):"toutiao"==e.globalData.platform?tt.login({force:!0,success:function(a){e.post("ApiIndex/toutiaologin",{code:a.code,pid:e.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}}):"alipay"==e.globalData.platform?my.getAuthCode({scopes:"auth_base",success:function(a){e.post("ApiIndex/alipaylogin",{code:a.authCode,pid:e.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}}):"mp"==e.globalData.platform&&console.log("🎯 baselogin检测到公众号平台，准备跳转静默登录")},authlogin:function(t,e){var a=this;if("wx"==a.globalData.platform)o.getUserProfile({lang:"zh_CN",desc:"用于展示头像昵称",success:function(e){console.log(e);var n=e.userInfo;o.login({success:function(e){var o=e.code;a.post("ApiIndex/wxlogin",{code:o,userinfo:n,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}})},fail:function(e){console.log(e),"getUserProfile:fail auth deny"==e.errMsg||"function"==typeof t&&t({status:0,msg:e.errMsg})}});else if("mp"==a.globalData.platform||"h5"==a.globalData.platform){var n="",i=a._url();"/pagesB/login/login"!=i&&"/pages/index/reg"!=i&&"/pages/index/getpwd"!=i&&(n=encodeURIComponent(a._fullurl())),e&&e.frompage&&(n=e.frompage),location.href=a.globalData.pre_url+"/index.php?s=ApiIndex/shouquan&aid="+a.globalData.aid+"&session_id="+a.globalData.session_id+"&pid="+a.globalData.pid+"&frompage="+encodeURIComponent(n)}else"app"==a.globalData.platform?plus.oauth.getServices((function(e){console.log(e);var o=e[0];for(var n in e){var i=e[n];"weixin"==i.id&&(o=i)}console.log(o),console.log("x----"),o.authorize((function(e){console.log(e);var o=e.code;a.post("ApiIndex/appwxlogin",{code:o,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)}))}))})):"baidu"==a.globalData.platform?swan.getLoginCode({success:function(e){console.log("getLoginCode success",e);var o=e.code;a.post("ApiIndex/baidulogin",{code:o,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)}))},fail:function(e){"function"==typeof t&&t({status:0,msg:e.errMsg})}}):"qq"==a.globalData.platform?qq.login({success:function(e){e.code?a.post("ApiIndex/qqlogin",{code:e.code,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)})):"function"==typeof t&&t({status:0,msg:e.errMsg})},fail:function(e){"function"==typeof t&&t({status:0,msg:e.errMsg})}}):"toutiao"==a.globalData.platform?tt.login({force:!0,success:function(e){e.code?a.post("ApiIndex/toutiaologin",{code:e.code,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)})):"function"==typeof t&&t({status:0,msg:e.errMsg})},fail:function(e){"function"==typeof t&&t({status:0,msg:e.errMsg})}}):"alipay"==a.globalData.platform&&my.getAuthCode({scopes:"auth_base",success:function(e){console.log(e),e.authCode&&a.post("ApiIndex/alipaylogin",{code:e.authCode,pid:a.globalData.pid},(function(e){"function"==typeof t&&t(e)}))},fail:function(e){"function"==typeof t&&t({status:0,msg:e.errMsg})}})},setinitdata:function(e){var n=this,i=n.globalData.mid;if(e&&e.data&&(e.data.mid||0===e.data.mid)&&n.globalData.mid!=e.data.mid&&(n.globalData.mid=e.data.mid,e.data.session_id&&(t.setStorageSync("session_id",e.data.session_id),n.globalData.session_id=e.data.session_id),n.globalData.mid&&(n.globalData.socket_token=e.data.socket_token,n.openSocket(),t.removeStorageSync("pid"))),e&&e.data&&e.data._initdata){if(n.globalData.isinit=!0,e.data._initdata.pre_url=n.globalData.pre_url,n.globalData.initdata=e.data._initdata,n.globalData.mid=e.data._initdata.mid,n.globalData.isdouyin=e.data._initdata.isdouyin,t.setStorageSync("session_id",e.data._initdata.session_id),n.globalData.session_id=e.data._initdata.session_id,"mp"==n.globalData.platform){var l=e.data.share_package,s=a("6fcc");s.config({debug:!1,appId:l.appId,timestamp:l.timestamp,nonceStr:l.nonceStr,signature:l.signature,jsApiList:["checkJsApi","onMenuShareAppMessage","onMenuShareTimeline","updateAppMessageShareData","updateTimelineShareData","chooseImage","previewImage","uploadImage","openLocation","getLocation","closeWindow","scanQRCode","chooseWXPay","addCard","chooseCard","openCard"],openTagList:["wx-open-launch-weapp"]})}n.globalData.mid&&(n.globalData.socket_token=e.data.socket_token,n.openSocket(),t.removeStorageSync("pid")),console.log("=== 静默登录条件检查 ==="),console.log("app.globalData.mid:",n.globalData.mid),console.log("app.globalData.platform:",n.globalData.platform),console.log("app.globalData.initdata.logintype:",n.globalData.initdata.logintype),console.log("logintype.length:",n.globalData.initdata.logintype.length);var r=n.globalData.initdata.logintype&&n.globalData.initdata.logintype.indexOf&&-1!==n.globalData.initdata.logintype.indexOf("3"),c=n.inArray(n.globalData.platform,["wx","mp","baidu","qq","toutiao","alipay"]);console.log("条件1 (!mid):",!n.globalData.mid),console.log("条件2 (支持授权登录):",r),console.log("条件3 (平台支持):",c),!n.globalData.mid&&r&&c?(console.log("✅ 满足静默登录条件，开始执行baselogin"),n.baselogin((function(t){console.log("baselogin回调结果:",t);var e=getCurrentPages(),a=e[e.length-1],o="/"+(a.route?a.route:a.__route__);if("/pagesB/login/login"==o){if("baidu"==n.globalData.platform)var i=a.options;else i=a.$vm.opt;console.log(i),i&&i.frompage?n.goto(decodeURIComponent(i.frompage),"redirect"):n.goto("/pages/my/usercenter","redirect")}else a.$vm.getdata()}))):(console.log("❌ 不满足静默登录条件，原因："),n.globalData.mid&&console.log("  - 用户已登录，mid="+n.globalData.mid),r||console.log("  - logintype中不包含授权登录选项(3)"),c||console.log("  - 平台不支持静默登录"))}n.globalData.mid&&n.globalData.mid!=i&&("wx"==n.globalData.platform?o.login({success:function(t){t.code&&n.post("ApiIndex/setwxopenid",{code:t.code},(function(){}))}}):"alipay"==n.globalData.platform?my.getAuthCode({scopes:["auth_base"],success:function(t){t.authCode&&n.post("ApiIndex/setalipayopenid",{code:t.authCode},(function(){}))}}):"baidu"==n.globalData.platform&&swan.getLoginCode({success:function(t){t.code&&n.post("ApiIndex/setbaiduopenid",{code:t.code},(function(){}))},fail:function(t){console.log("getLoginCode fail",t)}}))},openSocket:function(){console.log("openSocket");var e=this;e.globalData.socketOpen=!1,t.closeSocket(),t.connectSocket({url:e.globalData.pre_url.replace("https://","wss://")+"/wss"}),e.sendSocketMessage({type:"khinit",data:{aid:e.globalData.aid,mid:e.globalData.mid,platform:e.globalData.platform}}),clearInterval(e.globalData.socketInterval),e.globalData.socketInterval=setInterval((function(){e.sendSocketMessage({type:"connect"})}),25e3)},sendSocketMessage:function(e){var a=this;e.token||(e.token=this.globalData.socket_token),a.globalData.socketOpen?(console.log(e),t.sendSocketMessage({data:JSON.stringify(e),fail:function(t){console.log(t),console.log("发送失败"),a.globalData.socketConnecttimes<1&&(a.globalData.socketConnecttimes++,a.globalData.socketMsgQueue.push(e),console.log("openSocket 重连"),a.openSocket())},success:function(){console.log("发送成功"),a.globalData.socketConnecttimes=0}})):this.globalData.socketMsgQueue.push(e)},chooseImage:function(e,a){var o=this;t.chooseImage({count:a||1,sizeType:["original","compressed"],sourceType:["album","camera"],success:function(a){for(var n=a.tempFilePaths,i=[],l=0;l<n.length;l++)o.showLoading("上传中"),t.uploadFile({url:o.globalData.baseurl+"ApiImageupload/uploadImg/aid/"+o.globalData.aid+"/platform/"+o.globalData.platform+"/session_id/"+o.globalData.session_id,filePath:n[l],name:"file",success:function(t){o.showLoading(!1);var a=JSON.parse(t.data);1==a.status?(i.push(a.url),i.length==n.length&&"function"==typeof e&&e(i)):o.alert(a.msg)},fail:function(t){o.showLoading(!1),o.alert(t.errMsg)}})},fail:function(t){}})},getLocation:function(e,o){var n=this;if("mp"==this.globalData.platform){var i=a("6fcc");i.ready((function(){i.getLocation({type:"gcj02",success:function(t){n.setCache("getLocationCatch",t),"function"==typeof e&&e(t)},fail:function(t){var a=n.getCache("getLocationCatch");a?"function"==typeof e&&e(a):"function"==typeof o&&o(t)}})}))}else"alipay"==this.globalData.platform?t.getLocation({success:function(t){n.setCache("getLocationCatch",t),"function"==typeof e&&e(t)},fail:function(t){var a=n.getCache("getLocationCatch");a?"function"==typeof e&&e(a):"function"==typeof o&&o(t)}}):t.getLocation({type:"gcj02",success:function(t){n.setCache("getLocationCatch",t),"function"==typeof e&&e(t)},fail:function(t){var a=n.getCache("getLocationCatch");a?"function"==typeof e&&e(a):"function"==typeof o&&o(t)}})},setCache:function(e,a){return t.setStorageSync(e,a)},getCache:function(e){return t.getStorageSync(e)},removeCache:function(e){this.isNull(e)?t.clearStorageSync():t.removeStorageSync(e)},_url:function(){var t=getCurrentPages(),e=t[t.length-1],a="/"+(e.route?e.route:e.__route__);return a},_fullurl:function(){var t=getCurrentPages(),e=t[t.length-1],a="/"+(e.route?e.route:e.__route__);if("baidu"==this.globalData.platform)var o=e.options;else o=e.$vm.opt;console.log(o);var n=[];for(var i in o)n.push(i+"="+o[i]);return n.length>0&&(a+="?"+n.join("&")),console.log(a),a},checkUpdateVersion:function(){if(o.canIUse("getUpdateManager")){var t=o.getUpdateManager();t.onCheckForUpdate((function(e){e.hasUpdate&&(t.onUpdateReady((function(){t.applyUpdate()})),t.onUpdateFailed((function(){})))}))}},checkSystemInfo:function(){var e=t.getSystemInfoSync();this.$store&&this.$store.state&&e.windowHeight!==this.$store.state.windowHeight&&(this.$store.commit("setWindowHeight",e.windowHeight),this.$store.commit("setScreenHeight",e.screenHeight))},Debounce:function(t,e){var a;return function(){for(var o=arguments.length,n=new Array(o),i=0;i<o;i++)n[i]=arguments[i];var l=this;clearTimeout(a),a=setTimeout((function(){t.apply(l,n)}),e)}}}};e.default=n}).call(this,a("df3c")["default"],a("3223")["default"])},"6a19":function(t,e,a){"use strict";a.r(e);var o=a("5814"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},7979:function(t,e,a){"use strict";var o=a("a655"),n=a.n(o);n.a},9258:function(t,e,a){"use strict";(function(t,e,o){var n=a("47a9"),i=n(a("7ca3"));a("06e9");var l=n(a("3240")),s=n(a("a637")),r=n(a("4d79"));function c(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,o)}return a}function g(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?c(Object(a),!0).forEach((function(e){(0,i.default)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}t.__webpack_require_UNI_MP_PLUGIN__=a;l.default.use(r.default),l.default.component("t-index-address",(function(){a.e("components/t-index-address/t-index-address").then(function(){return resolve(a("3ce1"))}.bind(null,a)).catch(a.oe)}));var u=new r.default({locale:"zh-CN",messages:{"zh-CN":{}}});l.default.config.productionTip=!1,l.default.mixin({onShareAppMessage:function(){return this._sharewx()},onShareTimeline:function(){var t=this._sharewx(),e=t.path.split("?")[1];return{title:t.title,imageUrl:t.imageUrl,query:e}},onNavigationBarButtonTap:function(t){console.log(t);var e=getApp();if("home"==t.type){var a=e._url();0===a.indexOf("/admin/")?e.goto("/admin/index/index","reLaunch"):e.goto(e.globalData.indexurl,"reLaunch")}},methods:{goto:function(t){getApp().goto(t.currentTarget.dataset.url,t.currentTarget.dataset.opentype)},goback:function(){getApp().goback()},getmenuindex:function(t){this.menuindex=t},loaded:function(t){t&&t.title&&!t.desc&&(t.desc=t.title);var a=this;e.stopPullDownRefresh();var o=getApp();0==o.globalData.isinit?o.get("ApiIndex/linked",{},(function(){a.isload=!0,a._sharemp(t)})):(this.isload=!0,this._sharemp(t))},getdata:function(){var t=this;getApp().get("ApiIndex/linked",{},(function(){t.loaded()}))},getplatform:function(){return getApp().globalData.platform},_sharemp:function(t){},_sharewx:function(t){t||(t={});var e=getApp(),a=getCurrentPages(),o=a[a.length-1],n="/"+(o.route?o.route:o.__route__),i="";this.opt;this.opt&&this.opt.id?i+="?id="+this.opt.id:this.opt&&this.opt.cid?i+="?cid="+this.opt.cid:this.opt&&this.opt.gid?i+="?gid="+this.opt.gid:this.opt&&this.opt.bid&&(i+="?bid="+this.opt.bid);var l=n+i,s=e.globalData.initdata.sharelist;if(s)for(var r=0;r<s.length;r++)(1==s[r]["is_rootpath"]&&s[r]["indexurl"]==n||!s[r]["is_rootpath"]&&s[r]["indexurl"]==l)&&(t.title=s[r].title,t.desc=s[r].desc,t.pic=s[r].pic,t.link=s[r].url);if(t.link){var c=t.link;c.indexOf("#")>0&&(c=c.split("#")[1])}else if(t.tolink)c=t.tolink.indexOf("#")>0?t.tolink.split("#")[1]:t.tolink;else c=this.sharepath();if(t.title)var g=t.title;else g=e.globalData.initdata.name;if(t.pic)var u=t.pic;else u="";return console.log(c),"function"==typeof t.callback&&t.callback(),{title:g,path:c,imageUrl:u}},sharepath:function(){var t=getApp(),e=this.opt,a="/"+(this.route?this.route:this.__route__),o=[];for(var n in e)"pid"!=n&&"scene"!=n&&o.push(n+"_"+e[n]);console.log(t.globalData.mid),t.globalData.mid&&o.push("pid_"+t.globalData.mid);var i=o.join("-");return i&&(a=a+"?scene="+i+"&t="+parseInt((new Date).getTime()/1e3)),a},t:function(t){if("color1"==t)return getApp().globalData.initdata.color1;if("color2"==t)return getApp().globalData.initdata.color2;if("color1rgb"==t){var e=getApp().globalData.initdata.color1rgb;return e&&void 0!==e.red&&void 0!==e.green&&void 0!==e.blue?e["red"]+","+e["green"]+","+e["blue"]:"0,122,255"}if("color2rgb"==t){var a=getApp().globalData.initdata.color2rgb;return a&&void 0!==a.red&&void 0!==a.green&&void 0!==a.blue?a["red"]+","+a["green"]+","+a["blue"]:"255,87,34"}return getApp().globalData.initdata.textset&&getApp().globalData.initdata.textset[t]?getApp().globalData.initdata.textset[t]:t},inArray:function(t,e){for(var a in e)if(e[a]==t)return!0;return!1},isNull:function(t){return void 0==t||"undefined"==t||null==t||""==t},isEmpty:function(t){return!t||0===t.length||1===t.length&&(!t[0]||0===t[0].length)},dateFormat:function(t,e){void 0!=e&&"undefined"!=e&&null!=e&&""!=e||(e="Y-m-d H:i:s");var a=new Date;(""!=t||t>0)&&(a=new Date(1e3*t));var o=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,i=a.getDate()<10?"0"+a.getDate():a.getDate(),l=a.getHours()<10?"0"+a.getHours():a.getHours(),s=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),r=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();return e=e.replace("Y",o),e=e.replace("m",n),e=e.replace("d",i),e=e.replace("H",l),e=e.replace("i",s),e=e.replace("s",r),e},getDistance:function(t,e,a,o){if(!t||!e||!a||!o)return"";var n=t*Math.PI/180,i=a*Math.PI/180,l=n-i,s=e*Math.PI/180-o*Math.PI/180,r=12756274*Math.asin(Math.sqrt(Math.pow(Math.sin(l/2),2)+Math.cos(n)*Math.cos(i)*Math.pow(Math.sin(s/2),2)));return r/=1e3,r=r.toFixed(2),r},showMap:function(t){var a=parseFloat(t.currentTarget.dataset.latitude),o=parseFloat(t.currentTarget.dataset.longitude),n=(t.currentTarget.dataset.scale&&parseInt(t.currentTarget.dataset.scale),t.currentTarget.dataset.name),i=t.currentTarget.dataset.address;e.openLocation({latitude:a,longitude:o,name:n,address:i,scale:13})},previewImage:function(t){var a=t.currentTarget.dataset.url,o=t.currentTarget.dataset.urls;o||(o=a),o&&("string"==typeof o&&(o=o.split(",")),e.previewImage({current:a,urls:o}))},copy:function(t){e.setClipboardData({data:t.currentTarget.dataset.text,success:function(){getApp().error("复制成功")}})},subscribeMessage:function(t){var a=getApp(),o=this.tmplids;o&&o.length>0?e.requestSubscribeMessage({tmplIds:o,success:function(e){for(var n in o)"accept"==e[o[n]]&&a.post("ApiIndex/subscribemessage",{tmplid:o[n]},(function(){}));console.log(e),"function"==typeof t&&t()},fail:function(e){console.log(e),"function"==typeof t&&t()}}):"function"==typeof t&&t()},shoukuan:function(e){var o=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return console.log("进入main.js方法"),new Promise((function(l,s){var r=o;"wx"==d.globalData.platform||"mp"==d.globalData.platform?(r.loading=!0,d.post("ApiMy/getwithdrawinfo",{id:e,type:n},(function(o){r.loading=!1;var s=o.detail,c=o.appinfo;if(s.platform==d.globalData.platform){if("wx"==d.globalData.platform)t.canIUse("requestMerchantTransfer")?t.requestMerchantTransfer({mchId:c.wxpay_mchid,appId:t.getAccountInfoSync().miniProgram.appId,package:s.wx_package_info,success:function(t){console.log("success:",t),r.loading=!0,d.post("ApiMy/check_withdraw_result",{id:e,type:n},(function(t){r.loading=!1,1==t.status?(i&&setTimeout((function(){d.goto(i)}),1e3),d.success(t.msg),l(!0)):(d.error(t.msg),i&&setTimeout((function(){d.goto(i)}),1e3),l(!1))}))},fail:function(t){console.log("fail:",t),i&&setTimeout((function(){d.goto(i)}),1e3),l(!1)}}):(d.error("你的微信版本过低，请更新至最新版本。"),i&&setTimeout((function(){d.goto(i)}),1e3),l(!1));else if("mp"==d.globalData.platform){var g=a("6fcc");console.log(g),g.ready((function(){g.checkJsApi({jsApiList:["requestMerchantTransfer"],success:function(t){t.checkResult["requestMerchantTransfer"]?WeixinJSBridge.invoke("requestMerchantTransfer",{mchId:c.wxpay_mchid,appId:c.appid,package:s.wx_package_info},(function(t){"requestMerchantTransfer:ok"===t.err_msg?(r.loading=!0,d.post("ApiMy/check_withdraw_result",{id:e,type:n},(function(t){r.loading=!1,1==t.status?(i&&setTimeout((function(){d.goto(i)}),1e3),d.success(t.msg),l(!0)):(d.error(t.msg),i&&setTimeout((function(){d.goto(i)}),1e3),l(!1))}))):i&&d.goto(i)})):(alert("你的微信版本过低，请更新至最新版本。"),i&&setTimeout((function(){d.goto(i)}),1e3),l(!1))}})}))}}else d.error("请在提现发起端操作收款")}))):d.error("请在微信环境下操作")}))}}}),s.default.mpType="app";var d=new l.default(g(g({},s.default),{},{i18n:u}));o(d).$mount()}).call(this,a("3223")["default"],a("df3c")["default"],a("df3c")["createApp"])},a637:function(t,e,a){"use strict";a.r(e);var o=a("6a19");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("7979");var i=a("828b"),l=Object(i["a"])(o["default"],void 0,void 0,!1,null,null,null,!1,void 0,void 0);e["default"]=l.exports},a655:function(t,e,a){}},[["9258","common/runtime","common/vendor"]]]);