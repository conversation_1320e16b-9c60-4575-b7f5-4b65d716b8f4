<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Coze API演示</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
  <style>
    .chat-container {
      height: 400px;
      overflow-y: auto;
      border: 1px solid #e6e6e6;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    .chat-message {
      margin-bottom: 15px;
      display: flex;
    }
    .chat-message.user {
      flex-direction: row-reverse;
    }
    .chat-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      margin: 0 10px;
    }
    .chat-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .chat-content {
      max-width: 70%;
      padding: 10px 15px;
      border-radius: 4px;
      position: relative;
    }
    .chat-message.user .chat-content {
      background-color: #5FB878;
      color: #fff;
      border-radius: 4px 0 4px 4px;
    }
    .chat-message.bot .chat-content {
      background-color: #fff;
      color: #333;
      border: 1px solid #e6e6e6;
      border-radius: 0 4px 4px 4px;
    }
    .chat-time {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
    .chat-message.user .chat-time {
      text-align: right;
    }
    .bot-selector {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header"><i class="fa fa-robot"></i> Coze API演示</div>
          <div class="layui-card-body">
            <div class="layui-row">
              <div class="layui-col-md12">
                <div class="bot-selector">
                  <form class="layui-form" action="">
                    <div class="layui-form-item">
                      <label class="layui-form-label">当前机器人</label>
                      <div class="layui-input-block">
                        <input type="text" name="bot_name" id="bot-name" value="使用配置中的默认机器人" class="layui-input" readonly>
                        <input type="hidden" name="bot_id" id="bot-id" value="">
                        <div id="debug-info" style="margin-top: 10px; color: #999; font-size: 12px;"></div>
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <div class="layui-input-block">
                        <button type="button" class="layui-btn" id="create-conversation-btn">创建对话</button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
            
            <div class="layui-row">
              <div class="layui-col-md12">
                <div class="chat-container" id="chat-container">
                  <!-- 聊天消息将在这里显示 -->
                </div>
                <div class="layui-form">
                  <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                      <textarea name="message" placeholder="请输入消息内容" class="layui-textarea" id="message-input" disabled></textarea>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0; text-align: right;">
                      <button type="button" class="layui-btn" id="send-message-btn" disabled>发送消息</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header"><i class="fa fa-info-circle"></i> 接口响应信息</div>
          <div class="layui-card-body">
            <div class="layui-tab layui-tab-brief" lay-filter="response-tab">
              <ul class="layui-tab-title">
                <li class="layui-this">机器人列表</li>
                <li>对话信息</li>
                <li>消息记录</li>
                <li>工作流</li>
              </ul>
              <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                  <pre class="layui-code" lay-title="机器人列表响应" lay-skin="notepad" id="bots-response">// 点击"获取机器人列表"按钮查看响应</pre>
                </div>
                <div class="layui-tab-item">
                  <pre class="layui-code" lay-title="对话信息响应" lay-skin="notepad" id="conversation-response">// 点击"创建对话"按钮查看响应</pre>
                </div>
                <div class="layui-tab-item">
                  <pre class="layui-code" lay-title="消息记录响应" lay-skin="notepad" id="message-response">// 发送消息后查看响应</pre>
                </div>
                <div class="layui-tab-item">
                  <div class="layui-form">
                    <div class="layui-form-item">
                      <label class="layui-form-label">API Token</label>
                      <div class="layui-input-block">
                        <input type="text" name="api_token" id="api-token" placeholder="请输入 Coze API Token" class="layui-input" value="pat_WFZvXcAixTK2EStyRXGQzhLV3qSR5RzsKA4ycvKmvsVkXn4CAg9FGQoaXZpdHuDR">
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">工作流ID</label>
                      <div class="layui-input-block">
                        <input type="text" name="workflow_id" id="workflow-id" placeholder="请输入工作流ID" class="layui-input" value="7476690468139860006">
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">参数</label>
                      <div class="layui-input-block">
                        <textarea name="workflow_parameters" id="workflow-parameters" placeholder="请输入JSON格式的参数" class="layui-textarea">{"input": "https://p3-bot-sign.byteimg.com/tos-cn-i-v4nquku3lp/251f117e01fa4085b270bd3bd0029ca4.pdf~tplv-v4nquku3lp-image.image?rk3s=68e6b6b5&x-expires=1743396280&x-signature=kF4i7c%2BBoxhEdVEvxMzgJ7ALBzI%3D"}</textarea>
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <div class="layui-input-block">
                        <button type="button" class="layui-btn" id="run-workflow-btn">运行工作流</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="run-workflow-direct-btn">直接调用API</button>
                      </div>
                    </div>
                  </div>
                  <pre class="layui-code" lay-title="工作流响应" lay-skin="notepad" id="workflow-response">// 点击"运行工作流"按钮查看响应</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  {include file="public/js"/}
  <script>
  layui.use(['form', 'layer', 'code', 'element'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    var element = layui.element;
    
    // 渲染代码高亮
    layui.code({
      about: false
    });
    
    // 当前对话ID
    var currentConversationId = '';
    
    // 页面加载时获取默认Bot ID
    $(function(){
      // 获取默认Bot ID
      $('#debug-info').html('正在获取Bot信息...');
      
      $.ajax({
        url: '{:url("getBotInfo")}',
        type: 'GET',
        dataType: 'json',
        success: function(res){
          console.log('getBotInfo 响应:', res);
          $('#debug-info').html('getBotInfo 响应: ' + JSON.stringify(res));
          
          if(res.status === 1 && res.data){
            console.log('设置 bot-id 值:', res.data.bot_id);
            // 确保正确设置 bot-id 值
            document.getElementById('bot-id').value = res.data.bot_id;
            $('#debug-info').append('<br>设置 bot-id 值: ' + res.data.bot_id);
            
            if(res.data.bot_name){
              console.log('设置 bot-name 值:', res.data.bot_name);
              document.getElementById('bot-name').value = res.data.bot_name;
              $('#debug-info').append('<br>设置 bot-name 值: ' + res.data.bot_name);
            }
          } else {
            console.error('getBotInfo 响应状态不为1或无数据:', res);
            $('#debug-info').append('<br><span style="color:red">错误: getBotInfo 响应状态不为1或无数据</span>');
          }
        },
        error: function(xhr, status, error) {
          console.error('getBotInfo 请求失败:', status, error);
          $('#debug-info').html('getBotInfo 请求失败: ' + status + ' ' + error);
          if(xhr.responseText) {
            try {
              var response = JSON.parse(xhr.responseText);
              $('#debug-info').append('<br>响应内容: ' + JSON.stringify(response));
            } catch(e) {
              $('#debug-info').append('<br>响应内容(非JSON): ' + xhr.responseText);
            }
          }
        }
      });
    });
    
    // 创建对话
    $('#create-conversation-btn').click(function(){
      var botId = document.getElementById('bot-id').value;
      console.log('创建对话按钮点击, bot-id 值:', botId);
      $('#debug-info').html('创建对话按钮点击, bot-id 值: ' + botId);
      
      // 检查 bot-id 元素是否存在
      var botIdElement = document.getElementById('bot-id');
      if(!botIdElement) {
        console.error('bot-id 元素不存在');
        $('#debug-info').append('<br><span style="color:red">错误: bot-id 元素不存在</span>');
        layer.msg('页面元素错误，请刷新页面重试', {icon: 0});
        return;
      }
      
      if(!botId){
        console.error('bot-id 值为空');
        $('#debug-info').append('<br><span style="color:red">错误: bot-id 值为空</span>');
        
        // 尝试重新获取 bot-id
        $.ajax({
          url: '{:url("getBotInfo")}',
          type: 'GET',
          dataType: 'json',
          async: false,
          success: function(res){
            console.log('重新获取 getBotInfo 响应:', res);
            $('#debug-info').append('<br>重新获取 getBotInfo 响应: ' + JSON.stringify(res));
            
            if(res.status === 1 && res.data && res.data.bot_id){
              botId = res.data.bot_id;
              document.getElementById('bot-id').value = botId;
              $('#debug-info').append('<br>重新设置 bot-id 值: ' + botId);
            } else {
              layer.msg('未配置默认机器人ID，请先在配置页面设置', {icon: 0});
              return;
            }
          },
          error: function(){
            layer.msg('未配置默认机器人ID，请先在配置页面设置', {icon: 0});
            return;
          }
        });
        
        if(!botId){
          layer.msg('未配置默认机器人ID，请先在配置页面设置', {icon: 0});
          return;
        }
      }
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      // 生成随机用户ID
      var randomUserId = "user_" + new Date().getTime() + "_" + Math.floor(Math.random() * 9000 + 1000);
      
      // 确保 bot_id 是字符串类型
      botId = String(botId).trim();
      
      // 显示请求参数
      var requestData = {
        bot_id: botId,
        user_id: randomUserId
      };
      
      // 添加可选参数
      requestData.stream = false;
      requestData.auto_save_history = true;
      requestData.additional_messages = [];
      
      $('#debug-info').append('<br>发送请求参数: ' + JSON.stringify(requestData));
      console.log('发送请求参数:', requestData);
      
      // 检查 bot_id 格式
      $('#debug-info').append('<br>bot_id 类型: ' + typeof botId);
      $('#debug-info').append('<br>bot_id 长度: ' + botId.length);
      $('#debug-info').append('<br>bot_id 是否为数字: ' + (!isNaN(botId)));
      
      $.ajax({
        url: '{:url("createConversation")}',
        type: 'POST',
        dataType: 'json',
        contentType: 'application/x-www-form-urlencoded',
        data: requestData,
        complete: function(xhr, status) {
          $('#debug-info').append('<br>请求完成状态: ' + status);
          $('#debug-info').append('<br>请求头信息: Content-Type=' + xhr.getResponseHeader('Content-Type'));
          console.log('完整的 XHR 对象:', xhr);
        },
        success: function(res){
          layer.close(loading);
          console.log('创建对话响应:', res);
          $('#debug-info').append('<br>创建对话响应: ' + JSON.stringify(res));
          
          // 更新响应信息
          $('#conversation-response').html(JSON.stringify(res, null, 2));
          layui.code({
            elem: '#conversation-response',
            about: false
          });
          
          if(res.status === 1){
            // 保存对话ID
            currentConversationId = res.data.conversation_id;
            $('#debug-info').append('<br>对话ID: ' + currentConversationId);
            
            // 启用消息输入
            $('#message-input').removeAttr('disabled');
            $('#send-message-btn').removeAttr('disabled');
            
            // 清空聊天容器
            $('#chat-container').empty();
            
            // 添加系统消息
            addSystemMessage('对话已创建，您可以开始发送消息了');
            
            layer.msg('对话创建成功', {icon: 1});
          } else {
            $('#debug-info').append('<br><span style="color:red">错误: ' + (res.msg || '创建失败') + '</span>');
            layer.msg(res.msg || '创建失败', {icon: 2});
          }
        },
        error: function(xhr, status, error){
          layer.close(loading);
          console.error('创建对话请求失败:', status, error);
          $('#debug-info').append('<br><span style="color:red">请求失败: ' + status + ' ' + error + '</span>');
          
          if(xhr.responseText) {
            try {
              var response = JSON.parse(xhr.responseText);
              $('#debug-info').append('<br>响应内容: ' + JSON.stringify(response));
              $('#conversation-response').html(JSON.stringify(response, null, 2));
              layui.code({
                elem: '#conversation-response',
                about: false
              });
            } catch(e) {
              $('#debug-info').append('<br>响应内容(非JSON): ' + xhr.responseText);
              $('#conversation-response').html(xhr.responseText);
              layui.code({
                elem: '#conversation-response',
                about: false
              });
            }
          }
          
          layer.msg('网络错误，请重试', {icon: 2});
        }
      });
    });
    
    // 发送消息
    $('#send-message-btn').click(function(){
      var message = $('#message-input').val().trim();
      if(!message){
        layer.msg('请输入消息内容', {icon: 0});
        return;
      }
      
      if(!currentConversationId){
        layer.msg('请先创建对话', {icon: 0});
        return;
      }
      
      // 添加用户消息
      addUserMessage(message);
      
      // 清空输入框
      $('#message-input').val('');
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      // 构建请求数据
      var requestData = {
        conversation_id: currentConversationId,
        message: message,
        stream: false,
        additional_messages: [
          {
            role: 'user',
            content: message,
            content_type: 'text'
          }
        ]
      };
      
      $('#debug-info').html('发送消息请求参数: ' + JSON.stringify(requestData));
      
      $.ajax({
        url: '{:url("sendMessage")}',
        type: 'POST',
        dataType: 'json',
        data: requestData,
        success: function(res){
          layer.close(loading);
          if(res.status === 1){
            // 更新响应信息
            $('#message-response').html(JSON.stringify(res, null, 2));
            layui.code({
              elem: '#message-response',
              about: false
            });
            
            // 添加机器人回复
            if(res.data && res.data.content){
              addBotMessage(res.data.content);
            } else {
              addSystemMessage('机器人没有返回有效回复');
            }
          } else {
            layer.msg(res.msg || '发送失败', {icon: 2});
            addSystemMessage('消息发送失败: ' + (res.msg || '未知错误'));
          }
        },
        error: function(){
          layer.close(loading);
          layer.msg('网络错误，请重试', {icon: 2});
          addSystemMessage('网络错误，请重试');
        }
      });
    });
    
    // 添加用户消息
    function addUserMessage(content){
      var time = new Date().toLocaleTimeString();
      var html = '<div class="chat-message user">' +
                 '<div class="chat-content">' + content + '<div class="chat-time">' + time + '</div></div>' +
                 '<div class="chat-avatar"><img src="__STATIC__/admin/images/avatar.jpg" alt="用户"></div>' +
                 '</div>';
      $('#chat-container').append(html);
      scrollToBottom();
    }
    
    // 添加机器人消息
    function addBotMessage(content){
      var time = new Date().toLocaleTimeString();
      var html = '<div class="chat-message bot">' +
                 '<div class="chat-avatar"><img src="__STATIC__/admin/images/bot-avatar.png" alt="机器人"></div>' +
                 '<div class="chat-content">' + content + '<div class="chat-time">' + time + '</div></div>' +
                 '</div>';
      $('#chat-container').append(html);
      scrollToBottom();
    }
    
    // 添加系统消息
    function addSystemMessage(content){
      var time = new Date().toLocaleTimeString();
      var html = '<div class="chat-message system" style="justify-content: center;">' +
                 '<div class="chat-content" style="background-color: #f2f2f2; color: #666; text-align: center; max-width: 90%;">' + 
                 content + '<div class="chat-time" style="text-align: center;">' + time + '</div></div>' +
                 '</div>';
      $('#chat-container').append(html);
      scrollToBottom();
    }
    
    // 滚动到底部
    function scrollToBottom(){
      var chatContainer = document.getElementById('chat-container');
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // 按Enter键发送消息
    $('#message-input').keydown(function(e){
      if(e.keyCode === 13 && !e.shiftKey){
        e.preventDefault();
        $('#send-message-btn').click();
      }
    });
    
    // 运行工作流
    $('#run-workflow-btn').click(function(){
      var workflowId = $('#workflow-id').val().trim();
      var parametersStr = $('#workflow-parameters').val().trim();
      
      if(!workflowId){
        layer.msg('请输入工作流ID', {icon: 0});
        return;
      }
      
      var parameters = {};
      try {
        if(parametersStr) {
          parameters = JSON.parse(parametersStr);
        }
      } catch(e) {
        layer.msg('参数格式错误，请输入有效的JSON格式', {icon: 2});
        return;
      }
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      // 构建请求数据
      var requestData = {
        workflow_id: workflowId,
        parameters: parameters,
        stream: false
      };
      
      $('#debug-info').html('运行工作流请求参数: ' + JSON.stringify(requestData));
      
      $.ajax({
        url: '{:url("runWorkflowDemo")}',
        type: 'POST',
        dataType: 'json',
        data: requestData,
        success: function(res){
          layer.close(loading);
          console.log('运行工作流响应:', res);
          
          // 更新响应信息
          $('#workflow-response').html(JSON.stringify(res, null, 2));
          layui.code({
            elem: '#workflow-response',
            about: false
          });
          
          if(res.status === 1){
            layer.msg('工作流运行成功', {icon: 1});
            
            // 如果有运行ID，可以轮询获取状态
            if(res.data && res.data.run_id){
              var runId = res.data.run_id;
              $('#debug-info').append('<br>工作流运行ID: ' + runId);
              
              // 定时获取工作流运行状态
              var statusTimer = setInterval(function(){
                $.ajax({
                  url: '{:url("getWorkflowRunStatus")}',
                  type: 'GET',
                  dataType: 'json',
                  data: {
                    run_id: runId
                  },
                  success: function(statusRes){
                    console.log('工作流状态响应:', statusRes);
                    
                    // 更新响应信息
                    $('#workflow-response').html(JSON.stringify(statusRes, null, 2));
                    layui.code({
                      elem: '#workflow-response',
                      about: false
                    });
                    
                    // 如果工作流已完成或失败，停止轮询
                    if(statusRes.data && (statusRes.data.status === 'completed' || statusRes.data.status === 'failed')){
                      clearInterval(statusTimer);
                      $('#debug-info').append('<br>工作流运行' + (statusRes.data.status === 'completed' ? '完成' : '失败'));
                    }
                  },
                  error: function(){
                    console.error('获取工作流状态失败');
                    clearInterval(statusTimer);
                  }
                });
              }, 5000); // 每5秒查询一次
            }
          } else {
            layer.msg(res.msg || '运行失败', {icon: 2});
          }
        },
        error: function(){
          layer.close(loading);
          layer.msg('网络错误，请重试', {icon: 2});
        }
      });
    });
    
    // 直接调用 Coze API
    $('#run-workflow-direct-btn').click(function(){
      var apiToken = $('#api-token').val().trim();
      var workflowId = $('#workflow-id').val().trim();
      var parametersStr = $('#workflow-parameters').val().trim();
      
      if(!apiToken){
        layer.msg('请输入 API Token', {icon: 0});
        return;
      }
      
      if(!workflowId){
        layer.msg('请输入工作流ID', {icon: 0});
        return;
      }
      
      var parameters = {};
      try {
        if(parametersStr) {
          parameters = JSON.parse(parametersStr);
        }
      } catch(e) {
        layer.msg('参数格式错误，请输入有效的JSON格式', {icon: 2});
        $('#debug-info').html('<span style="color:red">参数解析错误: ' + e.message + '</span>');
        return;
      }
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      // 构建请求数据
      var requestData = {
        workflow_id: workflowId,
        parameters: parameters,
        stream: false
      };
      
      $('#debug-info').html('直接调用 Coze API 请求参数: ' + JSON.stringify(requestData));
      
      // 使用本地代理解决跨域问题
      $.ajax({
        url: '{:url("proxyRequest")}',
        type: 'POST',
        dataType: 'json',
        data: {
          api_token: apiToken,
          endpoint: 'workflow/run',
          method: 'POST',
          request_data: JSON.stringify(requestData)
        },
        success: function(res){
          layer.close(loading);
          console.log('直接调用 Coze API 响应:', res);
          
          // 更新响应信息
          $('#workflow-response').html(JSON.stringify(res, null, 2));
          layui.code({
            elem: '#workflow-response',
            about: false
          });
          
          if(res.data && res.data.run_id){
            layer.msg('工作流运行成功', {icon: 1});
            $('#debug-info').append('<br>工作流运行ID: ' + res.data.run_id);
            
            // 定时获取工作流运行状态
            var statusTimer = setInterval(function(){
              $.ajax({
                url: '{:url("proxyRequest")}',
                type: 'POST',
                dataType: 'json',
                data: {
                  api_token: apiToken,
                  endpoint: 'workflow/run/' + res.data.run_id,
                  method: 'GET',
                  request_data: '{}'
                },
                success: function(statusRes){
                  console.log('工作流状态响应:', statusRes);
                  
                  // 更新响应信息
                  $('#workflow-response').html(JSON.stringify(statusRes, null, 2));
                  layui.code({
                    elem: '#workflow-response',
                    about: false
                  });
                  
                  // 如果工作流已完成或失败，停止轮询
                  if(statusRes.data && (statusRes.data.status === 'completed' || statusRes.data.status === 'failed')){
                    clearInterval(statusTimer);
                    $('#debug-info').append('<br>工作流运行' + (statusRes.data.status === 'completed' ? '完成' : '失败'));
                  }
                },
                error: function(xhr, status, error){
                  console.error('获取工作流状态失败:', status, error);
                  clearInterval(statusTimer);
                  
                  if(xhr.responseText) {
                    try {
                      var response = JSON.parse(xhr.responseText);
                      $('#debug-info').append('<br>状态查询错误: ' + JSON.stringify(response));
                    } catch(e) {
                      $('#debug-info').append('<br>状态查询错误(非JSON): ' + xhr.responseText);
                    }
                  }
                }
              });
            }, 5000); // 每5秒查询一次
          } else {
            var errorMsg = '';
            if(res.data && (res.data.error || res.data.message)) {
              errorMsg = res.data.error || res.data.message;
            } else {
              errorMsg = res.error || res.message || '未知错误';
            }
            layer.msg('运行失败: ' + errorMsg, {icon: 2});
            $('#debug-info').append('<br><span style="color:red">错误: ' + errorMsg + '</span>');
            
            // 显示完整的响应数据，帮助调试
            $('#debug-info').append('<br><br>完整响应数据:<br>' + JSON.stringify(res, null, 2));
          }
        },
        error: function(xhr, status, error){
          layer.close(loading);
          console.error('直接调用 Coze API 失败:', status, error);
          $('#debug-info').append('<br><span style="color:red">请求失败: ' + status + ' ' + error + '</span>');
          
          if(xhr.responseText) {
            try {
              var response = JSON.parse(xhr.responseText);
              $('#debug-info').append('<br>响应内容: ' + JSON.stringify(response));
              $('#workflow-response').html(JSON.stringify(response, null, 2));
              layui.code({
                elem: '#workflow-response',
                about: false
              });
            } catch(e) {
              $('#debug-info').append('<br>响应内容(非JSON): ' + xhr.responseText);
              $('#workflow-response').html(xhr.responseText);
              layui.code({
                elem: '#workflow-response',
                about: false
              });
            }
          }
          
          layer.msg('网络错误，请重试', {icon: 2});
        }
      });
    });
  });
  </script>
  {include file="public/copyright"/}
</body>
</html>