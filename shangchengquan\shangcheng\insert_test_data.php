<?php
/**
 * 插入扣子工作流测试数据
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use think\facade\Db;

echo "开始插入扣子工作流测试数据...\n";

try {
    // 设置aid为1（通常是默认的应用ID）
    $aid = 1;
    
    // 检查是否已存在数据
    $existingCount = Db::name('coze_workflow')->where('aid', $aid)->count();
    
    if ($existingCount > 0) {
        echo "已存在 {$existingCount} 条工作流数据，先清空再插入新数据...\n";
        Db::name('coze_workflow')->where('aid', $aid)->delete();
    }
    
    // 插入测试工作流数据
    $workflowData = [
        [
            'aid' => $aid,
            'name' => '文档分析工作流',
            'workflow_id' => '7476690468139860006',
            'description' => '用于分析PDF文档内容的工作流',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ],
        [
            'aid' => $aid,
            'name' => '图片处理工作流',
            'workflow_id' => '7476690468139860007',
            'description' => '用于处理和分析图片的工作流',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ],
        [
            'aid' => $aid,
            'name' => '文本生成工作流',
            'workflow_id' => '7476690468139860008',
            'description' => '用于生成文本内容的工作流',
            'status' => 0,
            'create_time' => time(),
            'update_time' => time()
        ]
    ];
    
    foreach ($workflowData as $data) {
        $result = Db::name('coze_workflow')->insert($data);
        if ($result) {
            echo "✅ 成功插入工作流: {$data['name']}\n";
        } else {
            echo "❌ 插入工作流失败: {$data['name']}\n";
        }
    }
    
    // 插入API配置数据
    $existingConfigCount = Db::name('coze_config')->where('aid', $aid)->count();
    
    if ($existingConfigCount > 0) {
        echo "已存在 {$existingConfigCount} 条API配置数据，先清空再插入新数据...\n";
        Db::name('coze_config')->where('aid', $aid)->delete();
    }
    
    $configData = [
        'aid' => $aid,
        'name' => '默认配置',
        'api_key' => 'pat_WFZvXcAixTK2EStyRXGQzhLV3qSR5RzsKA4ycvKmvsVkXn4CAg9FGQoaXZpdHuDR',
        'bot_id' => '7476690468139860006',
        'workflow_id' => '7476690468139860006',
        'base_url' => 'https://api.coze.cn',
        'api_version' => 'v1',
        'timeout' => 30,
        'max_retries' => 3,
        'remark' => '测试配置，用于开发和调试',
        'status' => 1,
        'create_time' => time(),
        'update_time' => time()
    ];
    
    $result = Db::name('coze_config')->insert($configData);
    if ($result) {
        echo "✅ 成功插入API配置: {$configData['name']}\n";
    } else {
        echo "❌ 插入API配置失败\n";
    }
    
    // 验证插入结果
    echo "\n=== 验证插入结果 ===\n";
    
    $workflows = Db::name('coze_workflow')->where('aid', $aid)->select()->toArray();
    echo "工作流数据 (aid={$aid}):\n";
    foreach ($workflows as $workflow) {
        $statusText = $workflow['status'] ? '启用' : '禁用';
        echo "  - ID: {$workflow['id']}, 名称: {$workflow['name']}, 状态: {$statusText}\n";
    }
    
    $configs = Db::name('coze_config')->where('aid', $aid)->select()->toArray();
    echo "\nAPI配置数据 (aid={$aid}):\n";
    foreach ($configs as $config) {
        $statusText = $config['status'] ? '启用' : '禁用';
        echo "  - ID: {$config['id']}, 名称: {$config['name']}, 状态: {$statusText}\n";
    }
    
    echo "\n✅ 测试数据插入完成！\n";
    echo "现在可以访问后台扣子管理页面查看数据了。\n";
    
} catch (Exception $e) {
    echo "❌ 插入测试数据失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?>
