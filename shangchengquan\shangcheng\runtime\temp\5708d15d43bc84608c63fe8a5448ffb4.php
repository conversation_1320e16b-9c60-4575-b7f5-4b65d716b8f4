<?php /*a:4:{s:74:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\config.html";i:1753861194;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Coze API配置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-cog"></i> Coze API配置</div>
          <div class="layui-card-body" pad15>
            <form class="layui-form" action="" lay-filter="coze-config-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">API密钥</label>
                    <div class="layui-input-block">
                        <input type="text" name="api_key" placeholder="请输入Coze API密钥" autocomplete="off" class="layui-input" value="<?php echo (isset($config['api_key']) && ($config['api_key'] !== '')?$config['api_key']:''); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">Bot ID</label>
                    <div class="layui-input-block">
                        <input type="text" name="bot_id" placeholder="请输入默认使用的Bot ID" autocomplete="off" class="layui-input" value="<?php echo (isset($config['bot_id']) && ($config['bot_id'] !== '')?$config['bot_id']:''); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">API基础URL</label>
                    <div class="layui-input-block">
                        <input type="text" name="base_url" placeholder="请输入API基础URL" autocomplete="off" class="layui-input" value="<?php echo (isset($config['base_url']) && ($config['base_url'] !== '')?$config['base_url']:'https://api.coze.cn'); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">API版本</label>
                    <div class="layui-input-block">
                        <select name="api_version" lay-verify="">
                            <option value="v1" <?php if($config['api_version'] == 'v1'): ?>selected<?php endif; ?>>v1</option>
                            <option value="v3" <?php if($config['api_version'] == 'v3' || !$config['api_version']): ?>selected<?php endif; ?>>v3</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="启用" <?php if($config['status'] == 1 || !$config['status']): ?>checked<?php endif; ?>>
                        <input type="radio" name="status" value="0" title="禁用" <?php if($config['status'] == 0): ?>checked<?php endif; ?>>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">API说明</label>
                    <div class="layui-input-block">
                        <textarea placeholder="API使用说明" class="layui-textarea" readonly>
1. 访问 https://www.coze.cn 注册并登录账号
2. 创建机器人并获取API密钥
3. 将API密钥填入上方输入框并保存
4. 获取您要使用的机器人ID并填入Bot ID输入框
5. 通过API接口调用Coze机器人服务
                        </textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="coze-config-submit">保存配置</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" id="test-api">测试连接</button>
                    </div>
                </div>
            </form>
          </div>
        </div>
        
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> API接口说明</div>
          <div class="layui-card-body" pad15>
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>接口名称</th>
                        <th>接口地址</th>
                        <th>请求方式</th>
                        <th>参数说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>获取机器人列表</td>
                        <td>/apicoze/getBots</td>
                        <td>GET</td>
                        <td>无需参数</td>
                    </tr>
                    <tr>
                        <td>获取机器人详情</td>
                        <td>/apicoze/getBotDetail</td>
                        <td>GET</td>
                        <td>bot_id: 机器人ID</td>
                    </tr>
                    <tr>
                        <td>创建对话</td>
                        <td>/apicoze/createConversation</td>
                        <td>POST</td>
                        <td>bot_id: 机器人ID</td>
                    </tr>
                    <tr>
                        <td>发送消息</td>
                        <td>/apicoze/sendMessage</td>
                        <td>POST</td>
                        <td>conversation_id: 对话ID<br>message: 消息内容</td>
                    </tr>
                    <tr>
                        <td>获取对话历史</td>
                        <td>/apicoze/getConversationHistory</td>
                        <td>GET</td>
                        <td>conversation_id: 对话ID<br>limit: 返回条数(可选，默认20)<br>before: 分页标记(可选)</td>
                    </tr>
                    <tr>
                        <td>获取用户对话列表</td>
                        <td>/apicoze/getUserConversations</td>
                        <td>GET</td>
                        <td>limit: 返回条数(可选，默认20)<br>before: 分页标记(可选)</td>
                    </tr>
                </tbody>
            </table>
          </div>
        </div>
        
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-code"></i> API调用示例</div>
          <div class="layui-card-body" pad15>
            <pre class="layui-code" lay-title="JavaScript示例" lay-skin="notepad">
// 获取机器人列表
$.ajax({
    url: '<?php echo url("apicoze/getBots"); ?>',
    type: 'GET',
    data: {
        aid: "<?php echo ''; ?>" // 管理员ID
    },
    success: function(res) {
        if(res.status === 1) {
            console.log('机器人列表:', res.data);
        } else {
            console.error('获取失败:', res.msg);
        }
    }
});

// 创建对话
$.ajax({
    url: '<?php echo url("apicoze/createConversation"); ?>',
    type: 'POST',
    data: {
        aid: "<?php echo ''; ?>", // 管理员ID
        bot_id: 'bot_123456' // 机器人ID
    },
    success: function(res) {
        if(res.status === 1) {
            console.log('对话创建成功:', res.data);
            // 保存conversation_id用于后续发送消息
            var conversationId = res.data.conversation_id;
        } else {
            console.error('创建失败:', res.msg);
        }
    }
});

// 发送消息
$.ajax({
    url: '<?php echo url("apicoze/sendMessage"); ?>',
    type: 'POST',
    data: {
        aid: "<?php echo ''; ?>", // 管理员ID
        conversation_id: 'conv_123456', // 对话ID
        message: '你好，请问你是谁？' // 消息内容
    },
    success: function(res) {
        if(res.status === 1) {
            console.log('消息发送成功:', res.data);
            // 显示机器人回复
            var reply = res.data.content;
        } else {
            console.error('发送失败:', res.msg);
        }
    }
});
            </pre>
          </div>
        </div>
    </div>
  </div>
  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  <script>
  layui.use(['form', 'layer', 'code'], function(){
      var form = layui.form;
      var layer = layui.layer;
      var $ = layui.jquery;
      
      // 渲染代码高亮
      layui.code({
          about: false
      });
      
      // 监听提交
      form.on('submit(coze-config-submit)', function(data){
          var loading = layer.load(1, {shade: [0.1,'#fff']});
          $.ajax({
              url: '<?php echo url("coze/saveConfig"); ?>',
              type: 'POST',
              data: data.field,
              success: function(res){
                  layer.close(loading);
                  if(res.code == 1){
                      layer.msg('配置保存成功', {icon: 1});
                  }else{
                      layer.msg(res.msg || '保存失败', {icon: 2});
                  }
              },
              error: function(){
                  layer.close(loading);
                  layer.msg('网络错误，请重试', {icon: 2});
              }
          });
          return false;
      });

      // 测试API连接
      $('#test-api').click(function(){
          var apiKey = $('input[name="api_key"]').val();
          if(!apiKey){
              layer.msg('请先输入API密钥', {icon: 2});
              return;
          }

          var loading = layer.load(1, {shade: [0.1,'#fff']});
          $.ajax({
              url: '<?php echo url("coze/testConnection"); ?>',
              type: 'POST',
              data: {
                  api_key: apiKey,
                  base_url: $('input[name="base_url"]').val(),
                  api_version: $('select[name="api_version"]').val()
              },
              success: function(res){
                  layer.close(loading);
                  if(res.code == 1){
                      layer.msg('连接测试成功', {icon: 1});
                  }else{
                      layer.msg(res.msg || '连接测试失败', {icon: 2});
                  }
              },
              error: function(){
                  layer.close(loading);
                  layer.msg('网络错误，请重试', {icon: 2});
              }
          });
      });

      // 测试API连接
      $('#test-api').click(function(){
          var apiKey = $('input[name="api_key"]').val();
          if(!apiKey){
              layer.msg('请先输入API密钥', {icon: 2});
              return;
          }

          var loading = layer.load(1, {shade: [0.1,'#fff']});
          $.ajax({
              url: '<?php echo url("coze/testConnection"); ?>',
              type: 'POST',
              data: {
                  api_key: apiKey,
                  base_url: $('input[name="base_url"]').val(),
                  api_version: $('select[name="api_version"]').val()
              },
              success: function(res){
                  layer.close(loading);
                  if(res.code == 1){
                      layer.msg('连接测试成功', {icon: 1});
                  }else{
                      layer.msg(res.msg || '连接测试失败', {icon: 2});
                  }
              },
              error: function(){
                  layer.close(loading);
                  layer.msg('网络错误，请重试', {icon: 2});
              }
          });
      });
  });
  </script>
  
</body>
</html> 