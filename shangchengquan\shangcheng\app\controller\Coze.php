<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 扣子管理 扣子配置
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
use think\facade\View;

class Coze extends Common
{
    //列表
    public function index(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            $where = array();
            $where[] = ['aid','=',aid];
            
            if(input('param.name')) $where[] = ['name','like','%'.input('param.name').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];
            
            $count = 0 + Db::name('coze_config')->where($where)->count();
            $data = Db::name('coze_config')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '启用' : '禁用';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
                $data[$k]['update_time_text'] = $v['update_time'] ? date('Y-m-d H:i:s', $v['update_time']) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
    
    //编辑配置
    public function edit(){
        $type = input('type', '');
        if($type == 'workflow'){
            // 工作流编辑
            if(input('param.id')){
                $info = Db::name('coze_workflow')->where('aid',aid)->where('id',input('param.id/d'))->find();
                if(!$info){
                    $this->error('工作流不存在');
                }
            }else{
                $info = ['id'=>'','name'=>'','workflow_id'=>'','description'=>'','status'=>1,'create_time'=>time()];
            }
        }else{
            // 配置编辑
            if(input('param.id')){
                $info = Db::name('coze_config')->where('aid',aid)->where('id',input('param.id/d'))->find();
                if(!$info) return json(['status'=>0,'msg'=>'配置不存在']);
            }else{
                $info = array();
            }
        }
        View::assign('info',$info);
        View::assign('type',$type);
        return View::fetch();
    }
    
    //保存配置
    public function save(){
        $info = input('post.info');
        $type = input('post.type', '');

        if(!$info){
            return json(['status'=>0,'msg'=>'参数错误']);
        }

        if($type == 'workflow'){
            // 工作流保存
            if(empty($info['name'])){
                return json(['status'=>0,'msg'=>'工作流名称不能为空']);
            }
            if(empty($info['workflow_id'])){
                return json(['status'=>0,'msg'=>'工作流ID不能为空']);
            }

            $info['aid'] = aid;
            $info['update_time'] = time();

            if($info['id']){
                // 更新工作流
                $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$info['id'])->update($info);
                if($result !== false){
                    \app\common\System::plog('编辑工作流');
                    return json(['status'=>1,'msg'=>'更新成功','url'=>(string)url('workflow')]);
                }else{
                    return json(['status'=>0,'msg'=>'更新失败']);
                }
            }else{
                // 新增工作流
                $info['create_time'] = time();
                $result = Db::name('coze_workflow')->insert($info);
                if($result){
                    \app\common\System::plog('添加工作流');
                    return json(['status'=>1,'msg'=>'添加成功','url'=>(string)url('workflow')]);
                }else{
                    return json(['status'=>0,'msg'=>'添加失败']);
                }
            }
        }else{
            // 配置保存
            if(empty($info['api_key'])){
                return json(['status'=>0,'msg'=>'API密钥不能为空']);
            }

            if(empty($info['base_url'])){
                $info['base_url'] = 'https://api.coze.cn';
            }

            $info['aid'] = aid;
            $info['update_time'] = time();

            if($info['id']){
                // 更新配置
                $result = Db::name('coze_config')->where('aid',aid)->where('id',$info['id'])->update($info);
                if($result !== false){
                    \app\common\System::plog('编辑Coze配置');
                    return json(['status'=>1,'msg'=>'更新成功','url'=>(string)url('index')]);
                }else{
                    return json(['status'=>0,'msg'=>'更新失败']);
                }
            }else{
                // 新增配置
                $info['create_time'] = time();
                $result = Db::name('coze_config')->insert($info);
                if($result){
                    \app\common\System::plog('添加Coze配置');
                    return json(['status'=>1,'msg'=>'添加成功','url'=>(string)url('index')]);
                }else{
                    return json(['status'=>0,'msg'=>'添加失败']);
                }
            }
        }
    }
    
    //删除配置
    public function del(){
        $ids = input('post.ids');
        $type = input('post.type', '');

        // 如果没有传递ids，尝试获取id参数
        if(!$ids){
            $id = input('post.id');
            if(strpos($id, ',') !== false){
                $ids = explode(',', $id);
            } else {
                $ids = [$id];
            }
        }

        if($type == 'workflow'){
            // 删除工作流
            Db::name('coze_workflow')->where('aid',aid)->where('id','in',$ids)->delete();
            \app\common\System::plog('删除工作流');
        }else{
            // 删除配置
            Db::name('coze_config')->where('aid',aid)->where('id','in',$ids)->delete();
            \app\common\System::plog('删除扣子API配置');
        }

        return json(['status'=>1,'msg'=>'删除成功']);
    }

    //设置状态
    public function setst(){
        $ids = input('post.ids');
        $status = input('post.status/d');
        $type = input('post.type', '');

        // 如果没有传递ids，尝试获取id参数
        if(!$ids){
            $id = input('post.id');
            if(strpos($id, ',') !== false){
                $ids = explode(',', $id);
            } else {
                $ids = [$id];
            }
        }

        if($type == 'workflow'){
            // 设置工作流状态
            Db::name('coze_workflow')->where('aid',aid)->where('id','in',$ids)->update(['status'=>$status]);
            \app\common\System::plog('设置工作流状态');
        }else{
            // 设置配置状态
            Db::name('coze_config')->where('aid',aid)->where('id','in',$ids)->update(['status'=>$status]);
            \app\common\System::plog('设置扣子API配置状态');
        }

        return json(['status'=>1,'msg'=>'操作成功']);
    }
    
    //测试API连接
    public function test(){
        $id = input('post.id/d');
        $config = Db::name('coze_config')->where('aid',aid)->where('id',$id)->find();
        
        if(!$config){
            return json(['status'=>0,'msg'=>'配置不存在']);
        }
        
        if(empty($config['api_key'])){
            return json(['status'=>0,'msg'=>'API密钥不能为空']);
        }
        
        // 构建测试请求URL
        $baseUrl = $config['base_url'] ?: 'https://api.coze.cn';
        $apiVersion = $config['api_version'] ?: 'v1';
        $url = rtrim($baseUrl, '/') . '/' . $apiVersion . '/bots';
        
        // 发送测试请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $config['api_key'],
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return json(['status' => 0, 'msg' => '连接失败: ' . $error]);
        }
        
        if ($httpCode === 200) {
            \app\common\System::plog('测试扣子API连接成功');
            return json(['status' => 1, 'msg' => 'API连接测试成功']);
        } else {
            $responseData = json_decode($response, true);
            $errorMsg = $responseData['msg'] ?? '连接失败，HTTP状态码: ' . $httpCode;
            return json(['status' => 0, 'msg' => $errorMsg]);
        }
    }
    
    //日志列表
    public function logs(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'id desc';
            $where = array();
            $where[] = ['aid','=',aid];
            
            if(input('param.endpoint')) $where[] = ['endpoint','like','%'.input('param.endpoint').'%'];
            if(input('param.status')) $where[] = ['status','=',input('param.status')];
            
            $count = 0 + Db::name('coze_api_log')->where($where)->count();
            $data = Db::name('coze_api_log')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '成功' : '失败';
                $data[$k]['request_time_text'] = date('Y-m-d H:i:s', strtotime($v['request_time']));
                $data[$k]['response_time_text'] = $v['response_time'] ? date('Y-m-d H:i:s', strtotime($v['response_time'])) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
    
    //查看日志详情
    public function logdetail(){
        $id = input('param.id/d');
        $log = Db::name('coze_api_log')->where('aid',aid)->where('id',$id)->find();
        if(!$log) return json(['status'=>0,'msg'=>'日志不存在']);
        
        View::assign('log',$log);
        return View::fetch();
    }
    
    //清理日志
    public function clearlogs(){
        $days = input('post.days/d', 30);
        $time = time() - ($days * 24 * 3600);
        $count = Db::name('coze_api_log')->where('aid',aid)->where('request_time','<',date('Y-m-d H:i:s', $time))->delete();
        \app\common\System::plog('清理扣子API日志，清理了'.$count.'条记录');
        return json(['status'=>1,'msg'=>'清理成功，共清理'.$count.'条记录']);
    }

    //配置页面
    public function config(){
        if(request()->isPost()){
            $data = input('post.');

            // 验证必填字段
            if (empty($data['api_key'])) {
                return json(['status' => 0, 'msg' => 'API密钥不能为空']);
            }

            // 查找是否已有配置
            $config = Db::name('coze_config')->where('aid',aid)->find();

            if($config){
                // 更新配置
                $data['update_time'] = time();
                Db::name('coze_config')->where('aid',aid)->where('id',$config['id'])->update($data);
                \app\common\System::plog('更新扣子API配置');
            }else{
                // 新增配置
                $data['aid'] = aid;
                $data['name'] = '默认配置';
                $data['create_time'] = time();
                $data['update_time'] = time();
                Db::name('coze_config')->insert($data);
                \app\common\System::plog('新增扣子API配置');
            }

            return json(['status'=>1,'msg'=>'配置保存成功']);
        }

        // 获取配置信息
        $config = Db::name('coze_config')->where('aid',aid)->find();
        if(!$config){
            $config = [
                'api_key' => '',
                'bot_id' => '',
                'base_url' => 'https://api.coze.cn',
                'api_version' => 'v3',
                'status' => 1
            ];
        }

        View::assign('config',$config);
        return View::fetch();
    }

    //保存配置（专门用于config页面的AJAX提交）
    public function saveConfig(){
        $postData = input('post.');

        // 调试信息
        var_dump('=== saveConfig调试信息 ===');
        var_dump('POST数据:', $postData);

        // 验证必填字段
        if (empty($postData['api_key'])) {
            var_dump('错误: API密钥为空');
            return json(['code' => 0, 'msg' => 'API密钥不能为空']);
        }

        // 过滤允许的字段
        $allowFields = ['api_key', 'bot_id', 'base_url', 'api_version', 'status'];
        $data = [];
        foreach($allowFields as $field){
            if(isset($postData[$field])){
                $data[$field] = $postData[$field];
                var_dump('添加字段:', $field, '值:', $postData[$field]);
            }
        }

        var_dump('过滤后的数据:', $data);

        // 查找是否已有配置
        $config = Db::name('coze_config')->where('aid',aid)->find();
        var_dump('现有配置:', $config);

        try {
            if($config){
                // 更新配置
                var_dump('执行更新操作');
                $data['update_time'] = time();
                $result = Db::name('coze_config')->where('aid',aid)->where('id',$config['id'])->update($data);
                var_dump('更新结果:', $result);
                \app\common\System::plog('更新扣子API配置');
            }else{
                // 新增配置
                var_dump('执行新增操作');
                $data['aid'] = aid;
                $data['name'] = '默认配置';
                $data['create_time'] = time();
                $data['update_time'] = time();
                var_dump('新增数据:', $data);
                $result = Db::name('coze_config')->insert($data);
                var_dump('新增结果:', $result);
                \app\common\System::plog('新增扣子API配置');
            }
        } catch (\Exception $e) {
            var_dump('数据库操作失败:', $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }

        var_dump('saveConfig操作成功');
        return json(['code'=>1,'msg'=>'配置保存成功']);
    }

    //测试连接（专门用于config页面的AJAX调用）
    public function testConnection(){
        $api_key = input('post.api_key');
        $base_url = input('post.base_url', 'https://api.coze.cn');
        $api_version = input('post.api_version', 'v3');

        if(empty($api_key)){
            return json(['code'=>0,'msg'=>'API密钥不能为空']);
        }

        // 构建测试请求URL
        $url = rtrim($base_url, '/') . '/' . $api_version . '/bots';

        // 发送测试请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $api_key,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return json(['code' => 0, 'msg' => '连接失败: ' . $error]);
        }

        if ($httpCode === 200) {
            \app\common\System::plog('测试扣子API连接成功');
            return json(['code' => 1, 'msg' => 'API连接测试成功']);
        } else {
            $responseData = json_decode($response, true);
            $errorMsg = $responseData['msg'] ?? '连接失败，HTTP状态码: ' . $httpCode;
            return json(['code' => 0, 'msg' => $errorMsg]);
        }
    }

    //工作流管理页面
    public function workflow(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            $where = array();
            $where[] = ['aid','=',aid];

            if(input('param.name')) $where[] = ['name','like','%'.input('param.name').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];

            $count = 0 + Db::name('coze_workflow')->where($where)->count();
            $data = Db::name('coze_workflow')->where($where)->page($page,$limit)->order($order)->select()->toArray();

            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '启用' : '禁用';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
                $data[$k]['update_time_text'] = $v['update_time'] ? date('Y-m-d H:i:s', $v['update_time']) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }

        return View::fetch();
    }



    //保存工作流
    public function workflowSave(){
        $info = input('post.info');

        // 调试信息
        var_dump('=== 保存工作流调试信息 ===');
        var_dump('POST数据:', input('post.'));
        var_dump('info数据:', $info);

        // 验证必填字段
        if (empty($info['name'])) {
            var_dump('错误: 工作流名称为空');
            return json(['status' => 0, 'msg' => '工作流名称不能为空']);
        }

        if (empty($info['workflow_id'])) {
            var_dump('错误: 工作流ID为空');
            return json(['status' => 0, 'msg' => '工作流ID不能为空']);
        }

        // 过滤允许的字段
        $data = [];
        if(isset($info['name'])) $data['name'] = $info['name'];
        if(isset($info['workflow_id'])) $data['workflow_id'] = $info['workflow_id'];
        if(isset($info['description'])) $data['description'] = $info['description'];
        if(isset($info['status'])) $data['status'] = $info['status'];

        var_dump('最终要保存的数据:', $data);

        try {
            if($info['id']){
                // 更新工作流
                var_dump('执行更新操作，ID:', $info['id']);
                $data['update_time'] = time();
                $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$info['id'])->update($data);
                var_dump('更新结果:', $result);
                \app\common\System::plog('更新扣子工作流');
            }else{
                // 新增工作流
                var_dump('执行新增操作');
                $data['aid'] = aid;
                $data['create_time'] = time();
                $data['update_time'] = time();
                $result = Db::name('coze_workflow')->insert($data);
                var_dump('新增结果:', $result);
                \app\common\System::plog('新增扣子工作流');
            }
        } catch (\Exception $e) {
            var_dump('数据库操作失败:', $e->getMessage());
            return json(['status' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }

        var_dump('工作流保存成功');
        return json(['status'=>1,'msg'=>'操作成功','url'=>(string)url('workflow')]);
    }

    //删除工作流
    public function workflowDel(){
        $id = input('id',0,'intval');
        if(!$id){
            return json(['status'=>0,'msg'=>'参数错误']);
        }

        try {
            $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$id)->delete();
            if($result){
                \app\common\System::plog('删除扣子工作流');
                return json(['status'=>1,'msg'=>'删除成功']);
            }else{
                return json(['status'=>0,'msg'=>'删除失败']);
            }
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    //测试工作流
    public function testWorkflow(){
        $workflowId = input('workflow_id','');
        $parameters = input('parameters','{}');

        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        try {
            $params = json_decode($parameters, true);
            if(json_last_error() !== JSON_ERROR_NONE){
                return json(['code'=>0,'msg'=>'参数格式错误，请使用JSON格式']);
            }

            // 调用工作流
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $params);

            if($result['code'] == 1){
                return json(['code'=>1,'msg'=>'工作流执行成功','data'=>$result['data']]);
            }else{
                return json(['code'=>0,'msg'=>$result['msg'],'data'=>$result['data']]);
            }

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'测试失败: ' . $e->getMessage()]);
        }
    }

    //工作流演示页面
    public function workflowDemo(){
        $workflows = Db::name('coze_workflow')->where('aid',aid)->where('status',1)->select();
        View::assign('workflows',$workflows);
        return View::fetch();
    }

    //运行工作流演示
    public function runWorkflowDemo(){
        $workflowId = input('workflow_id','');
        $parameters = input('parameters','{}');

        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        try {
            $params = json_decode($parameters, true);
            if(json_last_error() !== JSON_ERROR_NONE){
                return json(['code'=>0,'msg'=>'参数格式错误，请使用JSON格式']);
            }

            // 调用工作流
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $params);

            // 记录执行历史
            $logData = [
                'aid' => aid,
                'workflow_id' => $workflowId,
                'parameters' => $parameters,
                'result' => json_encode($result),
                'status' => $result['code'] == 1 ? 1 : 0,
                'create_time' => time()
            ];
            Db::name('coze_workflow_log')->insert($logData);

            \app\common\System::plog('运行扣子工作流演示');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'运行失败: ' . $e->getMessage()]);
        }
    }

    //获取工作流执行历史
    public function getWorkflowHistory(){
        $page = input('page',1,'intval');
        $limit = input('limit',10,'intval');

        $list = Db::name('coze_workflow_log')
            ->where('aid',aid)
            ->field('id,workflow_id,parameters,status,create_time')
            ->order('id desc')
            ->page($page, $limit)
            ->select();

        $count = Db::name('coze_workflow_log')->where('aid',aid)->count();

        return json(['code'=>0,'msg'=>'','count'=>$count,'data'=>$list]);
    }

    //文件上传
    public function uploadFile(){
        $file = request()->file('file');
        if(!$file){
            return json(['code'=>0,'msg'=>'请选择要上传的文件']);
        }

        try {
            // 保存文件到临时目录
            $tempPath = sys_get_temp_dir() . '/' . uniqid() . '_' . $file->getOriginalName();
            $file->move(dirname($tempPath), basename($tempPath));

            // 上传到Coze
            $result = \app\common\Coze::uploadFile(aid, $tempPath);

            // 清理临时文件
            if(file_exists($tempPath)){
                unlink($tempPath);
            }

            if($result['code'] == 1){
                \app\common\System::plog('上传文件到扣子');
                return json(['code'=>1,'msg'=>'上传成功','data'=>$result['data']]);
            }else{
                return json(['code'=>0,'msg'=>$result['msg']]);
            }

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'上传失败: ' . $e->getMessage()]);
        }
    }

    //演示页面
    public function demo(){
        // 获取启用的工作流列表
        $workflows = Db::name('coze_workflow')
            ->where('aid', aid)
            ->where('status', 1)
            ->order('id desc')
            ->select()
            ->toArray();

        // 获取启用的API配置
        $configs = Db::name('coze_config')
            ->where('aid', aid)
            ->where('status', 1)
            ->order('id desc')
            ->select()
            ->toArray();

        View::assign('workflows', $workflows);
        View::assign('configs', $configs);

        \app\common\System::plog('访问扣子演示页面');

        return View::fetch();
    }

    //获取Bot信息
    public function getBotInfo(){
        try {
            $config = Db::name('coze_config')
                ->where('aid', aid)
                ->where('status', 1)
                ->find();

            if (!$config) {
                return json(['code'=>0,'msg'=>'未找到可用的API配置']);
            }

            \app\common\System::plog('获取Bot信息');

            return json([
                'code'=>1,
                'msg'=>'获取成功',
                'data'=>[
                    'bot_id' => $config['bot_id'],
                    'api_key' => $config['api_key'],
                    'base_url' => $config['base_url']
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取Bot信息失败: ' . $e->getMessage()]);
        }
    }

    //创建对话
    public function createConversation(){
        try {
            $botId = input('bot_id','');

            if(empty($botId)){
                return json(['code'=>0,'msg'=>'Bot ID不能为空']);
            }

            $result = \app\common\Coze::createConversation(aid, $botId);

            \app\common\System::plog('创建扣子对话');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'创建对话失败: ' . $e->getMessage()]);
        }
    }

    //发送消息
    public function sendMessage(){
        try {
            $conversationId = input('conversation_id','');
            $botId = input('bot_id','');
            $message = input('message','');

            if(empty($conversationId) || empty($botId) || empty($message)){
                return json(['code'=>0,'msg'=>'参数不完整']);
            }

            $result = \app\common\Coze::sendMessage(aid, $conversationId, $botId, $message);

            \app\common\System::plog('发送扣子消息');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'发送消息失败: ' . $e->getMessage()]);
        }
    }

    //获取工作流运行状态
    public function getWorkflowRunStatus(){
        try {
            $runId = input('run_id','');

            if(empty($runId)){
                return json(['code'=>0,'msg'=>'运行ID不能为空']);
            }

            $result = \app\common\Coze::getWorkflowRunStatus(aid, $runId);

            \app\common\System::plog('获取工作流运行状态');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取运行状态失败: ' . $e->getMessage()]);
        }
    }

    //代理请求
    public function proxyRequest(){
        try {
            $apiToken = input('api_token','');
            $endpoint = input('endpoint','');
            $method = input('method','POST');
            $data = input('data',[]);

            if(empty($apiToken) || empty($endpoint)){
                return json(['code'=>0,'msg'=>'参数不完整']);
            }

            $result = \app\common\Coze::proxyRequest(aid, $apiToken, $endpoint, $method, $data);

            \app\common\System::plog('代理扣子API请求');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'代理请求失败: ' . $e->getMessage()]);
        }
    }
}
