-- 扣子(Coze) API集成系统数据库表
-- 数据库表前缀：ddwx

-- 1. 扣子API配置表
CREATE TABLE `ddwx_coze_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL DEFAULT '默认配置' COMMENT '配置名称',
  `api_key` varchar(255) NOT NULL COMMENT 'Coze API密钥',
  `bot_id` varchar(100) DEFAULT NULL COMMENT '默认机器人ID',
  `workflow_id` varchar(100) DEFAULT NULL COMMENT '默认工作流ID',
  `base_url` varchar(255) DEFAULT 'https://api.coze.cn' COMMENT 'API基础URL',
  `api_version` varchar(10) DEFAULT 'v3' COMMENT 'API版本',
  `timeout` int(11) DEFAULT '30' COMMENT '请求超时时间(秒)',
  `max_retries` int(11) DEFAULT '3' COMMENT '最大重试次数',
  `remark` text COMMENT '备注信息',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子API配置表';

-- 2. 扣子API请求日志表
CREATE TABLE `ddwx_coze_api_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `method` varchar(10) NOT NULL DEFAULT 'POST' COMMENT '请求方法',
  `request_data` text COMMENT '请求数据(JSON)',
  `response_code` int(11) DEFAULT '0' COMMENT '响应状态码',
  `response_data` longtext COMMENT '响应数据',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `execution_time` float DEFAULT '0' COMMENT '执行时间(毫秒)',
  `ip` varchar(64) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '请求状态(0=失败,1=成功)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子API请求日志表';

-- 3. 扣子对话会话表
CREATE TABLE `ddwx_coze_conversation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
  `conversation_id` varchar(100) NOT NULL COMMENT '对话ID',
  `bot_id` varchar(100) NOT NULL COMMENT '机器人ID',
  `title` varchar(255) DEFAULT NULL COMMENT '对话标题',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=关闭,1=活跃)',
  `message_count` int(11) DEFAULT '0' COMMENT '消息数量',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_aid_mid` (`aid`, `mid`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子对话会话表';

-- 4. 扣子对话消息表
CREATE TABLE `ddwx_coze_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `conversation_id` varchar(100) NOT NULL COMMENT '对话ID',
  `message_id` varchar(100) DEFAULT NULL COMMENT '消息ID',
  `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT '角色(user/assistant)',
  `content` longtext NOT NULL COMMENT '消息内容',
  `content_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '内容类型(text/image/file)',
  `parent_message_id` varchar(100) DEFAULT NULL COMMENT '父消息ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子对话消息表';

-- 5. 扣子工作流运行记录表
CREATE TABLE `ddwx_coze_workflow_run` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `run_id` varchar(100) NOT NULL COMMENT '运行ID',
  `parameters` text COMMENT '参数(JSON)',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态(pending/running/completed/failed)',
  `output` longtext COMMENT '输出结果',
  `error` text COMMENT '错误信息',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间戳',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间戳',
  `execution_time` int(11) DEFAULT NULL COMMENT '执行时间(毫秒)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_run_id` (`run_id`),
  KEY `idx_aid_mid` (`aid`, `mid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流运行记录表';

-- 6. 扣子API速率限制记录表
CREATE TABLE `ddwx_coze_rate_limit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `count` int(11) NOT NULL DEFAULT '0' COMMENT '当前周期调用次数',
  `limit_value` int(11) NOT NULL DEFAULT '0' COMMENT '限制值',
  `reset_time` int(11) NOT NULL DEFAULT '0' COMMENT '重置时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_aid_endpoint` (`aid`, `endpoint`),
  KEY `idx_reset_time` (`reset_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子API速率限制记录表';

-- 7. 扣子工作流配置表
CREATE TABLE `ddwx_coze_workflow` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `description` text COMMENT '工作流描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流配置表';

-- 8. 扣子工作流执行日志表
CREATE TABLE `ddwx_coze_workflow_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `parameters` text COMMENT '执行参数',
  `result` text COMMENT '执行结果',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0=失败,1=成功)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流执行日志表';
