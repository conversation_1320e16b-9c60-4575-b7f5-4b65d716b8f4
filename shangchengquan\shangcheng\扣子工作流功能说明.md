# 扣子(Coze)工作流功能说明

## 📋 功能概述

本系统已成功集成扣子(Coze)工作流API功能，支持：
- 工作流配置管理
- 工作流执行调用
- 文件上传支持
- 执行日志记录
- 前端API接口
- 后端管理界面

## 🔧 API接口说明

### 工作流运行API
- **接口地址**: `https://api.coze.cn/v1/workflow/run`
- **请求方式**: POST
- **认证方式**: Bearer Token
- **请求格式**: JSON

### 文件上传API
- **接口地址**: `https://api.coze.cn/v1/files/upload`
- **请求方式**: POST (multipart/form-data)
- **认证方式**: Bearer Token

## 🗄️ 数据库表结构

### 1. 工作流配置表 (ddwx_coze_workflow)
```sql
- id: 主键ID
- aid: 应用ID
- name: 工作流名称
- workflow_id: 工作流ID
- description: 工作流描述
- status: 状态(0=禁用,1=启用)
- create_time: 创建时间戳
- update_time: 更新时间戳
```

### 2. 工作流执行日志表 (ddwx_coze_workflow_log)
```sql
- id: 主键ID
- aid: 应用ID
- mid: 会员ID
- workflow_id: 工作流ID
- parameters: 执行参数
- result: 执行结果
- status: 状态(0=失败,1=成功)
- create_time: 创建时间戳
```

## 🎯 后端功能

### 控制器方法 (app/controller/Coze.php)
- `workflow()`: 工作流管理页面
- `workflowEdit()`: 工作流编辑页面
- `workflowSave()`: 保存工作流配置
- `workflowDel()`: 删除工作流
- `testWorkflow()`: 测试工作流
- `workflowDemo()`: 工作流演示页面
- `runWorkflowDemo()`: 运行工作流演示
- `getWorkflowHistory()`: 获取执行历史
- `uploadFile()`: 文件上传

### 公共类方法 (app/common/Coze.php)
- `uploadFile()`: 上传文件到Coze
- `runWorkflow()`: 运行工作流
- `runWorkflowWithFiles()`: 运行工作流（带文件支持）

## 🌐 前端API接口

### API控制器方法 (app/controller/ApiCoze.php)
- `getWorkflowList()`: 获取工作流列表
- `runWorkflow()`: 运行工作流
- `runWorkflowWithFiles()`: 运行工作流（带文件上传）
- `getWorkflowLogs()`: 获取工作流执行记录
- `uploadfile()`: 文件上传

## 📱 使用方法

### 1. 配置工作流
1. 登录后台管理系统
2. 进入"拓展栏目" -> "Coze API" -> "工作流管理"
3. 点击"添加工作流"
4. 填写工作流名称、工作流ID、描述等信息
5. 保存配置

### 2. 测试工作流
1. 在工作流管理页面点击"测试"按钮
2. 输入JSON格式的参数
3. 查看执行结果

### 3. 工作流演示
1. 进入"工作流演示"页面
2. 选择要测试的工作流
3. 输入参数或上传文件
4. 点击"运行工作流"查看结果

### 4. 前端调用示例
```javascript
// 运行工作流
$.post('/api/coze/runWorkflow', {
    workflow_id: 'your_workflow_id',
    parameters: {
        text: 'Hello World',
        number: 123
    }
}, function(res){
    if(res.code == 1){
        console.log('执行成功:', res.data);
    } else {
        console.log('执行失败:', res.msg);
    }
});
```

## 🔐 权限配置

工作流功能已添加到后台菜单系统中，权限标识：
- `Coze/*`: 扣子API总权限
- `coze/workflow`: 工作流管理权限
- `coze/workflowDemo`: 工作流演示权限

## 📝 注意事项

1. **API密钥配置**: 需要先在"API配置"中配置有效的Coze API密钥
2. **工作流ID**: 需要从Coze平台获取正确的工作流ID
3. **参数格式**: 工作流参数必须是有效的JSON格式
4. **文件上传**: 支持文件上传作为工作流参数，文件会先上传到Coze再传递给工作流
5. **日志记录**: 所有工作流执行都会记录日志，便于调试和监控

## 🚀 扩展功能

系统支持进一步扩展：
- 工作流模板管理
- 批量执行工作流
- 工作流执行统计
- 自定义参数验证
- 工作流结果处理

## 🔧 故障排除

1. **工作流执行失败**: 检查API密钥和工作流ID是否正确
2. **参数错误**: 确保参数格式为有效JSON
3. **文件上传失败**: 检查文件大小和格式限制
4. **权限问题**: 确保用户有相应的菜单权限

---

**开发完成时间**: {$smarty.now|date_format:'%Y-%m-%d %H:%M:%S'}
**版本**: v1.0.0
