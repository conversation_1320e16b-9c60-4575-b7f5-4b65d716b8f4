-- 扣子系统数据库字段更新脚本
-- 用于修复现有数据库表缺少的字段

-- 检查并添加 name 字段
ALTER TABLE `ddwx_coze_config` 
ADD COLUMN `name` varchar(100) NOT NULL DEFAULT '默认配置' COMMENT '配置名称' AFTER `aid`;

-- 检查并添加 workflow_id 字段
ALTER TABLE `ddwx_coze_config` 
ADD COLUMN `workflow_id` varchar(100) DEFAULT NULL COMMENT '默认工作流ID' AFTER `bot_id`;

-- 检查并添加 timeout 字段
ALTER TABLE `ddwx_coze_config` 
ADD COLUMN `timeout` int(11) DEFAULT '30' COMMENT '请求超时时间(秒)' AFTER `api_version`;

-- 检查并添加 max_retries 字段
ALTER TABLE `ddwx_coze_config` 
ADD COLUMN `max_retries` int(11) DEFAULT '3' COMMENT '最大重试次数' AFTER `timeout`;

-- 检查并添加 remark 字段
ALTER TABLE `ddwx_coze_config` 
ADD COLUMN `remark` text COMMENT '备注信息' AFTER `max_retries`;

-- 添加索引
ALTER TABLE `ddwx_coze_config` 
ADD INDEX `idx_status` (`status`);

-- 移除唯一索引，改为普通索引（支持多个配置）
ALTER TABLE `ddwx_coze_config` 
DROP INDEX `idx_aid`;

ALTER TABLE `ddwx_coze_config` 
ADD INDEX `idx_aid` (`aid`);
