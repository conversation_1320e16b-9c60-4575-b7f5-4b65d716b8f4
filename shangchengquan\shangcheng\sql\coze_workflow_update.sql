-- 扣子工作流功能数据库更新脚本
-- 执行前请备份数据库

-- 1. 检查并创建工作流配置表
CREATE TABLE IF NOT EXISTS `ddwx_coze_workflow` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `description` text COMMENT '工作流描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流配置表';

-- 2. 检查并创建工作流执行日志表
CREATE TABLE IF NOT EXISTS `ddwx_coze_workflow_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `parameters` text COMMENT '执行参数',
  `result` text COMMENT '执行结果',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0=失败,1=成功)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流执行日志表';

-- 3. 插入示例工作流数据（可选）
-- INSERT INTO `ddwx_coze_workflow` (`aid`, `name`, `workflow_id`, `description`, `status`, `create_time`, `update_time`) VALUES
-- (1, '文本处理工作流', 'workflow_123456', '用于处理文本内容的工作流', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- (1, '图片分析工作流', 'workflow_789012', '用于分析图片内容的工作流', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 更新完成
SELECT '扣子工作流功能数据库更新完成！' as message;
