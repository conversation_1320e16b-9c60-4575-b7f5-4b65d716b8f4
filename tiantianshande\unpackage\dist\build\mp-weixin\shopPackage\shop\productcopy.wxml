<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="3dafff6e-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;">{{item.nickname+" "+item.showtime+"购买了该商品"}}</view></swiper-item></block></swiper></view></block><block wx:if="{{showtoptabbar==1&&toptabbar_show==1}}"><view class="toptabbar_tab"><view class="{{['item',toptabbar_index==0?'on':'']}}" style="{{'color:'+(toptabbar_index==0?$root.m2:'#333')+';'}}" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">商品<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view><view class="{{['item',toptabbar_index==1?'on':'']}}" style="{{'color:'+(toptabbar_index==1?$root.m4:'#333')+';'}}" data-index="1" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><view class="{{['item',toptabbar_index==2?'on':'']}}" style="{{'color:'+(toptabbar_index==2?$root.m6:'#333')+';'}}" data-index="2" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">详情<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view><block wx:if="{{$root.g1>0}}"><view class="{{['item',toptabbar_index==3?'on':'']}}" style="{{'color:'+(toptabbar_index==3?$root.m8:'#333')+';'}}" data-index="3" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">推荐<view class="after" style="{{'background:'+($root.m9)+';'}}"></view></view></block></view></block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab2"><view class="detail"><dp vue-id="3dafff6e-2" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><view id="scroll_view_tab3"><block wx:if="{{$root.g2>0}}"><view><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="/static/img/xihuan.png"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view><view class="prolist"><dp-product-item vue-id="3dafff6e-3" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-product-item></view></view></block></view><view style="width:100%;height:140rpx;"></view></scroll-view><block wx:if="{{product.status==1&&!showcuxiaodialog}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><block wx:if="{{showjiesheng==1}}"><view class="op2"><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="tocart2" style="{{'background:'+($root.m10)+';'}}" bindtap="__e"><text>分享赚钱</text><text style="font-size:24rpx;">{{"赚￥"+product.commission}}</text></view><view class="tobuy2" style="{{'background:'+($root.m11)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><text>立即购买</text><block wx:if="{{product.jiesheng_money>0}}"><text style="font-size:24rpx;">{{"省￥"+product.jiesheng_money}}</text></block></view></view></block><block wx:else><view class="op"><block wx:if="{{product.price_type==1}}"><block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m12)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" bindtap="__e">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view></block></block><block wx:else><block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m13)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">立即参与</view></block></block></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="3dafff6e-4" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^showLinkChange',[['showLinkChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:showLinkChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view hidden="{{!(scrolltopshow)}}" class="scrolltop" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/gotop.png"></image></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m14=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m15=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m16=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view><block wx:if="{{$root.m17}}"><view data-event-opts="{{[['tap',[['shareScheme',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">小程序链接</text></view></block></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{showScheme}}"><view class="posterDialog schemeDialog"><view class="main"><view class="schemecon"><view style="line-height:60rpx;">{{product.name+''}}</view><view>购买链接：<text style="color:#00A0E9;">{{schemeurl}}</text></view><view class="copybtn" style="{{'background:'+('linear-gradient(90deg,'+$root.m18+' 0%,rgba('+$root.m19+',0.8) 100%)')+';'}}" data-text="{{product.name+'购买链接：'+schemeurl}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">一键复制</view></view></view></view></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{sysset.name}}</view><block wx:if="{{product.bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{business.name}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{business.tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m20)+';'}}" data-url="{{'tel::'+business.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{business.tel}}<image class="copyicon" src="../../static/img/copy.png" data-text="{{business.tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="3dafff6e-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="3dafff6e-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3dafff6e-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>