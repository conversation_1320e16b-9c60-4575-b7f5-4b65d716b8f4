<?php
/**
 * 添加扣子工作流测试数据
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use think\facade\Db;

echo "开始添加扣子工作流测试数据...\n";

try {
    // 设置aid为1（通常是默认的应用ID）
    $aid = 1;
    
    // 检查ddwx_coze_workflow表是否存在数据
    $existingWorkflows = Db::name('coze_workflow')->where('aid', $aid)->count();
    
    if ($existingWorkflows > 0) {
        echo "已存在 {$existingWorkflows} 条工作流数据，跳过添加\n";
    } else {
        echo "正在添加工作流测试数据...\n";
        
        // 添加测试工作流数据
        $workflowData = [
            [
                'aid' => $aid,
                'name' => '文档分析工作流',
                'workflow_id' => '7476690468139860006',
                'description' => '用于分析PDF文档内容的工作流',
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ],
            [
                'aid' => $aid,
                'name' => '图片处理工作流',
                'workflow_id' => '7476690468139860007',
                'description' => '用于处理和分析图片的工作流',
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ],
            [
                'aid' => $aid,
                'name' => '文本生成工作流',
                'workflow_id' => '7476690468139860008',
                'description' => '用于生成文本内容的工作流',
                'status' => 0,
                'create_time' => time(),
                'update_time' => time()
            ]
        ];
        
        foreach ($workflowData as $data) {
            Db::name('coze_workflow')->insert($data);
            echo "✅ 已添加工作流: {$data['name']}\n";
        }
    }
    
    // 检查ddwx_coze_config表是否存在数据
    $existingConfigs = Db::name('coze_config')->where('aid', $aid)->count();
    
    if ($existingConfigs > 0) {
        echo "已存在 {$existingConfigs} 条API配置数据，跳过添加\n";
    } else {
        echo "正在添加API配置测试数据...\n";
        
        // 添加测试API配置数据
        $configData = [
            'aid' => $aid,
            'name' => '默认配置',
            'api_key' => 'pat_WFZvXcAixTK2EStyRXGQzhLV3qSR5RzsKA4ycvKmvsVkXn4CAg9FGQoaXZpdHuDR',
            'bot_id' => '7476690468139860006',
            'workflow_id' => '7476690468139860006',
            'base_url' => 'https://api.coze.cn',
            'api_version' => 'v1',
            'timeout' => 30,
            'max_retries' => 3,
            'remark' => '测试配置，用于开发和调试',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        Db::name('coze_config')->insert($configData);
        echo "✅ 已添加API配置: {$configData['name']}\n";
    }
    
    // 验证数据是否添加成功
    echo "\n=== 验证数据 ===\n";
    
    $workflows = Db::name('coze_workflow')->where('aid', $aid)->select()->toArray();
    echo "工作流数据 ({$aid}):\n";
    foreach ($workflows as $workflow) {
        $statusText = $workflow['status'] ? '启用' : '禁用';
        echo "  - ID: {$workflow['id']}, 名称: {$workflow['name']}, 状态: {$statusText}\n";
    }
    
    $configs = Db::name('coze_config')->where('aid', $aid)->select()->toArray();
    echo "\nAPI配置数据 ({$aid}):\n";
    foreach ($configs as $config) {
        $statusText = $config['status'] ? '启用' : '禁用';
        echo "  - ID: {$config['id']}, 名称: {$config['name']}, 状态: {$statusText}\n";
    }
    
    echo "\n✅ 测试数据添加完成！\n";
    echo "现在可以访问后台扣子管理页面查看数据了。\n";
    
} catch (Exception $e) {
    echo "❌ 添加测试数据失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?>
