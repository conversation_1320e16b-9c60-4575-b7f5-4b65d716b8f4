<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>工作流管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> 工作流管理</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('{:url('edit')}/type/workflow')">添加工作流</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">启用</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">禁用</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:80px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">启用</option>
										<option value="0">禁用</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="LAY-user-front-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<table class="layui-hide" id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
						<script type="text/html" id="table-useradmin-admin">
							<div class="layui-btn-container">
								{{# if(d.status == 1){ }}
								<a class="layui-btn layui-btn-xs" lay-event="test">测试</a>
								{{# } }}
								<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
								<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
							</div>
						</script>
					</div>
        </div>
    </div>
  </div>
  {include file="public/js"/}
  <script>
  layui.use(['table', 'form'], function(){
      var $ = layui.$
      ,table = layui.table
      ,form = layui.form;

      window.tableIns = table.render({
          elem: '#LAY-user-manage'
          ,url: '{:url("workflow")}'
          ,cols: [[
              {type: 'checkbox', fixed: 'left'}
              ,{field: 'id', width: 80, title: 'ID', sort: true}
              ,{field: 'name', title: '工作流名称', minWidth: 200}
              ,{field: 'workflow_id', title: '工作流ID', minWidth: 200}
              ,{field: 'description', title: '描述', minWidth: 200}
              ,{field: 'status_text', title: '状态', width: 80, align: 'center'}
              ,{field: 'create_time_text', title: '创建时间', width: 160}
              ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
          ]]
          ,page: true
          ,limit: 20
          ,height: 'full-220'
          ,text: '对不起，加载出现异常！'
      });

      //监听搜索
      form.on('submit(LAY-user-front-search)', function(data){
          var field = data.field;
          tableIns.reload({
              where: field
          });
      });

      //监听工具条
      table.on('tool(LAY-user-manage)', function(obj){
          var data = obj.data;
          if(obj.event === 'del'){
              layer.confirm('确定删除此工作流？', function(index){
                  datadel(data.id);
                  layer.close(index);
              });
          } else if(obj.event === 'edit'){
              openmax('{:url("edit")}/type/workflow/id/' + data.id);
          } else if(obj.event === 'test'){
              openmax('{:url("demo")}/id/' + data.id);
          }
      });
  });

  function datadel(id){
      var checkStatus = window.tableIns.checkStatus('LAY-user-manage');
      var data = checkStatus.data;
      var ids = [];
      if(id > 0){
          ids.push(id);
      } else {
          if(data.length === 0){
              layer.msg('请选择要删除的数据');
              return;
          }
          for(var i = 0; i < data.length; i++){
              ids.push(data[i].id);
          }
      }

      $.post('{:url("del")}', {ids: ids, type: 'workflow'}, function(res){
          if(res.status == 1){
              layer.msg(res.msg, {icon: 1});
              window.tableIns.reload();
          } else {
              layer.msg(res.msg, {icon: 2});
          }
      });
  }

  function setst(id, status){
      var checkStatus = window.tableIns.checkStatus('LAY-user-manage');
      var data = checkStatus.data;
      var ids = [];
      if(id > 0){
          ids.push(id);
      } else {
          if(data.length === 0){
              layer.msg('请选择要操作的数据');
              return;
          }
          for(var i = 0; i < data.length; i++){
              ids.push(data[i].id);
          }
      }

      $.post('{:url("setst")}', {ids: ids, status: status, type: 'workflow'}, function(res){
          if(res.status == 1){
              layer.msg(res.msg, {icon: 1});
              window.tableIns.reload();
          } else {
              layer.msg(res.msg, {icon: 2});
          }
      });
  }
  </script>
</body>
</html>
