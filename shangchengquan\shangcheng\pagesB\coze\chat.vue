<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="title">AI助手</view>
		</view>
		
		<!-- 聊天消息区域 -->
		<scroll-view class="chat-area" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
			<view class="message-list">
				<view v-for="(message, index) in messageList" :key="index" class="message-item">
					<view v-if="message.role === 'user'" class="user-message">
						<view class="message-content user-content">
							<text>{{ message.content }}</text>
						</view>
						<view class="avatar user-avatar">
							<image :src="userAvatar" mode="aspectFill"></image>
						</view>
					</view>
					<view v-else class="bot-message">
						<view class="avatar bot-avatar">
							<image :src="botAvatar" mode="aspectFill"></image>
						</view>
						<view class="message-content bot-content">
							<text>{{ message.content }}</text>
						</view>
					</view>
				</view>
				
				<!-- 加载中提示 -->
				<view v-if="isLoading" class="bot-message">
					<view class="avatar bot-avatar">
						<image :src="botAvatar" mode="aspectFill"></image>
					</view>
					<view class="message-content bot-content loading">
						<text>正在思考中...</text>
						<view class="loading-dots">
							<view class="dot"></view>
							<view class="dot"></view>
							<view class="dot"></view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-container">
				<input 
					class="message-input" 
					v-model="inputMessage" 
					placeholder="请输入消息..." 
					:disabled="isLoading"
					@confirm="sendMessage"
				/>
				<button 
					class="send-btn" 
					:class="{ 'disabled': !inputMessage.trim() || isLoading }"
					@tap="sendMessage"
					:disabled="!inputMessage.trim() || isLoading"
				>
					发送
				</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			messageList: [],
			inputMessage: '',
			isLoading: false,
			scrollTop: 0,
			conversationId: '',
			botId: '',
			userAvatar: '/static/img/default-avatar.png',
			botAvatar: '/static/img/bot-avatar.png'
		}
	},
	onLoad(options) {
		// 获取传入的机器人ID
		if (options.botId) {
			this.botId = options.botId;
		}
		
		// 初始化对话
		this.initConversation();
		
		// 添加欢迎消息
		this.messageList.push({
			role: 'assistant',
			content: '您好！我是AI助手，有什么可以帮助您的吗？',
			timestamp: Date.now()
		});
	},
	methods: {
		/**
		 * 初始化对话
		 */
		async initConversation() {
			try {
				const res = await this.$api.post('/apicoze/createConversation', {
					bot_id: this.botId
				});
				
				if (res.code === 1) {
					this.conversationId = res.data.conversation_id;
				} else {
					uni.showToast({
						title: res.msg || '初始化对话失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('初始化对话失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			}
		},
		
		/**
		 * 发送消息
		 */
		async sendMessage() {
			if (!this.inputMessage.trim() || this.isLoading) {
				return;
			}
			
			const message = this.inputMessage.trim();
			this.inputMessage = '';
			
			// 添加用户消息到列表
			this.messageList.push({
				role: 'user',
				content: message,
				timestamp: Date.now()
			});
			
			// 滚动到底部
			this.scrollToBottom();
			
			// 设置加载状态
			this.isLoading = true;
			
			try {
				const res = await this.$api.post('/apicoze/chat', {
					bot_id: this.botId,
					user_id: this.$store.state.user.id || 'anonymous',
					message: message,
					conversation_id: this.conversationId
				});
				
				this.isLoading = false;
				
				if (res.code === 1) {
					// 添加机器人回复到列表
					this.messageList.push({
						role: 'assistant',
						content: res.data.content || res.data.reply || '抱歉，我没有理解您的问题。',
						timestamp: Date.now()
					});
					
					// 更新对话ID（如果有新的）
					if (res.data.conversation_id) {
						this.conversationId = res.data.conversation_id;
					}
				} else {
					// 添加错误消息
					this.messageList.push({
						role: 'assistant',
						content: '抱歉，我暂时无法回答您的问题，请稍后再试。',
						timestamp: Date.now()
					});
					
					uni.showToast({
						title: res.msg || '发送失败',
						icon: 'none'
					});
				}
			} catch (error) {
				this.isLoading = false;
				console.error('发送消息失败:', error);
				
				// 添加错误消息
				this.messageList.push({
					role: 'assistant',
					content: '网络连接异常，请检查网络后重试。',
					timestamp: Date.now()
				});
				
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			}
			
			// 滚动到底部
			this.scrollToBottom();
		},
		
		/**
		 * 滚动到底部
		 */
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.header {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #e5e5e5;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	color: #333;
}

.chat-area {
	flex: 1;
	padding: 100rpx 0 120rpx 0;
}

.message-list {
	padding: 20rpx;
}

.message-item {
	margin-bottom: 30rpx;
}

.user-message, .bot-message {
	display: flex;
	align-items: flex-end;
}

.user-message {
	justify-content: flex-end;
}

.bot-message {
	justify-content: flex-start;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
	margin: 0 20rpx;
}

.avatar image {
	width: 100%;
	height: 100%;
}

.message-content {
	max-width: 500rpx;
	padding: 20rpx 30rpx;
	border-radius: 20rpx;
	word-wrap: break-word;
}

.user-content {
	background-color: #007aff;
	color: #fff;
	border-bottom-right-radius: 8rpx;
}

.bot-content {
	background-color: #fff;
	color: #333;
	border-bottom-left-radius: 8rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.loading {
	display: flex;
	align-items: center;
}

.loading-dots {
	display: flex;
	margin-left: 20rpx;
}

.dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background-color: #999;
	margin: 0 2rpx;
	animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
	0%, 80%, 100% {
		transform: scale(0);
	}
	40% {
		transform: scale(1);
	}
}

.input-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1rpx solid #e5e5e5;
	padding: 20rpx;
}

.input-container {
	display: flex;
	align-items: center;
}

.message-input {
	flex: 1;
	height: 80rpx;
	padding: 0 20rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 40rpx;
	font-size: 28rpx;
	background-color: #f8f8f8;
}

.send-btn {
	margin-left: 20rpx;
	padding: 0 30rpx;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.send-btn.disabled {
	background-color: #ccc;
	color: #999;
}
</style>
