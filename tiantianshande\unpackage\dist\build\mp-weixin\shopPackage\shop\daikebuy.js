(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["shopPackage/shop/daikebuy"],{1753:function(t,o,a){"use strict";(function(t,o){var r=a("47a9");a("06e9");r(a("3240"));var i=r(a("a16a"));t.__webpack_require_UNI_MP_PLUGIN__=a,o(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"2e99":function(t,o,a){"use strict";var r=a("fe43"),i=a.n(r);i.a},"8d61":function(t,o,a){"use strict";a.d(o,"b",(function(){return i})),a.d(o,"c",(function(){return n})),a.d(o,"a",(function(){return r}));var r={dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},i=function(){var t=this,o=t.$createElement,a=(t._self._c,t.isload?t.__map(t.proInfo,(function(o,a){var r=t.__get_orig(o),i=t.t("color1");return{$orig:r,m0:i}})):null),r=t.isload?t.t("color1"):null,i=t.isload?t.t("color1rgb"):null,n=t.isload&&t.cartList.total>0?t.t("color1"):null,e=t.isload?t.t("color1"):null,d=t.isload?t.t("color1"):null,c=t.isload?t.t("color1rgb"):null;t.$mp.data=Object.assign({},{$root:{l0:a,m1:r,m2:i,m3:n,m4:e,m5:d,m6:c}})},n=[]},a16a:function(t,o,a){"use strict";a.r(o);var r=a("8d61"),i=a("f6af");for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(o,t,(function(){return i[t]}))}(n);a("2e99");var e=a("828b"),d=Object(e["a"])(i["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);o["default"]=d.exports},a4b4:function(t,o,a){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var r=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,cartListShow:!1,buydialogShow:!1,harr:[],datalist:[],cartList:{},proid:"",totalprice:"0.00",currentActiveIndex:0,animation:!0,scrollToViewId:"",bid:"",scrollState:!0,orderData:{},proInfo:{},order_id:""}},onLoad:function(t){console.log(t),this.opt=t,this.bid=t.bid?t.bid:"",this.order_id=t["orderid"],this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this,o=t.opt.bid?t.opt.bid:0;t.loading=!0,r.get("ApiDaigou/daigoubuy",{bid:o,orderid:t.order_id},(function(o){var a=o.data;t.orderData=a,t.proInfo=a["product"],console.log(a),t.loaded()}))},clickRootItem:function(t){var o=this;this.scrollState=!1;var a=t.currentTarget.dataset;this.scrollToViewId="detail-"+a.rootItemId,this.currentActiveIndex=a.rootItemIndex,setTimeout((function(){o.scrollState=!0}),500)},addcart:function(t){var o=this,a=(o.ks,t.currentTarget.dataset.num),i=t.currentTarget.dataset.proid,n=t.currentTarget.dataset.ggid;o.loading=!0,r.post("ApiShop/addcart",{proid:i,ggid:n,num:a},(function(t){o.loading=!1,1==t.status?o.getdata():r.error(t.msg)}))},afteraddcart:function(t){t.hasoption=!1,console.log("111"),this.addcart({currentTarget:{dataset:t}})},clearShopCartFn:function(){var t=this;r.post("ApiShop/cartclear",{bid:t.opt.bid},(function(o){t.getdata()}))},gopay:function(){for(var t=this.proInfo,o=[],a=0;a<t.length;a++)o.push(t[a].proid+","+t[a].ggid+","+t[a].num);r.goto("buy?frompage=fastbuy&prodata="+o.join("-"))},gotoCatproductPage:function(t){var o=t.currentTarget.dataset;this.bid?r.goto("/shopPackage/shop/prolist?bid="+this.bid+"&cid2="+o.id):r.goto("/shopPackage/shop/prolist?cid="+o.id)},scroll:function(t){if(this.scrollState)for(var o=t.detail.scrollTop,a=this.harr,r=0,i=0;i<a.length;i++){if(o>=r&&o<r+a[i]){this.currentActiveIndex=i;break}r+=a[i]}},buydialogChange:function(t){this.buydialogShow||(this.proid=t.currentTarget.dataset.proid),this.buydialogShow=!this.buydialogShow},handleClickMask:function(){this.cartListShow=!this.cartListShow}}};o.default=i},f6af:function(t,o,a){"use strict";a.r(o);var r=a("a4b4"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(o,t,(function(){return r[t]}))}(n);o["default"]=i.a},fe43:function(t,o,a){}},[["1753","common/runtime","common/vendor"]]]);