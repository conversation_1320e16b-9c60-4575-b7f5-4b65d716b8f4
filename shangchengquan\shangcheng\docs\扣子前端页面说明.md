# 扣子(Coze)前端页面说明

## 页面结构

### 1. 聊天页面
**路径**: `pagesB/coze/chat.vue`
**功能**: AI助手聊天界面
**特性**:
- 实时聊天对话
- 消息历史记录
- 加载状态提示
- 自动滚动到底部
- 现代化UI设计

### 2. 机器人列表页面
**路径**: `pagesB/coze/bots.vue` (待创建)
**功能**: 显示可用的AI机器人列表
**特性**:
- 机器人卡片展示
- 搜索和筛选
- 机器人详情查看
- 选择机器人进入聊天

### 3. 对话历史页面
**路径**: `pagesB/coze/conversations.vue` (待创建)
**功能**: 查看历史对话记录
**特性**:
- 对话列表展示
- 按时间排序
- 搜索对话内容
- 删除对话记录

## API接口调用

### 前端API调用格式
```javascript
// 使用统一的API调用方法
const res = await this.$api.post('/apicoze/chat', {
    bot_id: this.botId,
    user_id: this.$store.state.user.id,
    message: message
});

// 处理响应
if (res.code === 1) {
    // 成功处理
    console.log('成功:', res.data);
} else {
    // 错误处理
    uni.showToast({
        title: res.msg || '操作失败',
        icon: 'none'
    });
}
```

### 主要接口列表

1. **获取机器人列表**
   - 接口: `/apicoze/getBots`
   - 方法: GET
   - 参数: 无

2. **创建对话**
   - 接口: `/apicoze/createConversation`
   - 方法: POST
   - 参数: `bot_id`

3. **发送消息**
   - 接口: `/apicoze/chat`
   - 方法: POST
   - 参数: `bot_id`, `user_id`, `message`, `conversation_id`

4. **获取对话历史**
   - 接口: `/apicoze/getConversationHistory`
   - 方法: GET
   - 参数: `conversation_id`, `limit`, `before`

5. **获取用户对话列表**
   - 接口: `/apicoze/getUserConversations`
   - 方法: GET
   - 参数: `limit`, `before`

## 样式规范

### 颜色主题
- 主色调: `#007aff` (蓝色)
- 背景色: `#f5f5f5` (浅灰)
- 文字色: `#333` (深灰)
- 边框色: `#e5e5e5` (浅灰边框)

### 组件样式
- 圆角: `20rpx` (消息气泡)
- 阴影: `0 2rpx 10rpx rgba(0,0,0,0.1)`
- 间距: `20rpx` (标准间距)
- 头像: `80rpx` (圆形头像)

### 响应式设计
- 消息最大宽度: `500rpx`
- 输入框高度: `80rpx`
- 按钮高度: `80rpx`

## 使用说明

### 1. 集成到现有项目
将 `pagesB/coze/` 目录下的页面文件复制到项目中，确保路径正确。

### 2. 配置路由
在项目的路由配置中添加扣子相关页面路由。

### 3. API配置
确保后端API接口已正确配置，前端可以正常调用。

### 4. 权限控制
根据需要添加用户权限验证，确保只有授权用户可以使用AI功能。

## 开发注意事项

1. **错误处理**: 所有API调用都应包含错误处理逻辑
2. **加载状态**: 长时间操作应显示加载提示
3. **用户体验**: 消息发送后立即显示，提升响应速度
4. **数据缓存**: 适当缓存机器人列表等不常变化的数据
5. **网络异常**: 处理网络连接异常情况

## 扩展功能

### 可添加的功能
1. 语音输入支持
2. 图片消息发送
3. 消息收藏功能
4. 对话分享功能
5. 多语言支持
6. 主题切换功能

### 性能优化
1. 虚拟滚动(长对话列表)
2. 图片懒加载
3. 消息分页加载
4. 离线消息缓存

## 测试建议

1. **功能测试**: 测试所有API接口调用
2. **兼容性测试**: 测试不同设备和系统版本
3. **网络测试**: 测试弱网络环境下的表现
4. **压力测试**: 测试长时间使用的稳定性

## 维护说明

1. **日志记录**: 记录重要操作和错误信息
2. **版本控制**: 保持代码版本管理
3. **文档更新**: 及时更新开发文档
4. **用户反馈**: 收集和处理用户反馈
