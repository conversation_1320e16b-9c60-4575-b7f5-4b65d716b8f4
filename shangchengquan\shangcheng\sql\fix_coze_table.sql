-- 修复扣子配置表缺少字段的问题
-- 请在数据库中执行此SQL脚本

-- 添加 name 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'ddwx_coze_config' 
     AND column_name = 'name' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "name字段已存在" as message',
    'ALTER TABLE `ddwx_coze_config` ADD COLUMN `name` varchar(100) NOT NULL DEFAULT "默认配置" COMMENT "配置名称" AFTER `aid`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 workflow_id 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'ddwx_coze_config' 
     AND column_name = 'workflow_id' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "workflow_id字段已存在" as message',
    'ALTER TABLE `ddwx_coze_config` ADD COLUMN `workflow_id` varchar(100) DEFAULT NULL COMMENT "默认工作流ID" AFTER `bot_id`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 timeout 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'ddwx_coze_config' 
     AND column_name = 'timeout' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "timeout字段已存在" as message',
    'ALTER TABLE `ddwx_coze_config` ADD COLUMN `timeout` int(11) DEFAULT 30 COMMENT "请求超时时间(秒)" AFTER `api_version`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 max_retries 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'ddwx_coze_config' 
     AND column_name = 'max_retries' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "max_retries字段已存在" as message',
    'ALTER TABLE `ddwx_coze_config` ADD COLUMN `max_retries` int(11) DEFAULT 3 COMMENT "最大重试次数" AFTER `timeout`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 remark 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'ddwx_coze_config' 
     AND column_name = 'remark' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "remark字段已存在" as message',
    'ALTER TABLE `ddwx_coze_config` ADD COLUMN `remark` text COMMENT "备注信息" AFTER `max_retries`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT "扣子配置表字段修复完成！" as result;
