(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["shopPackage/shop/category3"],{"03e7":function(t,n,o){},5092:function(t,n,o){"use strict";(function(t,n){var a=o("47a9");o("06e9");a(o("3240"));var e=a(o("e8ff"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"59b4":function(t,n,o){"use strict";var a=o("03e7"),e=o.n(a);e.a},6434:function(t,n,o){"use strict";o.r(n);var a=o("d584"),e=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(i);n["default"]=e.a},d584:function(t,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),e={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:[],currentActiveIndex:0,animation:!0,clist:"",bid:""}},onLoad:function(t){this.opt=a.getopts(t),this.bid=this.opt.bid?this.opt.bid:"",this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,a.get("ApiShop/category3",{bid:t.bid},(function(n){t.loading=!1,t.data=n.data,t.loaded(),t.getdownclist3(n.data[0].id)}))},getdownclist3:function(t){var n=this;n.loading=!0,n.nodata=!1,a.post("ApiShop/getdownclist3",{id:t,bid:n.bid},(function(t){n.loading=!1,n.clist=t.data,0==n.clist.length&&(n.nodata=!0)}))},clickRootItem:function(t){var n=t.currentTarget.dataset;this.currentActiveIndex=n.rootItemIndex;var o=n.rootItemId;this.getdownclist3(o)},gotoCatproductPage:function(t){var n=t.currentTarget.dataset;this.bid?a.goto("/shopPackage/shop/prolist?bid="+this.bid+"&cid2="+n.id):a.goto("/shopPackage/shop/prolist?cid="+n.id)}}};n.default=e},e8ff:function(t,n,o){"use strict";o.r(n);var a=o("eb72"),e=o("6434");for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);o("59b4");var d=o("828b"),r=Object(d["a"])(e["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=r.exports},eb72:function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){return a}));var a={nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},e=function(){var t=this,n=t.$createElement,o=(t._self._c,t.isload?t.__map(t.data,(function(n,o){var a=t.__get_orig(n),e=t.t("color1");return{$orig:a,m0:e}})):null);t.$mp.data=Object.assign({},{$root:{l0:o}})},i=[]}},[["5092","common/runtime","common/vendor"]]]);