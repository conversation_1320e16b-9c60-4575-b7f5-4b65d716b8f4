<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

/*分销*/
namespace app\controller;
use think\facade\Db;
class ApiAgent extends ApiCommon
{
	public function initialize(){
		parent::initialize();
		$this->checklogin();
	}
	public function commissionSurvey(){
		// 性能监控：记录接口开始时间
		$interface_start_time = microtime(true);
		$timestamp = date('Y-m-d H:i:s');
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey] 接口开始执行', 'info');

		// 性能监控：用户信息查询
		$step_start = microtime(true);
		$userinfo = Db::name('member')->field('*')->where('aid',aid)->where('id',mid)->find();
		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_001] 用户信息查询完成，耗时: '.$step_time.'ms', 'info');

		// 性能监控：提现统计查询
		$step_start = microtime(true);
		$count = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->sum('txmoney');
		$count0 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',0)->sum('txmoney');
		$count1 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',1)->sum('txmoney');
		$count2 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',2)->sum('txmoney');
		$count3 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',3)->sum('txmoney');
		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_002] 提现统计查询完成，耗时: '.$step_time.'ms', 'info');

		if($userinfo){
			// 性能监控：20层级佣金查询开始
			$step_start = microtime(true);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_003] 开始20层级佣金查询', 'info');

			$countdqr1 = Db::query("select sum(parent1commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent1={$userinfo['id']}");
			$countdqr2 = Db::query("select sum(parent2commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent2={$userinfo['id']}");
			$countdqr3 = Db::query("select sum(parent3commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent3={$userinfo['id']}");
			$countdqr4 = Db::query("select sum(parent4commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent4={$userinfo['id']}");
			$countdqr5 = Db::query("select sum(parent5commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent5={$userinfo['id']}");
			$countdqr6 = Db::query("select sum(parent6commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent6={$userinfo['id']}");
			$countdqr7 = Db::query("select sum(parent7commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent7={$userinfo['id']}");
			$countdqr8 = Db::query("select sum(parent8commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent8={$userinfo['id']}");
			$countdqr9 = Db::query("select sum(parent9commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent9={$userinfo['id']}");
			$countdqr10 = Db::query("select sum(parent10commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent10={$userinfo['id']}");
			$countdqr11 = Db::query("select sum(parent11commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent11={$userinfo['id']}");
			$countdqr12 = Db::query("select sum(parent12commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent12={$userinfo['id']}");
			$countdqr13 = Db::query("select sum(parent13commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent13={$userinfo['id']}");
			$countdqr14 = Db::query("select sum(parent14commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent14={$userinfo['id']}");
			$countdqr15 = Db::query("select sum(parent15commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent15={$userinfo['id']}");
			$countdqr16 = Db::query("select sum(parent16commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent16={$userinfo['id']}");
			$countdqr17 = Db::query("select sum(parent17commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent17={$userinfo['id']}");
			$countdqr18 = Db::query("select sum(parent18commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent18={$userinfo['id']}");
			$countdqr19 = Db::query("select sum(parent19commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent19={$userinfo['id']}");
			$countdqr20 = Db::query("select sum(parent20commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent20={$userinfo['id']}");

			$step_time = round((microtime(true) - $step_start) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_004] 20层级佣金查询完成，耗时: '.$step_time.'ms', 'info');
		}
		if($userinfo['pid']){
			$userinfo['pnickname'] = Db::name('member')->where('aid',aid)->where('id',$userinfo['pid'])->value('nickname');
		}
		$countdqr = 0 + $countdqr1[0]['c'] + $countdqr2[0]['c'] + $countdqr3[0]['c']+ $countdqr4[0]['c']+ $countdqr5[0]['c']+ $countdqr6[0]['c']+ $countdqr7[0]['c']+ $countdqr8[0]['c']+ $countdqr9[0]['c']+ $countdqr10[0]['c']+ $countdqr11[0]['c']+ $countdqr12[0]['c']+ $countdqr13[0]['c']+ $countdqr14[0]['c']+ $countdqr15[0]['c']+ $countdqr16[0]['c']+ $countdqr17[0]['c']+ $countdqr18[0]['c']+ $countdqr19[0]['c']+ $countdqr20[0]['c'];
		
		$count = number_format($count,2,'.','');
		$count0 = number_format($count0,2,'.','');
		$count1 = number_format($count1,2,'.','');
		$count2 = number_format($count2,2,'.','');
		$count3 = number_format($count3,2,'.','');
		$countdqr = number_format($countdqr,2,'.','');

		$set = Db::name('admin_set')->where('aid',aid)->find();
 
		// 初始化 $defaultCid，解决未定义变量问题
		$defaultCid = 0; // 或者从 $set 或其他地方获取 sensible default

		//是否显示分红订单
		$levelinfo = Db::name('member_level')->where('id',$this->member['levelid'])->find();
		$levelRecord = Db::name('member_level_record')->where('mid',mid)->select();
        $levelids = $levelRecord->column('levelid');
        $levelExtend = Db::name('member_level')->whereIn('id',$levelids);
		$showfenhong = false;
		$hasfenhong = false;
		$hasteamfenhong = false;
		$hasareafenhong = false;
        $hasYeji = false;
		if($levelinfo['fenhong'] > 0){ //有股东分红
            $showfenhong = true;
			$hasfenhong = true;
		}
        if($levelExtend->where('fenhong', '>', 0)->count()) {
            $showfenhong = true;
			$hasfenhong = true;
        }
		if($levelinfo['teamfenhonglv'] > 0 && ($levelinfo['teamfenhongbl'] > 0 || $levelinfo['teamfenhong_money'] > 0)){ //有团队分红
			$showfenhong = true;
			$hasteamfenhong = true;
		}
		if($levelinfo['jiedian_teamfenhonglv'] > 0 && ($levelinfo['jiedian_teamfenhongbl'] > 0 || $levelinfo['jiedian_teamfenhong_money'] > 0)){ //有节点团队分红
			$showfenhong = true;
			$hasteamfenhong = true;
		}
		
        if($levelinfo['product_teamfenhong_ids'] != '' && $levelinfo['product_teamfenhonglv'] > 0 && ($levelinfo['product_teamfenhong_money'] > 0)){ //有商品团队分红
            $showfenhong = true;
            $hasteamfenhong = true;
        }
        if(!empty($levelinfo['level_teamfenhong_ids']) && $levelinfo['level_teamfenhonglv'] > 0 && ($levelinfo['level_teamfenhongbl'] > 0 || $levelinfo['level_teamfenhong_money'] > 0)){ //有等级团队分红
            $showfenhong = true;
            $hasteamfenhong = true;
        }
        if(!empty($levelinfo['tongji_yeji']) && $levelinfo['tongji_yeji'] == 1) {
            $hasYeji = true;
        }
		$countfenhong = $levelExtend->where('teamfenhonglv', '>', 0)->where(function ($query) {
            $query->where('teamfenhongbl','>',0)->whereOr('teamfenhong_money','>',0);
        })->count();
        if($countfenhong) {
            $showfenhong = true;
			$hasteamfenhong = true;
        }
		if($levelinfo['areafenhong'] > 0 && $levelinfo['areafenhongbl'] > 0){ //有区域代理分红
			$showfenhong = true;
			$hasareafenhong = true;
		}
        if($levelExtend->where('areafenhong', '>', 0)->where('areafenhongbl', '>', 0)->count()) {
            $showfenhong = true;
			$hasareafenhong = true;
        }
		if($this->member['areafenhong'] > 0 && $this->member['areafenhongbl'] > 0){ //有单独设置的区域代理分红
			$showfenhong = true;
			$hasareafenhong = true;
		}
        if($levelRecord->where('areafenhong', '>', 0)->where('areafenhongbl', '>', 0)->count()) {
            $showfenhong = true;
			$hasareafenhong = true;
        }
        
        // 初始化 $showMendianOrder, 解决未定义变量问题
        $showMendianOrder = isset($set['show_mendian_order_入口']) ? $set['show_mendian_order_入口'] : false; // 假设设置项名称，如果不存在则默认为false

        //预计佣金
//		$commissionyj1 = Db::query("select sum(parent1commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2 or o.status=3) and iscommission=0 and og.parent1=".mid."");
//		$commissionyj2 = Db::query("select sum(parent2commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2 or o.status=3) and iscommission=0 and og.parent2=".mid."");
//		$commissionyj3 = Db::query("select sum(parent3commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2 or o.status=3) and iscommission=0 and og.parent3=".mid."");
		$commissionyj_collage1 = Db::name('collage_order')->where('aid',aid)->where('parent1',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent1commission');
        $commissionyj_collage2 = Db::name('collage_order')->where('aid',aid)->where('parent2',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent2commission');
        $commissionyj_collage3 = Db::name('collage_order')->where('aid',aid)->where('parent3',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent3commission');
        $commissionyj_score1 = Db::name('scoreshop_order_goods')->where('aid',aid)->where('parent1',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent1commission');
        $commissionyj_score2 = Db::name('scoreshop_order_goods')->where('aid',aid)->where('parent2',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent2commission');
        $commissionyj_score3 = Db::name('scoreshop_order_goods')->where('aid',aid)->where('parent3',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent3commission');
        //ddwx_seckill_order 不存在iscommission字段
        $commissionyj_yuyue1 = Db::name('yuyue_order')->where('aid',aid)->where('parent1',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent1commission');
        $commissionyj_yuyue2 = Db::name('yuyue_order')->where('aid',aid)->where('parent2',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent2commission');
        $commissionyj_yuyue3 = Db::name('yuyue_order')->where('aid',aid)->where('parent3',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent3commission');
        $commissionyj_ke1 = Db::name('kecheng_order')->where('aid',aid)->where('parent1',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent1commission');
        $commissionyj_ke2 = Db::name('kecheng_order')->where('aid',aid)->where('parent2',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent2commission');
        $commissionyj_ke3 = Db::name('kecheng_order')->where('aid',aid)->where('parent3',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent3commission');
        $commissionyj_tuan1 = Db::name('tuangou_order')->where('aid',aid)->where('parent1',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent1commission');
        $commissionyj_tuan2 = Db::name('tuangou_order')->where('aid',aid)->where('parent2',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent2commission');
        $commissionyj_tuan3 = Db::name('tuangou_order')->where('aid',aid)->where('parent3',mid)->whereIn('status',[1,2,3])->where('iscommission',0)->sum('parent3commission');
        //todo 其他佣金
        $recordyj = Db::name('member_commission_record')->where('aid',aid)->where('mid',mid)->where('type','<>','shop')->where('type','<>','lucky_collage')->where('status',0)->sum('commission');
        
        $lunckyuyj = Db::name('member_commission_record')->alias('r')->leftJoin('lucky_collage_order o','r.orderid=o.id')->where('r.aid',aid)
        ->where('r.mid',mid)->where('r.type','lucky_collage')->where('r.status',0)->where('o.status','in',[1,2,3])->sum('r.commission');//
        $recordyjshop = Db::name('member_commission_record')->alias('r')->leftJoin('shop_order o','r.orderid=o.id')->where('r.aid',aid)->where('r.mid',mid)->where('r.type','shop')->where('r.status',0)->where('o.status','in',[1,2,3])->sum('r.commission');
        // var_dump($recordyjshop);die;
        $commissionyj = 0 /*+ $commissionyj1[0]['c'] + $commissionyj2[0]['c'] + $commissionyj3[0]['c']*/
            + $commissionyj_collage1 + $commissionyj_collage2 + $commissionyj_collage3
            + $commissionyj_score1 + $commissionyj_score2 + $commissionyj_score3
            + $commissionyj_yuyue1 + $commissionyj_yuyue2 + $commissionyj_yuyue3
            + $commissionyj_ke1 + $commissionyj_ke2 + $commissionyj_ke3
            + $commissionyj_tuan1 + $commissionyj_tuan2 + $commissionyj_tuan3
            + $recordyj + $recordyjshop+$lunckyuyj;
		$userinfo['fenhong'] = Db::name('member_fenhonglog')->where('aid',aid)->where('mid',mid)->where('type','fenhong')->sum('commission');
		$userinfo['areafenhong'] = Db::name('member_fenhonglog')->where('aid',aid)->where('mid',mid)->where('type','areafenhong')->sum('commission');
		$userinfo['teamfenhong'] = Db::name('member_fenhonglog')->where('aid',aid)->where('mid',mid)->where('type','teamfenhong')->sum('commission');
		
		$userinfo['jiedian_teamfenhong'] = Db::name('member_fenhonglog')->where('aid',aid)->where('mid',mid)->where('type','jiedian_teamfenhong')->sum('commission');
		$userinfo['jiedian_count'] =  \app\common\Member::getdownxiaji22(1,1,[]);
//var_dump($commissionyj);die;
		$userinfo['fenhong'] = round($userinfo['fenhong'],2);
		$userinfo['areafenhong'] = round($userinfo['areafenhong'],2);
		$userinfo['teamfenhong'] = round($userinfo['teamfenhong'],2);
		$userinfo['commission_yj'] = $commissionyj;

		// 2025-01-03 22:55:53,565-INFO-[ApiAgent][commissionSurvey_001] 开始计算分红预期佣金
		$timestamp = date('Y-m-d H:i:s');
		$fenhong_total_start_time = microtime(true);
		
		// 检查缓存的分红数据是否存在且有效（5分钟内）
		$cache_key = 'fenhong_yj_' . aid . '_' . mid;
		$cached_data = cache($cache_key);
		$cache_valid = $cached_data && (time() - $cached_data['timestamp']) < 300; // 5分钟缓存
		
		if($showfenhong){
			$fenhong_start_time = microtime(true);
			
			if($cache_valid && isset($cached_data['fenhong_yj'])){
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_002] 使用缓存的股东分红数据: '.$cached_data['fenhong_yj'], 'info');
				$userinfo['fenhong_yj'] = $cached_data['fenhong_yj'];
			} else {
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_003] 开始计算股东分红预期佣金', 'info');
				$rs = \app\common\Fenhong::gdfenhong(aid,$this->sysset,[],0,time(),1,mid);
				$userinfo['fenhong_yj'] = round($rs['commissionyj'],2);
				
				// 缓存股东分红结果
				if(!is_array($cached_data)) $cached_data = [];
				$cached_data['fenhong_yj'] = $userinfo['fenhong_yj'];
				$cached_data['timestamp'] = time();
				
				$fenhong_end_time = microtime(true);
				$fenhong_time = round(($fenhong_end_time - $fenhong_start_time) * 1000, 2);
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_004] 股东分红计算完成，耗时: '.$fenhong_time.'ms，结果: '.$userinfo['fenhong_yj'], 'info');
			}
			
			$userinfo['commission_yj'] += $userinfo['fenhong_yj'];
		}
		
		if($hasteamfenhong){
			$teamfenhong_start_time = microtime(true);
			
			if($cache_valid && isset($cached_data['teamfenhong_yj'])){
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_005] 使用缓存的团队分红数据: '.$cached_data['teamfenhong_yj'], 'info');
				$userinfo['teamfenhong_yj'] = $cached_data['teamfenhong_yj'];
			} else {
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_006] 开始计算团队分红预期佣金', 'info');
				
				// 串行计算三种团队分红，但添加性能监控
				$rs1_start = microtime(true);
				$rs1 = \app\common\Fenhong::teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
				$rs1_time = round((microtime(true) - $rs1_start) * 1000, 2);
				
				$rs2_start = microtime(true);
				$rs2 = \app\common\Fenhong::product_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
				$rs2_time = round((microtime(true) - $rs2_start) * 1000, 2);
				
				$rs3_start = microtime(true);
				$rs3 = \app\common\Fenhong::level_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
				$rs3_time = round((microtime(true) - $rs3_start) * 1000, 2);
				
				$teamfenhong_yj = 0;
				if($rs1 && $rs1['oglist']){
					$teamfenhong_yj += $rs1['commissionyj'];
					\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_007] 普通团队分红: '.$rs1['commissionyj'].'，耗时: '.$rs1_time.'ms', 'info');
				}
				if($rs2 && $rs2['oglist']){
					$teamfenhong_yj += $rs2['commissionyj'];
					\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_008] 商品团队分红: '.$rs2['commissionyj'].'，耗时: '.$rs2_time.'ms', 'info');
				}
				if($rs3 && $rs3['oglist']){
					$teamfenhong_yj += $rs3['commissionyj'];
					\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_009] 等级团队分红: '.$rs3['commissionyj'].'，耗时: '.$rs3_time.'ms', 'info');
				}
				
				$userinfo['teamfenhong_yj'] = round($teamfenhong_yj,2);
				
				// 缓存团队分红结果
				if(!is_array($cached_data)) $cached_data = [];
				$cached_data['teamfenhong_yj'] = $userinfo['teamfenhong_yj'];
				$cached_data['timestamp'] = time();
			}
			
			$userinfo['commission_yj'] += $userinfo['teamfenhong_yj'];
			
			$teamfenhong_end_time = microtime(true);
			$teamfenhong_time = round(($teamfenhong_end_time - $teamfenhong_start_time) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_010] 团队分红计算完成，总耗时: '.$teamfenhong_time.'ms，结果: '.$userinfo['teamfenhong_yj'], 'info');
		}

		if($hasareafenhong){
			$areafenhong_start_time = microtime(true);
			
			if($cache_valid && isset($cached_data['areafenhong_yj'])){
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_011] 使用缓存的区域分红数据: '.$cached_data['areafenhong_yj'], 'info');
				$userinfo['areafenhong_yj'] = $cached_data['areafenhong_yj'];
			} else {
				\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_012] 开始计算区域分红预期佣金', 'info');
				$rs = \app\common\Fenhong::areafenhong(aid,$this->sysset,[],0,time(),1,mid);
				$userinfo['areafenhong_yj'] = round($rs['commissionyj'],2);
				
				// 缓存区域分红结果
				if(!is_array($cached_data)) $cached_data = [];
				$cached_data['areafenhong_yj'] = $userinfo['areafenhong_yj'];
				$cached_data['timestamp'] = time();
			}
			
			$userinfo['commission_yj'] += $userinfo['areafenhong_yj'];
			
			$areafenhong_end_time = microtime(true);
			$areafenhong_time = round(($areafenhong_end_time - $areafenhong_start_time) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_013] 区域分红计算完成，耗时: '.$areafenhong_time.'ms，结果: '.$userinfo['areafenhong_yj'], 'info');
		}
		
		// 更新缓存
		if(!$cache_valid && is_array($cached_data)){
			cache($cache_key, $cached_data, 300); // 缓存5分钟
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_014] 分红数据已缓存，缓存键: '.$cache_key, 'info');
		}
		
		$fenhong_total_end_time = microtime(true);
		$fenhong_total_time = round(($fenhong_total_end_time - $fenhong_total_start_time) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_015] 所有分红计算完成，总耗时: '.$fenhong_total_time.'ms', 'info');
		
		$userinfo['commission_yj'] = round($userinfo['commission_yj'],2);

		// 2025-01-03 22:55:53,565-INFO-[ApiAgent][commissionSurvey_001] Calculating total and current month commissions
		// 计算总累计佣金 - 只统计正数佣金（收入）
		$total_commission_earned = 0 + Db::name('member_commissionlog')
		    ->where('aid', aid)
		    ->where('mid', mid)
		    ->where('commission', '>', 0)  // 只统计正数佣金
		    ->sum('commission');

		// 计算当月佣金 - 优化版本：分别统计收入和转出
		$current_month_start = strtotime(date('Y-m-01 00:00:00'));
		$current_month_end = strtotime(date('Y-m-t 23:59:59'));

		// 当月收益 - 只统计正数佣金（收入）
		$current_month_commission = 0 + Db::name('member_commissionlog')
		    ->where('aid', aid)
		    ->where('mid', mid)
		    ->where('createtime', '>=', $current_month_start)
		    ->where('createtime', '<=', $current_month_end)
		    ->where('commission', '>', 0)  // 只统计正数佣金
		    ->sum('commission');

		// 获取当月所有佣金记录用于详细统计
		$commission_records = Db::name('member_commissionlog')
		    ->where('aid', aid)
		    ->where('mid', mid)
		    ->where('createtime', '>=', $current_month_start)
		    ->where('createtime', '<=', $current_month_end)
		    ->field('commission,remark')
		    ->select()
		    ->toArray();

		// 分别统计不同类型的佣金（用于详细信息）
		$current_month_income = 0;      // 当月收入佣金
		$current_month_transfer = 0;    // 当月转出佣金（佣金转余额等）
		$current_month_other_deduct = 0; // 当月其他扣除

		foreach ($commission_records as $record) {
		    if ($record['commission'] > 0) {
		        // 正值：收入佣金
		        $current_month_income += $record['commission'];
		    } elseif ($record['commission'] < 0) {
		        // 负值：区分转出和扣除
		        if (strpos($record['remark'], '佣金转余额') !== false) {
		            $current_month_transfer += abs($record['commission']);
		        } else {
		            $current_month_other_deduct += abs($record['commission']);
		        }
		    }
		}

		// 添加日志：记录佣金详细情况
		\think\facade\Log::write('佣金统计 - 用户ID:'.mid.' 总收益:'.$total_commission_earned.' 当月收益:'.$current_month_commission.' 当月转出:'.$current_month_transfer.' 当月其他扣除:'.$current_month_other_deduct, 'info');

		// 2025-01-03 22:56:53,565-INFO-[ApiAgent][commissionSurvey_002] Calculating today's commission
		// 计算当日佣金 - 只统计正数佣金（收入）
		$today_start = strtotime(date('Y-m-d 00:00:00'));
		$today_end = strtotime(date('Y-m-d 23:59:59'));
		$today_commission = 0 + Db::name('member_commissionlog')
		    ->where('aid', aid)
		    ->where('mid', mid)
		    ->where('createtime', '>=', $today_start)
		    ->where('createtime', '<=', $today_end)
		    ->where('commission', '>', 0)  // 只统计正数佣金
		    ->sum('commission');

		// 获取当日所有佣金记录用于详细统计
		$today_records = Db::name('member_commissionlog')
		    ->where('aid', aid)
		    ->where('mid', mid)
		    ->where('createtime', '>=', $today_start)
		    ->where('createtime', '<=', $today_end)
		    ->field('commission,remark')
		    ->select()
		    ->toArray();

		$today_income = 0;
		$today_transfer = 0;
		$today_other_deduct = 0;

		foreach ($today_records as $record) {
		    if ($record['commission'] > 0) {
		        $today_income += $record['commission'];
		    } elseif ($record['commission'] < 0) {
		        if (strpos($record['remark'], '佣金转余额') !== false) {
		            $today_transfer += abs($record['commission']);
		        } else {
		            $today_other_deduct += abs($record['commission']);
		        }
		    }
		}

		if($set['teamyeji_show']==1){ //团队业绩
			// 性能监控：团队业绩计算开始
			$step_start = microtime(true);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_005] 开始团队业绩计算', 'info');

			//总业绩
			//$levelids = Db::name('member_level')->where('aid',aid)->where('sort','<',$levelinfo['sort'])->column('id');
			$levelids = Db::name('member_level')->where('aid',aid)->column('id');
			$yejiwhere = [];
			//$yejiwhere[] = ['isfenhong','=',0];
			//if($set['fhjiesuantime_type'] == 1){
				$yejiwhere[] = ['status','in','1,2,3'];
			//}else{
			//	$yejiwhere[] = ['status','=','3'];
			//}
			$totalyeji = 0;
			$levelids = Db::name('member_level')->where('aid',aid)->where('id','>=',2)->column('id');

			// 性能监控：获取团队成员ID
			$team_start = microtime(true);
			$downmids = \app\common\Member::getteammids(aid,mid,999,$levelids);
			$team_time = round((microtime(true) - $team_start) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_006] 获取团队成员ID完成，耗时: '.$team_time.'ms，成员数量: '.count($downmids), 'info');
// 			var_dump($downmids);

			// 计算商品订单的业绩
			$shop_start = microtime(true);
			$teamyeji_shop = Db::name('shop_order_goods')->where('aid',aid)->where('mid','in',$downmids)->where($yejiwhere)->sum('totalprice');
			$shop_time = round((microtime(true) - $shop_start) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_007] 商品订单业绩计算完成，耗时: '.$shop_time.'ms，业绩: '.$teamyeji_shop, 'info');

			// 计算会员升级订单的业绩
			$levelup_start = microtime(true);
			$levelup_where = [];
			$levelup_where[] = ['aid','=',aid];
			$levelup_where[] = ['mid','in',$downmids];
			$levelup_where[] = ['status','in','1,2'];  // 会员升级订单状态：1-已支付待审核，2-已完成
			$teamyeji_levelup = Db::name('member_levelup_order')->where($levelup_where)->sum('totalprice');
			$levelup_time = round((microtime(true) - $levelup_start) * 1000, 2);
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_008] 升级订单业绩计算完成，耗时: '.$levelup_time.'ms，业绩: '.$teamyeji_levelup, 'info');

			// 团队总业绩 = 商品订单业绩 + 会员升级订单业绩
			$teamyeji = $teamyeji_shop + $teamyeji_levelup;

			$step_time = round((microtime(true) - $step_start) * 1000, 2);
			// 记录计算过程日志，便于调试和验证
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey_009] 团队业绩计算完成，总耗时: '.$step_time.'ms - 用户ID:'.mid.' 商品订单业绩:'.$teamyeji_shop.' 升级订单业绩:'.$teamyeji_levelup.' 总业绩:'.$teamyeji, 'info');
// 			echo DB::getLastsql();
// 			var_dump($teamyeji);die;
		}
		
		if($set['teamnum_show']==1){ //团队总人数
			if($set['teamyeji_show']!=1){
				// 如果没有计算团队业绩，需要重新获取团队成员ID列表
				$levelids = Db::name('member_level')->where('aid',aid)->where('id','>=',2)->column('id');
				$downmids = \app\common\Member::getteammids(aid,mid,999,$levelids);
			}
			// 团队总人数就是符合条件的团队成员数量
			$teamnum = count($downmids);
		}

		$gongxianfenhong_show = 0;
		if($set['gongxianfenhong_show'] == 1){
			$pergxcommon = 0;
			if($set['partner_gongxian']==1 && $levelinfo['fenhong'] > 0 && $levelinfo['fenhong_gongxian_percent'] > 0){
				$gongxianfenhong_show = 1;

				
				$orderwhere = [];
				$orderwhere[] = ['aid','=',aid];
				$orderwhere[] = ['isfenhong','=',0];
				//if($set['fhjiesuantime_type'] == 1){
				//	$orderwhere[] = ['status','in','1,2,3'];
				//}else{
				//	$orderwhere[] = ['status','=','3'];
				//}
				$orderwhere[] = ['status','in','1,2,3'];
				$real_totalprice = Db::name('shop_order_goods')->where($orderwhere)->sum('real_totalprice');
				if($set['fhjiesuantime_type'] == 0){
					$fenhongprice = $real_totalprice;
				}else{
					$cost_price = Db::name('shop_order_goods')->where($orderwhere)->sum('cost_price');
					$num = Db::name('shop_order_goods')->where($orderwhere)->sum('num');
					$fenhongprice = $real_totalprice - $cost_price * $num;
				}

				$fhlevellist = Db::name('member_level')->where('aid',aid)->where('fenhong','>','0')->order('sort desc,id desc')->column('id,cid,name,fenhong,fenhong_num,fenhong_max_money,sort,fenhong_gongxian_minyeji,fenhong_gongxian_percent','id');
				$lastmidlist = [];
				$total_fenhong_partner = $this->member['total_fenhong_partner'];
				$commission = 0;
				foreach($fhlevellist as $fhlevel){
					$where = [];
					$where[] = ['aid', '=', aid];
					$where[] = ['levelid', '=', $fhlevel['id']];
					$where2 = [];
					$where2[] = ['ml.aid', '=', aid];
					$where2[] = ['ml.levelid', '=', $fhlevel['id']];
					if ($fhlevel['fenhong_max_money'] > 0) {
						$where[] = ['total_fenhong_partner', '<', $fhlevel['fenhong_max_money']];
						$where2[] = ['m.total_fenhong_partner', '<', $fhlevel['fenhong_max_money']];
					}
					if ($defaultCid > 0 && $defaultCid != $fhlevel['cid']) {

					} else {
						if ($fhlevel['fenhong_num'] > 0) {
							$midlist = Db::name('member')->where($where)->order('levelstarttime,id')->limit(intval($fhlevel['fenhong_num']))->column('id,total_fenhong_partner', 'id');
						} else {
							$midlist = Db::name('member')->where($where)->column('id,total_fenhong_partner', 'id');
						}
					}
					
					if($this->sysset['partner_jiaquan'] == 1){
						$oldmidlist = $midlist;
						$midlist = array_merge((array)$lastmidlist,(array)$midlist);
						$lastmidlist = array_merge((array)$lastmidlist,(array)$oldmidlist);
					}

					if (!$midlist) continue;
					
					//股东贡献量分红 开启后可设置一定比例的分红金额按照股东的团队业绩量分红
					$pergxcommon = 0;
					if($this->sysset['partner_gongxian']==1 && $fhlevel['fenhong_gongxian_percent'] > 0){
						$gongxian_percent = $fhlevel['fenhong'] * $fhlevel['fenhong_gongxian_percent']*0.01;
						$fhlevel['fenhong'] = $fhlevel['fenhong'] * (1 - $fhlevel['fenhong_gongxian_percent']*0.01);
						$gongxianCommissionTotal = $gongxian_percent * $fenhongprice * 0.01;
						//总业绩
						//$levelids = Db::name('member_level')->where('aid',aid)->where('sort','<',$fhlevel['sort'])->column('id');
						$levelids = Db::name('member_level')->where('aid',aid)->column('id');
						$yejiwhere = [];
						$yejiwhere[] = ['isfenhong','=',0];
						//if($fhjiesuantime_type == 1) {
							$yejiwhere[] = ['status','in','1,2,3'];
						//}else{
						//	$yejiwhere[] = ['status','=','3'];
						//}
						$totalyeji = 0;
						foreach($midlist as $kk=>$item){
							$downmids = \app\common\Member::getteammids(aid,$item['id'],999,$levelids);
							$yeji = Db::name('shop_order')->where('aid',aid)->where('mid','in',$downmids)->where($yejiwhere)->sum('totalprice');
							$midlist[$kk]['yeji'] = $yeji;
							$totalyeji += $yeji;
						}
						if($totalyeji > 0){
							$pergxcommon = $gongxianCommissionTotal / $totalyeji;
						}else{
							$pergxcommon = 0;
						}
					}

					$fenhongmoney = $fhlevel['fenhong'] * $fenhongprice * 0.01 / count($midlist);//平均分给此等级的会员
					foreach($midlist as $item){
						if($item['id'] == mid){
							$gxcommon = 0;
							if($pergxcommon > 0){
								if($item['yeji'] >= $fhlevel['fenhong_gongxian_minyeji']){
									$gxcommon = $item['yeji'] * $pergxcommon;
								}
							}
							$commission += $gxcommon;
							if ($fhlevel['fenhong_max_money'] > 0) {
								if ($fenhongmoney + $total_fenhong_partner > $fhlevel['fenhong_max_money']) {
									$fenhongmoney = $fhlevel['fenhong_max_money'] - $total_fenhong_partner;
								}
								$total_fenhong_partner += $fenhongmoney;//总分红增加
							}
							//$commission += $fenhongmoney;
							break;
						}
					}
				}

				$gongxianfenhong = $commission;
			}
		}
		


		//卡片
        $rsset = [];
        $rsset['parent_show'] = $set['parent_show'];
        $rsset['teamfenhong_show'] = $set['teamfenhong_show'];
        $rsset['commissionlog_show'] = $set['commissionlog_show'];
        $rsset['commissionrecord_show'] = $set['commissionrecord_show'];
        $rsset['fhorder_show'] = $set['fhorder_show'];
        $rsset['fhlog_show'] = $set['fhlog_show'];
        $rsset['distribution_abc_yeji_show'] = $set['distribution_abc_yeji_show'];
        $rsset['team_list_yeji_show'] = $set['team_list_yeji_show'];
        $rsset['team_list_relation_show'] = $set['team_list_relation_show'];
        $rsset['team_list_commission_show'] = $set['team_list_commission_show'];
        $rsset['team_list_member_count_show'] = $set['team_list_member_count_show'];
        $rsset['my_order_performance_show'] = $set['my_order_performance_show'] ?? 1; // 我的单数业绩显示控制，默认显示
        $rsset['my_earnings_show'] = $set['my_earnings_show'] ?? 1; // 我的收益显示控制，默认显示

		$rdata = [];
		$rdata['total_commission_earned'] = round(floatval($total_commission_earned),2); // 总累计佣金
		$rdata['current_month_commission'] = round(floatval($current_month_commission),2); // 当月佣金
		$rdata['today_commission'] = round(floatval($today_commission),2); // 当日佣金

		// 添加详细的佣金统计信息，帮助用户理解佣金构成
		$rdata['current_month_income'] = round(floatval($current_month_income),2); // 当月收入佣金
		$rdata['current_month_transfer'] = round(floatval($current_month_transfer),2); // 当月转出佣金（佣金转余额）
		$rdata['current_month_other_deduct'] = round(floatval($current_month_other_deduct),2); // 当月其他扣除
		$rdata['today_income'] = round(floatval($today_income),2); // 当日收入佣金
		$rdata['today_transfer'] = round(floatval($today_transfer),2); // 当日转出佣金（佣金转余额）
		$rdata['today_other_deduct'] = round(floatval($today_other_deduct),2); // 当日其他扣除

		// 2025-01-03 22:59:53,565-INFO-[ApiAgent][commissionSurvey_003] 计算销售件数统计
		// 获取团队所有成员ID（包括自己）
		$levelids = Db::name('member_level')->where('aid', aid)->where('id', '>=', 2)->column('id');
		$downmids = \app\common\Member::getteammids(aid, mid, 999, $levelids);
		$downmids[] = mid; // 包含自己

		// 计算总销售件数
		$total_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->sum('num');

		// 计算当月销售件数
		$current_month_start = strtotime(date('Y-m-01 00:00:00'));
		$current_month_end = strtotime(date('Y-m-t 23:59:59'));
		$current_month_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $current_month_start)
			->where('createtime', '<=', $current_month_end)
			->sum('num');

		// 计算当日销售件数
		$today_start = strtotime(date('Y-m-d 00:00:00'));
		$today_end = strtotime(date('Y-m-d 23:59:59'));
		$today_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $today_start)
			->where('createtime', '<=', $today_end)
			->sum('num');

		$rdata['total_sales_count'] = intval($total_sales_count); // 总销售件数
		$rdata['current_month_sales_coun'] = intval($current_month_sales_count); // 当月销售件数
		$rdata['today_sales_count'] = intval($today_sales_count); // 当日销售件数

		// 2025-01-04 新增：计算当月业绩统计
		// 计算当月商品订单业绩
		$current_month_teamyeji_shop = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $current_month_start)
			->where('createtime', '<=', $current_month_end)
			->sum('totalprice');

		// 计算当月会员升级订单业绩
		$current_month_levelup_where = [];
		$current_month_levelup_where[] = ['aid','=',aid];
		$current_month_levelup_where[] = ['mid','in',$downmids];
		$current_month_levelup_where[] = ['status','in','1,2'];  // 会员升级订单状态：1-已支付待审核，2-已完成
		$current_month_levelup_where[] = ['createtime','>=', $current_month_start];
		$current_month_levelup_where[] = ['createtime','<=', $current_month_end];
		$current_month_teamyeji_levelup = Db::name('member_levelup_order')->where($current_month_levelup_where)->sum('totalprice');

		// 当月团队总业绩 = 当月商品订单业绩 + 当月会员升级订单业绩
		$current_month_teamyeji = $current_month_teamyeji_shop + $current_month_teamyeji_levelup;

		// 2025-01-04 新增：计算季度业绩统计
		// 获取当前季度的开始和结束时间
		$current_quarter = ceil(date('n') / 3); // 获取当前季度（1-4）
		$current_year = date('Y');
		$quarter_start_month = ($current_quarter - 1) * 3 + 1; // 季度开始月份
		$quarter_end_month = $current_quarter * 3; // 季度结束月份
		$quarter_start = strtotime($current_year . '-' . sprintf('%02d', $quarter_start_month) . '-01 00:00:00');
		$quarter_end = strtotime($current_year . '-' . sprintf('%02d', $quarter_end_month) . '-' . date('t', strtotime($current_year . '-' . sprintf('%02d', $quarter_end_month) . '-01')) . ' 23:59:59');

		// 计算季度商品订单业绩
		$quarter_teamyeji_shop = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $quarter_start)
			->where('createtime', '<=', $quarter_end)
			->sum('totalprice');

		// 计算季度会员升级订单业绩
		$quarter_levelup_where = [];
		$quarter_levelup_where[] = ['aid','=',aid];
		$quarter_levelup_where[] = ['mid','in',$downmids];
		$quarter_levelup_where[] = ['status','in','1,2'];  // 会员升级订单状态：1-已支付待审核，2-已完成
		$quarter_levelup_where[] = ['createtime','>=', $quarter_start];
		$quarter_levelup_where[] = ['createtime','<=', $quarter_end];
		$quarter_teamyeji_levelup = Db::name('member_levelup_order')->where($quarter_levelup_where)->sum('totalprice');

		// 季度团队总业绩 = 季度商品订单业绩 + 季度会员升级订单业绩
		$quarter_teamyeji = $quarter_teamyeji_shop + $quarter_teamyeji_levelup;

		// 计算季度总单数（商品订单数量）
		$quarter_total_orders = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $quarter_start)
			->where('createtime', '<=', $quarter_end)
			->sum('num');

		// 记录业绩计算过程日志，便于调试和验证
		\think\facade\Log::write('业绩统计计算详情 - 用户ID:'.mid.' 当月商品业绩:'.$current_month_teamyeji_shop.' 当月升级业绩:'.$current_month_teamyeji_levelup.' 当月总业绩:'.$current_month_teamyeji.' 季度商品业绩:'.$quarter_teamyeji_shop.' 季度升级业绩:'.$quarter_teamyeji_levelup.' 季度总业绩:'.$quarter_teamyeji.' 季度总单数:'.$quarter_total_orders);

		// 将计算结果添加到返回数据中
		$rdata['current_month_teamyeji'] = round(floatval($current_month_teamyeji), 2); // 当月团队业绩
		$rdata['quarter_teamyeji'] = round(floatval($quarter_teamyeji), 2); // 季度团队业绩
		$rdata['quarter_total_orders'] = intval($quarter_total_orders); // 季度总单数

		$rdata['count'] = $count;
		$rdata['count1'] = $count1;
		$rdata['count2'] = $count2;
		$rdata['count3'] = $count3;
		$rdata['count0'] = $count0;
		$rdata['countdqr'] = $countdqr;
		$rdata['comwithdraw'] = $set['comwithdraw'];
		$rdata['comwithdrawmin'] = $set['comwithdrawmin'];
		$rdata['commission2money'] = $set['commission2money'];
		$rdata['commission2money_deduct_gongxianzhi'] = isset($set['commission2money_deduct_gongxianzhi']) ? $set['commission2money_deduct_gongxianzhi'] : 0;
		$rdata['commission2money_gongxianzhi_rate'] = isset($set['commission2money_gongxianzhi_rate']) ? $set['commission2money_gongxianzhi_rate'] : 100;
		$rdata['fxjiesuantime'] = $set['fxjiesuantime'];
		$rdata['showfenhong'] = $showfenhong;
		$rdata['hasfenhong'] = $hasfenhong;
		$rdata['hasteamfenhong'] = $hasteamfenhong;
		$rdata['hasareafenhong'] = $hasareafenhong;
        $rdata['showMendianOrder'] = $showMendianOrder;
        $rdata['hasYeji'] = $hasYeji;
        $rdata['teamyeji_show'] = $set['teamyeji_show'];
        $rdata['set'] = $rsset;
        
		if($rdata['teamyeji_show'] == 1){
			$userinfo['teamyeji'] = round($teamyeji,2);
		}
        $rdata['teamnum_show'] = $set['teamnum_show'];
// 		if($rdata['teamnum_show'] == 1){
			$userinfo['teamnum'] = $teamnum;
// 		}
        $rdata['gongxianfenhong_show'] = $gongxianfenhong_show;
		if($rdata['gongxianfenhong_show']==1){
			$userinfo['gongxianfenhong'] = round($gongxianfenhong,2);
			$userinfo['gongxianfenhong_txt'] = $set['gongxianfenhong_txt'];
		}
		$userinfo['commissiontuanA'] = \app\common\Member::gettanduiA(aid,mid);
		$userinfo['commissiontuanB'] = \app\common\Member::gettanduiB(aid,mid);
		$userinfo['commissiontuanC'] = \app\common\Member::gettanduiC(aid,mid);
        $userinfo['countjiandian'] =0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%见点奖%')->sum('commission');
        $userinfo['countprofenh'] =0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%商品分红%')->sum('commission');
        //商品团队分红
        $where = [['order.aid','=',aid]];
	   	$where[] = ['good.fenhongstatus','=',0];
	    if($set['fxjiesuantime'] == 1)
	    {
	        //1付款后结算
	        $where[] = ['order.status','>=',1];
	        $where[] = ['order.status','<>',4];
	    }else{
	        //0确认收货结算
	        $where[] = ['order.status','=',3];
	    }
	    //获取分红的订单
	    $ordercount = Db::name('shop_order')->alias('order')->where($where)->join('shop_order_goods good','order.id=good.orderid')->field('order.id,good.id as ordergoodsid,good.fenhongbili,good.totalprice')->select()->toArray();
	    $countstr = 0;
	    //计算分红的金额
	    foreach($ordercount as $v)
	    {
	        $countstr = $countstr+($v['totalprice']*$v['fenhongbili']/100);
	    }
	     //获取我的分红的订单
	    $ordercount2 = Db::name('shop_order')->alias('order')->where($where)->where('order.mid',mid)->join('shop_order_goods good','order.id=good.orderid')->field('order.id,good.id as ordergoodsid,good.fenhongbili,good.totalprice')->select()->toArray();
	    $countstr2 = 0;
	    //计算分红的金额
	    foreach($ordercount2 as $v)
	    {
	        $countstr2 = $countstr2+($v['totalprice']*$v['fenhongbili']/100);
	    }
	    //获取我的已分红的订单
	    $where = [['order.aid','=',aid]];
	   	$where[] = ['good.fenhongstatus','=',1];
	    if($set['fxjiesuantime'] == 1)
	    {
	        //1付款后结算
	        $where[] = ['order.status','>=',1];
	        $where[] = ['order.status','<>',4];
	    }else{
	        //0确认收货结算
	        $where[] = ['order.status','=',3];
	    }
	    $ordercount3 = Db::name('shop_order')->alias('order')->where($where)->where('order.mid',mid)->join('shop_order_goods good','order.id=good.orderid')->field('order.id,good.id as ordergoodsid,good.fenhongbili,good.totalprice')->select()->toArray();
	    $countstr3 = 0;
	    //计算分红的金额
	    foreach($ordercount3 as $v)
	    {
	        $countstr3 = $countstr3+($v['totalprice']*$v['fenhongbili']/100);
	    }
	    $userinfo['pingtaigoodsfenh'] = $countstr;
	    $userinfo['mygoodsfenh'] = $countstr2;
	    $userinfo['mygoodsfenhyi'] = $countstr3;
	    $userinfo['scorehuang'] = $this->member['scorehuang'];
	    $userinfo['scorexiaofeizhi'] = $this->member['scorexiaofeizhi'];
	    $userinfo['scorelv'] = $this->member['scorelv'];
	    $userinfo['scorelvjiazhi'] = $set['lvjifenjiazhi'];
	     $userinfo['scorehuangyuji'] = 0;
	     
	     $signset = Db::name('signset')->where('aid',aid)->order('id desc')->find();
	     //签到的分红点
	     $userinfo['qd']=array();
	     $userinfo['qd']['z_nums']=$userinfo['fanxian_nums'];
	     //计算出未分红的点数
	     //$order_dis=Db::name('shop_order')->where('aid',aid)->where('mid',mid)->field('sum(totalprice) as nums')->find();
	     $order_dis_nums=Db::name('shop_order_goods')->where('status','in','1,2,3')->where('aid',aid)->where('mid',mid)->where('is_yeji','1')->sum('real_totalprice');
	     //$userinfo['qd']['w_nums']=round($order_dis_nums-$userinfo['fanxian_nums'],2);
	     //参数分红的订单金额计算
	     $z_totalpric=$order_dis_nums-(floor($userinfo['fanxian_nums']/$signset['order_fx_nums']))*$signset['order_fx_nums'];
	     if($signset['order_fx_nums'] == 0)
	     {
	         $userinfo['qd']['fx_nums']=floor($z_totalpric/$signset['order_fx_nums']);
	         $userinfo['qd']['w_nums']=((floor($order_dis_nums/$signset['order_fx_nums'])*$signset['order_fx_nums'])-$userinfo['fanxian_nums']);
	         $userinfo['qd']['fx_nums']=0;
	         $userinfo['qd']['w_nums']= 0;
	     }
	     
	     
         if($signset['scorehuang'] >0)
            {
                $scorehuang = \app\common\Member::getscorehuang($this->member);
                if($scorehuang >0)
                {
                   $shifang = $scorehuang*$signset['scorehuang']*0.01;
                   $userinfo['scorehuangyuji'] = $shifang;
                }
            }else{
                 $userinfo['scorehuangyuji'] = 0;
            }
	     $userinfo['commissionzongliang'] = 21000000;
	     
	     //积分释放 remark 消费值获取绿积分  SELECT * FROM `ddwx_member_scoreloglv` WHERE remark LIKE '%消费值获取绿积分%'
	     $datalist22 = Db::name('member_scoreloglv')->field("id,score,from_unixtime(createtime)createtime")->where('aid','=',aid)->where('remark','like','%消费值获取绿积分%')->select()->toArray();
	      $scorelvshifang = 0;
        foreach($datalist22 as $k=>$v)
        {
            $scorelvshifang = $scorelvshifang+$v['score'];
        }
        $userinfo['scorelvshifang22'] = round($scorelvshifang,2);
	     //剩余积分释放
	     $userinfo['scorelvshifangshengyu22'] = $userinfo['commissionzongliang']  - $userinfo['scorelvshifang22'];
	     //绿积分释放今日
	     $day = date('Y-m-d');
	    $memberAll = Db::name('member')->field('id,scorexiaofeizhi')->where('aid',aid)->select()->toArray();
	    $scorexiaofeizhicount22 = 0;
        foreach($memberAll as $k=>$v)
        {
            $scorexiaofeizhicount22 = $scorexiaofeizhicount22+$v['scorexiaofeizhi'];
        }
        if($scorexiaofeizhicount22 != 0){
             $fanlvjifen = $this->member['scorexiaofeizhi']/$scorexiaofeizhicount22*$set['xiaofeizhijiazhi'];
            $userinfo['fanlvjifen'] = round($fanlvjifen,2);
        }else{
            $userinfo['fanlvjifen'] =0;
        }
	    $where1= [];
		$where1[] = ['aid','=',aid];
		//昨日销毁的绿积分
		//昨天时间戳
        $starttime1 = strtotime(date('Y-m-d 00:00:00',time()-3600*24));
        $endtime1 = strtotime(date('Y-m-d 23:59:59',time()-3600*24));
        $datalist2 = Db::name('zhuanxiaofeizhi_log')->field("id,money,phone,from_unixtime(createtime)createtime")->where($where1)->where('createtime','>=',$starttime1)->where('createtime','<=',$endtime1)->where('money','like','%-%')->select()->toArray();
        $scorexiaofeizhicountlvzuo = 0;
        foreach($datalist2 as $k=>$v)
        {
            $scorexiaofeizhicountlvzuo = $scorexiaofeizhicountlvzuo+abs($v['money']);
        }
        $userinfo['scorexiaofeizhicountlvzuo'] = round($scorexiaofeizhicountlvzuo,2);
		 //今日销毁的绿积分
		 $starttime = strtotime(date('Y-m-d 00:00:00',time()));
		 $endtime = strtotime(date('Y-m-d 23:59:59',time()));
		$datalist2 = Db::name('zhuanxiaofeizhi_log')->field("id,money,phone,from_unixtime(createtime)createtime")->where($where1)->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->where('money','like','%-%')->select()->toArray();
        $scorexiaofeizhicountlvjin = 0;
        foreach($datalist2 as $k=>$v)
        {
            $scorexiaofeizhicountlvjin = $scorexiaofeizhicountlvjin+abs($v['money']);
        }
        $userinfo['scorexiaofeizhicountlvjin'] = round($scorexiaofeizhicountlvjin,2);
       
		//总的销毁
	    $datalist2 = Db::name('zhuanxiaofeizhi_log')->field("id,money,phone,from_unixtime(createtime)createtime")->where($where1)->where('money','like','%-%')->select()->toArray();
        $scorexiaofeizhicountlv2 = 0;
        foreach($datalist2 as $k=>$v)
        {
            $scorexiaofeizhicountlv2 = $scorexiaofeizhicountlv2+abs($v['money']);
        }
        $userinfo['scorelvranshao'] = round($scorexiaofeizhicountlv2,2);
        $userinfo['zhituiyeji'] = 0;
        $memberlist = Db::name('member')->field('id')->where('aid',aid)->where('pid',$this->member['id'])->order('createtime','asc')->select()->toArray();
        $memberids = array_column($memberlist,'id');
        $myxiajistr = implode(',',$memberids);
         // kfj_shengyu
        $myxiajiorder= Db::name('shop_order')->where('aid',aid)->field('id,kfj_shengyu')->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->where('kfj_shengyu','>',0)->select()->order('id','desc')->toArray();
        $kfj_shengyu = 0;
        foreach($myxiajiorder as $k=>$v)
        {
           $kfj_shengyu = $kfj_shengyu+ $v['kfj_shengyu'];
        }
        $userinfo['zhituiyeji'] = $kfj_shengyu;
        //zhituiyeji
        $memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
        $userinfo['kfj_jiangli'] = '直推下级所有业绩每达到'.$memberlevel['kfj_yeji'].'元,奖励'.$memberlevel['kfj_jiangli'].'元';
        $userinfo['kfj'] = 0;
         $userinfo['pyj']  = Db::name('member_commissionlog')->field("id,commission money,`after`,from_unixtime(createtime)createtime,remark")->where($where1)->where('mid',mid)->where('commission','>',0)->where('remark','like','%开发奖%')->sum('commission');
        //peuyuj
        $userinfo['pyj'] = 0;
        $userinfo['pyj']  = Db::name('member_commissionlog')->field("id,commission money,`after`,from_unixtime(createtime)createtime,remark")->where($where1)->where('mid',mid)->where('commission','>',0)->where('remark','like','%培育奖%')->sum('commission');
        $userinfo['cyjjyong'] = 0;
        //创业基金余额 创业基金佣金 
        $userinfo['cyjjyong']  = Db::name('member_commissionlog')->field("id,commission money,`after`,from_unixtime(createtime)createtime,remark")->where($where1)->where('mid',mid)->where('commission','>',0)->where('remark','like','%创业基金%')->sum('commission');
        
        // $userinfo['cyjjyong'] = 0;
        // //创业基金余额 创业基金佣金 
        // $userinfo['cyjjyong']  = Db::name('member_commissionlog')->field("id,commission money,`after`,from_unixtime(createtime)createtime,remark")->where($where1)->where('mid',mid)->where('commission','>',0)->where('remark','like','%创业基金%')->sum('commission');
        // jl_type
        //动态奖统计
        $where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
        $userinfo['dongtaizong'] = Db::name('member_commission_record')->where($where)->where('jl_type',1)->sum('commission');
        $userinfo['dongtaiyi'] = Db::name('member_commission_record')->where($where)->where('jl_type',1)->where('status',1)->sum('commission');
        $userinfo['dongtaishengy'] = Db::name('member_commission_record')->where($where)->where('jl_type',1)->where('status',0)->sum('commission');
        //静态奖统计
        $userinfo['jingtaiyi'] = Db::name('member_commission_record')->where($where)->where('jl_type',3)->where('status',1)->sum('commission');
        $userinfo['jingtaidai'] = Db::name('member_commission_record')->where($where)->where('jl_type',3)->where('status',0)->sum('commission');
        $userinfo['bustotal'] =  \app\common\Member::getscorebus(mid);
        $userinfo['heiscore'] =  \app\common\Member::getscoheijifen(mid);
        // var_dump($userinfo);
        $signset =  Db::name('signset')->where('aid',aid)->order('id desc')->find();

        if($signset['jiangfenh'] >0 && $this->member['bus_total']>0)
        {
             $userinfo['qiandaofh'] = \app\common\Member::getqianfh(aid,mid,$signset);
             $userinfo['fhdian'] = \app\common\Member::getfhdian3(aid,mid,$signset,$userinfo['fenhong_num']);
        }else{
            $userinfo['qiandaofh'] =0;
            $userinfo['fhdian'] = 0;
        }
        $userlevelArr = Db::name('member_level')->where('aid',aid)->where('id',$userinfo['levelid'])->find();
        $idsxiaji = \app\common\Member::getdownmids(aid,mid,1,$lv['up_fxorderlevelid_two']);
        $idsxiajiyeji = Db::name('shop_order_goods')->where('status','in','1,2,3')->where('aid',aid)->where('mid','in',$idsxiaji)->where('is_yeji','1')->sum('real_totalprice');
        $idsxiajimy = Db::name('shop_order_goods')->where('status','in','1,2,3')->where('aid',aid)->where('mid',mid)->where('is_yeji','1')->sum('real_totalprice');
         $directProductCount = Db::name('shop_order_goods')->where('status','in','1,2,3')->where('aid',aid)->where('mid','in',$idsxiaji)->sum('num');
        	// 获取当前月份的开始和结束时间
        $startOfMonth = strtotime(date('Y-m-01 00:00:00'));
        $endOfMonth = strtotime(date('Y-m-t 23:59:59'));
        
        // 统计当前月内下级成员的商品购买数量
        $directProductCount = Db::name('shop_order_goods')
            ->where('status', 'in', [1, 2, 3]) // 仅统计有效订单
            ->where('aid', aid)
            ->where('mid', 'in', $idsxiaji) // 使用下级成员的 ID 列表
            ->whereBetween('createtime', [$startOfMonth, $endOfMonth]) // 限制在当前月内
            ->sum('num'); // 统计购买的商品数量
// 获取当前月份的开始和结束时间
$startOfMonth = strtotime(date('Y-m-01 00:00:00'));
$endOfMonth = strtotime(date('Y-m-t 23:59:59'));

// 统计当前月内下级成员的商品购买数量
$dangyuedirectProductCount = Db::name('shop_order_goods')
    ->where('status', 'in', [1, 2, 3]) // 仅统计有效订单
    ->where('aid', aid)
    ->where('mid', 'in', $idsxiaji) // 使用下级成员的 ID 列表
    ->whereBetween('createtime', [$startOfMonth, $endOfMonth]) // 限制在当前月内
    ->sum('num');

//var_dump($directProductCount);die;
        //我的业绩
        $userinfo['yeji']['my']= $idsxiajimy;
         $userinfo['yeji']['shuliang']= $directProductCount;
          $userinfo['yeji']['dangyueshuliang']= $dangyuedirectProductCount;
        //获取我的团队的id
        //我的大区业绩
        $userinfo['yeji']['dq']=0;
        //我的小区业绩
        $userinfo['yeji']['xq']=0;
        //全队总业绩
        $userinfo['yeji']['z']=0;
        //升级团队业绩
        $userinfo['yeji']['sj']=0;
        // $my_z_id=Db::name('member')->field('id')->where('pid',mid)->select()->toArray();
       
            //  $my_id_arr=array_column($my_z_id,'id');
            //  $my_id_str=implode(',',$idsxiaji);
            //  var_dump($my_id_str);die;
            
               $mid=mid;
               $idsxiaji[] = $mid;
               $performanceList = [];
//  var_dump($idsxiaji);die;
                    foreach ( $idsxiaji as $subordinateMid) {
                        // var_dump($subordinateMid);die;
                        if ($subordinateMid == $mid) {
                            // 如果是自己的ID，只计算自己的业绩
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', $mid)
                                ->sum('real_totalprice');
                        } else {
                            // 否则，计算包括所有下级的总业绩
                            $allSubordinates = \app\common\Member::getdownmids(aid, $subordinateMid, 99999);
                            // var_dump($allSubordinates);die;
                            $allSubordinates[] = $subordinateMid; // 包含直推下级自身
                            
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', 'in', $allSubordinates)
                                ->sum('real_totalprice');
                        }
                    
                        $line_totals[$subordinateMid] = $subordinatePerformance;
                    }
                    // 当只有一个直推下级时的特殊处理
                    // var_dump( $line_totals);die;
                    if (count($line_totals) == 1) {
                        // 当只有一个下级时，大区业绩为该下级的业绩，小区业绩为0
                        $max_totalprice = reset($line_totals); // 获取该下级的业绩作为大区业绩
                        $min_totalprice = 0; // 小区业绩设置为0
                    } else {
                        // 计算大小区
                        arsort($line_totals); // 业绩降序排序
                        $max_totalprice = reset($line_totals); // 最大业绩
                        array_shift($line_totals); // 移除大区业绩
                        $min_totalprice = array_sum($line_totals); // 小区总业绩
                    }
                                  
                    // var_dump('self'.$max_line);
                    //      var_dump('self'.$self_line_total);
                    // var_dump('大区'.$max_totalprice);
                    // var_dump('小区'.$min_totalprice);die;
                $userinfo['yeji']['dq']=$max_totalprice;
                $userinfo['yeji']['xq']=$min_totalprice;
                $userinfo['yeji']['z']=$max_totalprice+$min_totalprice;
                $fxordermoney=$max_totalprice+$min_totalprice;
                //如果小区业绩2倍大于等于大区业绩就按照 ，它的考核总业绩就等于所有业绩之和如果小区业绩2倍小于大区业绩，它的考核总业绩就等于所有小区业绩的3倍
                if($min_totalprice*2<$max_totalprice){
                    $fxordermoney=$min_totalprice*2;
                }
                $userinfo['yeji']['sj']=$fxordermoney;
            
         
        
        
        //静态释放金额
		$userinfo['yicountA'] = abs(Db::name('member_scoreloga')->where('mid',mid)->where('score','<',0)->where('aid',aid)->sum('score'));
		$userinfo['yicountB'] = abs(Db::name('member_scorelogb')->where('mid',mid)->where('score','<',0)->where('aid',aid)->sum('score'));
         //计算我的下级星级
         $memberArron = \app\common\Member::getdownxiaji(mid,1,$leveldata,$userinfo['xingji'],aid);
         $memberArron = $this->array_flatten($memberArron);
         $cxingjiArr = [];
         $cxingjiArr2 = [];
         $cxingjiArr3 = [];
         for ($i = 1; $i <= $userinfo['xingji']; $i++) 
         {
            $member_nums = Db::name('member')->where('aid',aid)->where('xingji',$i)->whereIn('id',$memberArron)->count();
            if($i <= 3){
                $cxingjiArr[$i]=$member_nums;
            }elseif($i >3 && $i <=6){
                $cxingjiArr2[$i]=$member_nums;
            }elseif($i >6 && $i <=9){
                $cxingjiArr3[$i]=$member_nums;
            }
             
         }

        $jiuxingsetinfo = Db::name('jifechi')->where('aid',aid)->find();
        $jiuxingsetinfo = json_decode($jiuxingsetinfo['jiuxing'],1);
		$rdata['userinfo'] = $userinfo;
		$rdata['xingjiArr'] = $cxingjiArr;
		$rdata['xingjiArr2'] = $cxingjiArr2;
		$rdata['xingjiArr3'] = $cxingjiArr3;
		$rdata['jiuxing_status'] = $jiuxingsetinfo['jiuxing_status'];
		//统计今天没有拿到的奖励
		$starttime =strtotime(date('Y-m-d 00:00:00'));
		$endtime = strtotime(date('Y-m-d 23:59:59'));
		$rdata['today_sunshi'] = Db::name('member_xingjilog')->where('mid',mid)->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->where('aid',aid)->sum('money');
		//统计一共没拿到的奖励
		$rdata['total_sunshi'] = Db::name('member_xingjilog')->where('mid',mid)->where('aid',aid)->sum('money');
		//获取我的无限级
		 //计算我的下级星级
         $memberArron = \app\common\Member::getdownxiaji22(mid,1,$leveldata,$userinfo['xingji'],aid);
         //count($memberArron);
         $memberArron = $this->array_flatten($memberArron);
		$rdata['total_jiediancount'] = Db::name('shop_order')->whereIn('mid',$memberArron)->where('aid',aid)->whereIn('status',[1,2,3])->sum('totalprice');
		//返回云库存状态
		$rdata['yunkucun_status'] = \app\common\Member::getyunkucun(aid);
		$rdata['today_yunshouyi'] = 0;
		$rdata['zong_yunshouyi'] = 0;
		if($rdata['yunkucun_status'] == 1)
		{
		    $rdata['today_yunshouyi'] = 0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->where('remark','like','%云库存%')->sum('commission');
		    $rdata['zong_yunshouyi'] =  0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%云库存%')->sum('commission');
		}
		$datalistlevel = [];
		//datalistlevel
		//我的每个等级的下级
		$levelArr = Db::name('member_level')->where('aid',aid)->field('id,name')->order('sort asc')->select()->toArray();
		foreach($levelArr as $key=>$value)
		{
		    $datalistlevel[$key]['levelname']=$value['name'];
		    $datalistlevel[$key]['membercount']= 0+Db::name('member')->where('aid',aid)->where('levelid',$value['id'])->where('find_in_set('.mid.',path)')->count();
		}
		$rdata['datalistlevel'] = $datalistlevel;
		
		//轮数
		 if($set['gongpaizhi_shi'] > 0 && $set['gongpaizhi_ren'] >0 && $set['gongpaizhi_xiaofei'] >0)
	     {
	         $lunshustatus  =1;
         	if($userinfo['gongpainum7'] == 1)
			{
			    $lunshu = 7;
			}elseif($userinfo['gongpainum6'] == 1)
			{
			    $lunshu = 6;
			}elseif($userinfo['gongpainum5'] == 1)
			{
			    $lunshu = 5;
			}elseif($userinfo['gongpainum4'] == 1)
			{
			    $lunshu = 4;
			}elseif($userinfo['gongpainum3'] == 1)
			{
			    $lunshu = 3;
			}elseif($userinfo['gongpainum2'] == 1)
			{
			    $lunshu = 2;
			}elseif($userinfo['gongpainum'] == 1)
			{
			    $lunshu = 1;
			}else
			{
			    $lunshu = 0;
			}
			$yongjin =0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%公排%')->sum('commission');
	     }else{
	         $lunshustatus  =0;
	          $lunshu = 0;
	          $yongjin = 0;
	     }
	     //轮数//无主账号
	     $rdata['lunshustatus'] = $lunshustatus;
	     $rdata['lunshu'] = $lunshu;
	      $rdata['yongjin'] = $yongjin; 
	      //轮数有主账号
	      $xunizhanhao= Db::name('member')->where('aid',aid)->field('*')->where('ypid',mid)->where('isxuni',1)->select()->toArray();
	      $xunizhanhaocount = count($xunizhanhao);
	      $comm = 0;
	      $yishouyi1 = 0;
	      foreach($xunizhanhao as $k=>$v)
		  {
		     $yongjin = Db::name('member_commissionlog')->where('aid',aid)->where('mid',$v['id'])->where('isgongpaitixian',0)->where('remark','like','%公排%')->sum('commission');
		     $yishouyi = Db::name('member_commissionlog')->where('aid',aid)->where('mid',$v['id'])->where('isgongpaitixian',1)->where('commission','>',0)->where('remark','like','%公排%')->sum('commission');
		     $comm = $yongjin+$comm;
		     $yishouyi1 = $yishouyi1+$yishouyi;
	 	 }
		  $rdata['xunizhanhaocount'] = $xunizhanhaocount;
	      $rdata['xunizhanhao']['count']= $xunizhanhaocount;
	      $rdata['xunizhanhao']['dai']= $comm;
	      $rdata['xunizhanhao']['shouyi']= $yishouyi1;
	      
	   //   var_dump($xunizhanhaocount);die;
// 		/销售业绩分红
        $rdata['xsyj_shi'] = $set['xsyj_shi'];
        $rdata['xsyj']['dianshu'] = Db::name('shop_order')->where('aid',aid)->where('mid',mid)->where('dianshu','>',0)->where('status','in',[1,2,3])->sum('dianshu');
        $jiangjinchi = Db::name('shop_order')->where('aid',aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->sum('xsyjfh');//奖金池金额
        $zongdianshu = Db::name('shop_order')->where('aid',aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->sum('dianshu');//总点数
        $dandian = $jiangjinchi/$zongdianshu;
        $dandian = number_format($dandian,2);
        $rdata['xsyj']['daifenhong'] = round($dandian*$rdata['xsyj']['dianshu'],2);
        //销售金额
        $rdata['xsyj']['yifenhong'] = 0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%销售金额%')->sum('commission');
        $rdata['xsyj']['shoudong'] = 0 + Db::name('member_commissionlog')->where('aid',aid)->where('mid',mid)->where('remark','like','%手动分红%')->sum('commission');
        $rdata['xsyj']['yifenhong'] = $rdata['xsyj']['yifenhong']+$rdata['xsyj']['shoudong'];
        $rdata['levelArr']  = $memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
        $liandongrs = Db::name('liandong_set')->where('aid',aid)->find();
        $rdata['liandongrs'] = $liandongrs;
        //
        $adminset = Db::name('admin_set')->where('aid',aid)->find();
         $rdata['tuozhancrm']['tuozhancrm_status'] = $adminset['tuozhancrm_status'];
        $rdata['yihuo']['yihuo_status'] = $adminset['yihuo_status'];
        //，商家总流水，今日流水，昨日流水，本月流水，季度流水，抽佣总和
        $mybusiness = Db::name('business')->where('aid',aid)->where('tuozhanid',mid)->field('id')->where('tuozhanid','<>',0)->select()->toArray();
        $rdata['yihuo']['businesscount'] = count($mybusiness);
        $mybusiness = array_column($mybusiness,'id');
        $mybusiness = implode(',',$mybusiness);
          if(empty($mybusiness)){
            $rdata['yihuo']['zongliushui'] =0;
            $rdata['yihuo']['jinday'] = 0;
            $rdata['yihuo']['zuoday'] = 0;
             $rdata['yihuo']['jinmonth']  = 0;
        }else{
        //商家总流水
// 		$zongliushui = Db::name('shop_order')->where('aid',aid)->where('bid','in',$mybusiness)->where('status','in','1,2,3')->sum('totalprice');
// 		$rdata['yihuo']['zongliushui'] = $zongliushui;
            $zongliushui = (
                Db::name('shop_order')->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1,2,3])
                    ->sum('totalprice')
            ) + (
                Db::name('maidan_order') // 添加maidan_order表的统计
                    ->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1]) // 替换为maidan_order表中的正确状态码
                    ->sum('paymoney') // 假设maidan_order表中流水字段名为paymoney
            );
            
            $rdata['yihuo']['zongliushui'] = number_format($zongliushui, 2, '.', '');
		//今日流水
            	$start = strtotime(date('Y-m-d 00:00:00'));
            $end = strtotime(date('Y-m-d 23:59:59'));
            
            $jinday = (
                Db::name('shop_order')->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1,2,3])
                    ->where('paytime', '>=', $start)
                    ->where('paytime', '<=', $end)
                    ->sum('totalprice')
            ) + (
                Db::name('maidan_order') // 添加maidan_order表的今日流水统计
                    ->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1]) // 替换为maidan_order表中的正确状态码
                    ->where('paytime', '>=', $start)
                    ->where('paytime', '<=', $end)
                    ->sum('paymoney') // 假设maidan_order表中流水字段名为paymoney
            );
            
            $rdata['yihuo']['jinday'] = number_format($jinday, 2, '.', '');
		//昨日流水
            	$start = strtotime(date('Y-m-d 00:00:00', strtotime('-1 days')));
            $end = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days')));
            
            $zuoday = (
                Db::name('shop_order')->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1,2,3])
                    ->where('paytime', '>=', $start)
                    ->where('paytime', '<=', $end)
                    ->sum('totalprice')
            ) + (
                Db::name('maidan_order') // 添加maidan_order表的昨日流水统计
                    ->where('aid', aid)
                    ->where('bid', 'in', $mybusiness)
                    ->whereIn('status', [1]) // 替换为maidan_order表中的正确状态码
                    ->where('paytime', '>=', $start)
                    ->where('paytime', '<=', $end)
                    ->sum('paymoney') // 假设maidan_order表中流水字段名为paymoney
            );
            
            $rdata['yihuo']['zuoday'] = number_format($zuoday, 2, '.', '');
		//本月流水
		$start = strtotime(date('Y-m-01 00:00:00'));
		$end = strtotime(date('Y-m-31 23:59:59'));

                
                $jinmonth = (
                    Db::name('shop_order')->where('aid', aid)
                        ->where('bid', 'in', $mybusiness)
                        ->whereIn('status', [1,2,3])
                        ->where('paytime', '>=', $start)
                        ->where('paytime', '<=', $end)
                        ->sum('totalprice')
                ) + (
                    Db::name('maidan_order') // 添加maidan_order表的当月流水统计
                        ->where('aid', aid)
                        ->where('bid', 'in', $mybusiness)
                        ->whereIn('status', [1]) // 替换为maidan_order表中的正确状态码
                        ->where('paytime', '>=', $start)
                        ->where('paytime', '<=', $end)
                        ->sum('paymoney') // 假设maidan_order表中流水字段名为paymoney
                );

                $rdata['yihuo']['jinmonth'] = number_format($jinmonth, 2, '.', '');
        }
		$chouyong = Db::name('shop_order_goods')->where('aid',aid)->where('tuozhanid',mid)->where('tuozhanid','<>',0)->where('tuozhanfei_status',1)->sum('tuozhanfei');
		$chouyong2 = Db::name('maidan_order')->where('aid',aid)->where('tuozhanid',mid)->where('tuozhanid','<>',0)->where('tuozhanfei_status',1)->sum('tuozhanfei');
	//	$rdata['yihuo']['chouyong'] = $chouyong+$chouyong2;
	$rdata['yihuo']['chouyong'] = number_format($chouyong + $chouyong2, 5, '.', '');
		$rdata['userinfo']['tuozhanfei_zls'] = Db::name('shop_order_goods')->where('aid',aid)->where('tuozhanid',mid)->where('tuozhanid','<>',0)->where('tuozhanfei_status',0)->sum('tuozhanfei');
		$rdata['yihuo']['tixiantuozhan'] = abs(Db::name('member_tuozhanfeilog')->where('aid',aid)->where('mid',mid)->where('money','<',0)->sum('money'));
		$tuozhan = Db::name('member_tuozhan')->where('aid',aid)->where('mid',mid)->find();
		$cityliushui = $businesscount = 0;
		  if($tuozhan)
		  {
		      
		      $name  =Db::name('yihuo_category')->where('aid',aid)->where('id',$tuozhan['cateid'])->find();
		      if($name)
		      {
		          $shenfen = $name['name'];
		      }else{
		          $shenfen = '';
		      }
		      if($tuozhan['defaultcity'] >0)
              {
    		      $defaultprovince_name =  Db::name('area')->where(['id'=>$tuozhan['defaultprovince']])->find()['name'];
                  $defaultcity_name = Db::name('area')->where(['id'=>$tuozhan['defaultcity']])->find()['name'];
                  $cityname = $defaultprovince_name.'-'.$defaultcity_name;
                  $cityliushui = 0;
                   $mybusiness = Db::name('business')->where('aid',aid)->where('city_id',$tuozhan['defaultcity'])->where('city_id','<>',0)->field('id')->select()->toArray();
                    $businesscount = count($mybusiness);
                    $mybusiness = array_column($mybusiness,'id');
                    $mybusiness = implode(',',$mybusiness);
        	        if(empty($mybusiness)){
                        $cityliushui =0;
                    }else{
                        $zongliushui_shop_order = Db::name('shop_order')
                            ->where('aid', aid)
                            ->where('bid', 'in', $mybusiness)
                            ->where('bid', '<>', 0)
                            ->where('status', 'in', '1,2,3')
                            ->sum('totalprice');
                        $zongliushui_maidan_order = Db::name('maidan_order')
                            ->where('aid', aid) // 假设maidan_order表也有aid字段，根据实际情况调整
                            ->where('bid', 'in', $mybusiness)// 同上，确认maidan_order是否有bid字段
                            ->where('status', 'in', '1,2,3') // 根据maidan_order表的实际状态字段和状态值调整
                            ->sum('paymoney');
                        
                        $cityliushui = $zongliushui_shop_order + $zongliushui_maidan_order;
                    }
              }else{
                  $cityname = '';
              }
		  }else{
		      $shenfen = '';
		      $cityname = '';
		  }
		  
		   $rdata['userinfo']['shenfen'] =$shenfen;
		   $rdata['userinfo']['cityname'] =$cityname;
		   $rdata['userinfo']['cityliushui'] = number_format($cityliushui, 2, '.', '');
		   $rdata['userinfo']['businesscount'] =$businesscount;
		  $jfc_statusre = Db::name('jifechi')->where('aid',aid)->find();
		  $rdata['userinfo']['jfc_status'] = $jfc_statusre['jfc_status'];
		  // 获取排行榜设置
 // 获取排行榜设置
        $ranking_setting = Db::name('jietikaoheset')->where('aid', aid)->find();
        
        // 判断当前用户是否有权限查看排行榜
        $can_view_ranking = false;
        if ($ranking_setting && $ranking_setting['ranking_status'] == 1) {
            // 检查 ranking_level 是否为空，避免 explode() 报错
            $ranking_level = $ranking_setting['ranking_level'] ?? '';
            if (!empty($ranking_level)) {
                $allowed_levels = explode(',', $ranking_level);
                $can_view_ranking = in_array($userinfo['levelid'], $allowed_levels);
            }
        }
        
        $rdata['ranking_info'] =  $can_view_ranking;

        // 获取拓展费转余额贡献值扣除配置
        $yihuo_config = Db::name('admin_set')->where('aid', aid)->field('yihuo_deduct_gongxianzhi,yihuo_gongxianzhi_rate')->find();
        $rdata['yihuo_deduct_gongxianzhi'] = $yihuo_config['yihuo_deduct_gongxianzhi'] ?? 0;
        $rdata['yihuo_gongxianzhi_rate'] = $yihuo_config['yihuo_gongxianzhi_rate'] ?? 100;

		// 性能监控：接口执行完成
		$total_time = round((microtime(true) - $interface_start_time) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurvey] 接口执行完成，总耗时: '.$total_time.'ms', 'info');

		return $this->json($rdata);
	}

	/**
	 * 优化版本的佣金统计接口
	 * 通过拆分功能模块和优化查询来提升性能
	 */
	public function commissionSurveyOptimized(){
		// 性能监控：记录接口开始时间
		$interface_start_time = microtime(true);
		$timestamp = date('Y-m-d H:i:s');
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurveyOptimized] 优化版接口开始执行', 'info');

		// 获取基础用户信息
		$userinfo = $this->getUserBasicInfo();
		if(!$userinfo) {
			return $this->json(['code' => 0, 'msg' => '用户信息不存在']);
		}

		// 获取系统设置
		$set = Db::name('admin_set')->where('aid',aid)->find();

		// 初始化返回数据
		$rdata = [];

		// 1. 获取提现统计数据
		$withdrawStats = $this->getCommissionWithdrawStats();
		$rdata = array_merge($rdata, $withdrawStats);

		// 2. 获取上级佣金统计
		$parentCommissionStats = $this->getParentCommissionStats($userinfo);
		$rdata = array_merge($rdata, $parentCommissionStats);

		// 3. 获取分红统计数据
		if($this->shouldCalculateFenhong($userinfo)) {
			$fenhongStats = $this->getFenhongStats($userinfo, $set);
			$rdata = array_merge($rdata, $fenhongStats);
		}

		// 4. 获取团队业绩统计
		if($set['teamyeji_show'] == 1) {
			$teamStats = $this->getTeamPerformanceStats($set);
			$rdata = array_merge($rdata, $teamStats);
		}

		// 5. 获取团队成员统计
		if($set['teamnum_show'] == 1) {
			$teamMemberStats = $this->getTeamMemberStats();
			$rdata = array_merge($rdata, $teamMemberStats);
		}

		// 6. 获取销售统计数据
		$salesStats = $this->getSalesStats();
		$rdata = array_merge($rdata, $salesStats);

		// 7. 获取其他统计数据
		$otherStats = $this->getOtherStats($userinfo, $set);
		$rdata = array_merge($rdata, $otherStats);

		// 性能监控：接口执行完成
		$total_time = round((microtime(true) - $interface_start_time) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][commissionSurveyOptimized] 优化版接口执行完成，总耗时: '.$total_time.'ms', 'info');

		return $this->json($rdata);
	}

	/**
	 * 获取用户基础信息
	 */
	private function getUserBasicInfo() {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		$userinfo = Db::name('member')->field('*')->where('aid',aid)->where('id',mid)->find();

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getUserBasicInfo] 用户信息查询完成，耗时: '.$step_time.'ms', 'info');

		return $userinfo;
	}

	/**
	 * 获取提现统计数据
	 */
	private function getCommissionWithdrawStats() {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		$count = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->sum('txmoney');
		$count0 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',0)->sum('txmoney');
		$count1 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',1)->sum('txmoney');
		$count2 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',2)->sum('txmoney');
		$count3 = 0 + Db::name('member_commission_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status',3)->sum('txmoney');

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getCommissionWithdrawStats] 提现统计查询完成，耗时: '.$step_time.'ms', 'info');

		return [
			'count' => number_format($count,2,'.',''),
			'count0' => number_format($count0,2,'.',''),
			'count1' => number_format($count1,2,'.',''),
			'count2' => number_format($count2,2,'.',''),
			'count3' => number_format($count3,2,'.','')
		];
	}

	/**
	 * 获取上级佣金统计（优化版本）
	 * 使用单个SQL查询替代20个独立查询
	 */
	private function getParentCommissionStats($userinfo) {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		if(!$userinfo) {
			return ['countdqr' => '0.00'];
		}

		// 优化：使用单个SQL查询获取所有层级的佣金
		$sql = "SELECT
			SUM(parent1commission) as c1, SUM(parent2commission) as c2, SUM(parent3commission) as c3, SUM(parent4commission) as c4, SUM(parent5commission) as c5,
			SUM(parent6commission) as c6, SUM(parent7commission) as c7, SUM(parent8commission) as c8, SUM(parent9commission) as c9, SUM(parent10commission) as c10,
			SUM(parent11commission) as c11, SUM(parent12commission) as c12, SUM(parent13commission) as c13, SUM(parent14commission) as c14, SUM(parent15commission) as c15,
			SUM(parent16commission) as c16, SUM(parent17commission) as c17, SUM(parent18commission) as c18, SUM(parent19commission) as c19, SUM(parent20commission) as c20
			FROM ".table_name('shop_order_goods')." og
			LEFT JOIN ".table_name('shop_order')." o ON o.id=og.orderid
			WHERE og.aid=".aid." AND (o.status=1 OR o.status=2)
			AND (og.parent1={$userinfo['id']} OR og.parent2={$userinfo['id']} OR og.parent3={$userinfo['id']} OR og.parent4={$userinfo['id']} OR og.parent5={$userinfo['id']}
			OR og.parent6={$userinfo['id']} OR og.parent7={$userinfo['id']} OR og.parent8={$userinfo['id']} OR og.parent9={$userinfo['id']} OR og.parent10={$userinfo['id']}
			OR og.parent11={$userinfo['id']} OR og.parent12={$userinfo['id']} OR og.parent13={$userinfo['id']} OR og.parent14={$userinfo['id']} OR og.parent15={$userinfo['id']}
			OR og.parent16={$userinfo['id']} OR og.parent17={$userinfo['id']} OR og.parent18={$userinfo['id']} OR og.parent19={$userinfo['id']} OR og.parent20={$userinfo['id']})";

		$result = Db::query($sql);
		$countdqr = 0;

		if($result && isset($result[0])) {
			$row = $result[0];
			for($i = 1; $i <= 20; $i++) {
				$countdqr += floatval($row['c'.$i] ?? 0);
			}
		}

		// 获取上级昵称
		$pnickname = '';
		if($userinfo['pid']) {
			$pnickname = Db::name('member')->where('aid',aid)->where('id',$userinfo['pid'])->value('nickname');
		}

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getParentCommissionStats] 上级佣金统计完成，耗时: '.$step_time.'ms，佣金: '.$countdqr, 'info');

		return [
			'countdqr' => number_format($countdqr,2,'.',''),
			'pnickname' => $pnickname
		];
	}

	/**
	 * 判断是否需要计算分红
	 */
	private function shouldCalculateFenhong($userinfo) {
		$levelinfo = Db::name('member_level')->where('id',$userinfo['levelid'])->find();
		$levelRecord = Db::name('member_level_record')->where('mid',mid)->select();
		$levelids = $levelRecord->column('levelid');
		$levelExtend = Db::name('member_level')->whereIn('id',$levelids);

		// 检查各种分红条件
		if($levelinfo['fenhong'] > 0) return true;
		if($levelExtend->where('fenhong', '>', 0)->count()) return true;
		if($levelinfo['teamfenhonglv'] > 0 && ($levelinfo['teamfenhongbl'] > 0 || $levelinfo['teamfenhong_money'] > 0)) return true;
		if($levelinfo['areafenhong'] > 0 && $levelinfo['areafenhongbl'] > 0) return true;

		return false;
	}

	/**
	 * 获取分红统计数据（使用缓存优化）
	 */
	private function getFenhongStats($userinfo, $set) {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		// 检查缓存
		$cache_key = 'fenhong_stats_' . aid . '_' . mid;
		$cached_data = cache($cache_key);

		if($cached_data && (time() - $cached_data['timestamp']) < 300) {
			\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getFenhongStats] 使用缓存的分红数据', 'info');
			return $cached_data['data'];
		}

		$fenhong_data = [];

		// 计算各种分红
		$rs1 = \app\common\Fenhong::gdfenhong(aid,$set,[],0,time(),1,mid);
		$fenhong_data['fenhong_yj'] = round($rs1['commissionyj'],2);

		$rs2 = \app\common\Fenhong::teamfenhong(aid,$set,[],0,time(),1,mid);
		$fenhong_data['teamfenhong_yj'] = round($rs2['commissionyj'],2);

		$rs3 = \app\common\Fenhong::areafenhong(aid,$set,[],0,time(),1,mid);
		$fenhong_data['areafenhong_yj'] = round($rs3['commissionyj'],2);

		// 缓存结果
		cache($cache_key, [
			'data' => $fenhong_data,
			'timestamp' => time()
		], 300);

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getFenhongStats] 分红统计完成，耗时: '.$step_time.'ms', 'info');

		return $fenhong_data;
	}

	/**
	 * 获取团队业绩统计（优化版本）
	 * 使用缓存的团队成员ID列表
	 */
	private function getTeamPerformanceStats($set) {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		// 获取团队成员ID（使用缓存）
		$downmids = $this->getCachedTeamMemberIds();

		// 计算商品订单的业绩
		$shop_start = microtime(true);
		$yejiwhere = [['status','in','1,2,3']];
		$teamyeji_shop = Db::name('shop_order_goods')->where('aid',aid)->where('mid','in',$downmids)->where($yejiwhere)->sum('totalprice');
		$shop_time = round((microtime(true) - $shop_start) * 1000, 2);

		// 计算会员升级订单的业绩
		$levelup_start = microtime(true);
		$levelup_where = [
			['aid','=',aid],
			['mid','in',$downmids],
			['status','in','1,2']
		];
		$teamyeji_levelup = Db::name('member_levelup_order')->where($levelup_where)->sum('totalprice');
		$levelup_time = round((microtime(true) - $levelup_start) * 1000, 2);

		$teamyeji = $teamyeji_shop + $teamyeji_levelup;

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getTeamPerformanceStats] 团队业绩统计完成，总耗时: '.$step_time.'ms，商品业绩: '.$teamyeji_shop.'，升级业绩: '.$teamyeji_levelup.'，总业绩: '.$teamyeji, 'info');

		return [
			'teamyeji' => round($teamyeji,2),
			'teamyeji_shop' => round($teamyeji_shop,2),
			'teamyeji_levelup' => round($teamyeji_levelup,2)
		];
	}

	/**
	 * 获取缓存的团队成员ID列表
	 */
	private function getCachedTeamMemberIds() {
		$cache_key = 'team_member_ids_' . aid . '_' . mid;
		$cached_ids = cache($cache_key);

		if($cached_ids && (time() - $cached_ids['timestamp']) < 600) { // 10分钟缓存
			\think\facade\Log::write(date('Y-m-d H:i:s').'-INFO-[ApiAgent][getCachedTeamMemberIds] 使用缓存的团队成员ID，数量: '.count($cached_ids['data']), 'info');
			return $cached_ids['data'];
		}

		$team_start = microtime(true);
		$levelids = Db::name('member_level')->where('aid',aid)->where('id','>=',2)->column('id');
		$downmids = \app\common\Member::getteammids(aid,mid,999,$levelids);
		$team_time = round((microtime(true) - $team_start) * 1000, 2);

		// 缓存结果
		cache($cache_key, [
			'data' => $downmids,
			'timestamp' => time()
		], 600);

		\think\facade\Log::write(date('Y-m-d H:i:s').'-INFO-[ApiAgent][getCachedTeamMemberIds] 重新获取团队成员ID，耗时: '.$team_time.'ms，数量: '.count($downmids), 'info');

		return $downmids;
	}

	/**
	 * 获取团队成员统计
	 */
	private function getTeamMemberStats() {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		$downmids = $this->getCachedTeamMemberIds();
		$teamnum = count($downmids);

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getTeamMemberStats] 团队成员统计完成，耗时: '.$step_time.'ms，成员数量: '.$teamnum, 'info');

		return ['teamnum' => $teamnum];
	}

	/**
	 * 获取销售统计数据
	 */
	private function getSalesStats() {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		$downmids = $this->getCachedTeamMemberIds();
		$downmids[] = mid; // 包含自己

		// 计算总销售件数
		$total_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->sum('num');

		// 计算当月销售件数
		$current_month_start = strtotime(date('Y-m-01 00:00:00'));
		$current_month_end = strtotime(date('Y-m-t 23:59:59'));
		$current_month_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $current_month_start)
			->where('createtime', '<=', $current_month_end)
			->sum('num');

		// 计算当日销售件数
		$today_start = strtotime(date('Y-m-d 00:00:00'));
		$today_end = strtotime(date('Y-m-d 23:59:59'));
		$today_sales_count = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', '>=', $today_start)
			->where('createtime', '<=', $today_end)
			->sum('num');

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getSalesStats] 销售统计完成，耗时: '.$step_time.'ms', 'info');

		return [
			'total_sales_count' => intval($total_sales_count),
			'current_month_sales_count' => intval($current_month_sales_count),
			'today_sales_count' => intval($today_sales_count)
		];
	}

	/**
	 * 获取其他统计数据（简化版本）
	 */
	private function getOtherStats($userinfo, $set) {
		$step_start = microtime(true);
		$timestamp = date('Y-m-d H:i:s');

		// 这里只包含最基本的其他统计，复杂的统计可以按需加载
		$other_data = [
			'comwithdraw' => $set['comwithdraw'],
			'comwithdrawmin' => $set['comwithdrawmin'],
			'commission2money' => $set['commission2money'],
			'fxjiesuantime' => $set['fxjiesuantime']
		];

		$step_time = round((microtime(true) - $step_start) * 1000, 2);
		\think\facade\Log::write($timestamp.'-INFO-[ApiAgent][getOtherStats] 其他统计完成，耗时: '.$step_time.'ms', 'info');

		return $other_data;
	}

	function array_flatten($array) {
        $result=array();
        array_walk_recursive($array,function($value)use(&$result){
            if( Gettype($value)== 'integer' && $value >0)
            {
                array_push($result,$value);
            }
        });
        return$result;
    }
    
    
	/**
	 * 销售统计
	 */
	public function salesStatistics() 
	{
		$rdata = [];
		
		// 获取当前时间和30天前的时间戳
		$now = time();
		$thirtyDaysAgo = strtotime('-30 days');
		
		// 获取团队所有成员ID
		$levelids = Db::name('member_level')->where('aid', aid)->where('id', '>=', 2)->column('id');
		$downmids = \app\common\Member::getteammids(aid, mid, 999, $levelids);
// 		var_dump($downmids);
		$downmids[] = mid; // 包含自己
	//	var_dump($downmids);
		
		// 1. 近30天销售数量
		$last30DaysSales = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->where('createtime', 'between', [$thirtyDaysAgo, $now])
			->sum('total_count_num');
			//var_dump($last30DaysSales);
		// 2. 累计销售额
		$totalSalesAmount = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)//
			->where('status', 'in', [1,2,3])
			->sum('totalprice');
		
		// 3. 累计销售数量
		$totalSalesCount = Db::name('shop_order_goods')
			->where('aid', aid)
			->where('mid', 'in', $downmids)
			->where('status', 'in', [1,2,3])
			->sum('total_count_num');
		
		// 4. 近30天团队新增人数
		$last30DaysNewMembers = Db::name('member')
			->where('aid', aid)
			->where('pid', 'in', $downmids)
			->where('createtime', 'between', [$thirtyDaysAgo, $now])
			->count();
		
		$rdata['last30DaysSales'] = intval($last30DaysSales);
		$rdata['totalSalesAmount'] = number_format($totalSalesAmount, 2, '.', '');
		$rdata['totalSalesCount'] = intval($totalSalesCount);
		$rdata['last30DaysNewMembers'] = intval($last30DaysNewMembers);
		
		return $this->json($rdata);
	}

    /**
     * 我的云库存
     * */
    public function myyunkucun()
    {
        $yunkucun_status = \app\common\Member::getyunkucun(aid);
        if($yunkucun_status == 1){
            $pagenum = input('post.pagenum')?input('post.pagenum'):1;
            $pernum = 20;
            $datalist = Db::name('yunkucun_users')->alias('xj')
            ->field("xj.*,from_unixtime(xj.updatetime)createtime")
            ->leftJoin('member m','m.id=xj.mid')
            ->where('xj.mid',mid)->where('xj.kuncun_type',0)->where('xj.aid',aid)->page($pagenum,$pernum)->order('xj.id desc')->select()->toArray();
            if(!$datalist) $datalist = [];
        	return $this->json(['status'=>1,'datalist'=>$datalist]);
        }else{
            return $this->json(['status'=>0,'msg'=>'云库存未开启']); 
        }
    }
    
    
   
    // /**
    //  * 我的云库存收益
    //  * */
    // public function yunshouyi()
    // {
    //     $yunkucun_status = \app\common\Member::getyunkucun(aid);
    //     if($yunkucun_status == 1){
    //         $pagenum = input('post.pagenum')?input('post.pagenum'):1;
    //         $pernum = 20;
    //         $datalist = Db::name('member_commissionlog')->alias('xj')
    //         ->field("xj.*,from_unixtime(xj.createtime)createtime")
    //         ->leftJoin('member m','m.id=xj.mid')
    //         ->where('xj.mid',mid)->where('xj.aid',aid)->where('xj.remark','like','%云库存%')->page($pagenum,$pernum)->order('xj.id desc')->select()->toArray();
    //         if(!$datalist) $datalist = [];
    //         foreach($datalist as $k=>$v)
    //         {
    //              $cuser = Db::name('member')->where('id',$v['frommid'])->find();
    //              $datalist[$k]['fromname'] = $cuser['nickname'];
    //         }
    //     	return $this->json(['status'=>1,'datalist'=>$datalist]);
    //     }else{
    //         return $this->json(['status'=>0,'msg'=>'云库存未开启']); 
    //     }
    // }
    /**
     * 提货
     * */
    public function givetihuo()
    {
        $yunkucun_status = \app\common\Member::getyunkucun(aid);
        if($yunkucun_status == 1){
            $id = input('param.id/d');
    		$money = input('param.money/f');
    		if($money <= 0) return $this->json(['status'=>0,'msg'=>'提货数量必须大于0']);
    		$yundata = Db::name('yunkucun_users')->where('aid',aid)->where('mid',mid)->where('id',$id)->find();
    		if(empty($yundata))
    		{
    		    return $this->json(['status'=>0,'msg'=>'提货商品错误']);
    		}
    		if($yundata['num'] < $money)
    		{
    		    return $this->json(['status'=>0,'msg'=>'云库存数量不足']);
    		}
    		return $this->json(['status'=>1,'msg'=>'跳转中']); 
        }else{
            return $this->json(['status'=>0,'msg'=>'云库存未开启']); 
        }
    }
    public function createOrder()
    {
        $yunkucun_status = \app\common\Member::getyunkucun(aid);
        if($yunkucun_status == 1){
            $this->checklogin();
    		$sysset = Db::name('admin_set')->where('aid',aid)->find();
    		$post = input('post.');
    		//收货地址
    		if($post['addressid']=='' || $post['addressid']==0){
    			$address = ['id'=>0,'name'=>$post['linkman'],'tel'=>$post['tel'],'area'=>'','address'=>''];
    		}else{
    			$address = Db::name('member_address')->where('id',$post['addressid'])->where('aid',aid)->where('mid',mid)->find();
    			if(!$address) return $this->json(['status'=>0,'msg'=>'所选收货地址不存在']);
    		}
    		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
    		$shopset = Db::name('shop_sysset')->where('aid',aid)->find();
    
    		$buydata = $post['buydata'];
    		$arr = explode(',',$buydata[0]['prodata']);
		    $count =count($arr);
		    $yundata = Db::name('yunkucun_users')->where('aid',aid)->where('mid',mid)->where('proid',$arr[0])->where('ggid',$arr[1])->find();
		    if(empty($yundata))
    		{
    		    return $this->json(['status'=>0,'msg'=>'提货商品错误']);
    		}
		    if($yundata['num'] < $arr[$count-1])
		    {
		        return $this->json(['status'=>0,'msg'=>'云库存数量不足']);
		    }
    		$ordernum = \app\common\Common::generateOrderNo(aid);
    		$scoredkmaxmoney = 0;
    		$scoremaxtype = 0;
    		$scoredkmaxmoneyhei = 0;
    		$scoremaxtypehei = 0;
    		$i = 0;
    		$alltotalprice = 0;
    		$alltotalscore = 0;
    		
    		
    		foreach($buydata as $data){
    			$i++;
    			$bid = $data['bid'];
    			if($data['prodata']){
    				$prodata = explode('-',$data['prodata']);
    			}else{
    				return $this->json(['status'=>0,'msg'=>'产品数据错误']);
    			}
                if($bid)
                    $store_info = Db::name('business')->where('aid',aid)->where('id',$bid)->find();
                else
                    $store_info = Db::name('admin_set')->where('aid',aid)->find();
                $store_name = $store_info['name'];
    
                $freightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);
    
    			$fids = [];
    			foreach($freightList as $v){
    				$fids[] = $v['id'];
    			}
    			//			dd($data['prodataList']);
    			$extendInput = [];
                //符合满减的商品价格
                $mj_price = 0;
    			foreach($prodata as $key=>$pro){
    				$sdata = explode(',',$pro);
    				$sdata[2] = intval($sdata[2]);
    				if($sdata[2] <= 0) return $this->json(['status'=>0,'msg'=>'购买数量有误']);
    				$product = Db::name('shop_product')->where('aid',aid)->where('ischecked',1)->where('bid',$bid)->where('id',$sdata[0])->find();
    				if(!$product) return $this->json(['status'=>0,'msg'=>'产品不存在或已下架']);
    				
    				if($product['status']==0){
    					return $this->json(['status'=>0,'msg'=>'商品未上架']);
    				}
    				if($product['status']==2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())){
    					return $this->json(['status'=>0,'msg'=>'商品未上架']);
    				}
    				if($product['status']==3){
    					$start_time = strtotime(date('Y-m-d '.$product['start_hours']));
    					$end_time = strtotime(date('Y-m-d '.$product['end_hours']));
    					if(($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))){
    						return $this->json(['status'=>0,'msg'=>'商品未上架']);
    					}
    				}
    				
    				if(getcustom('shopcate_time')){
    					$procids = explode(',',$product['cid']);
    					$cate = Db::name('shop_category')->where('aid',aid)->where('id','in',$product['cid'])->select()->toArray();
    					foreach($cate as $c){
    						if($c['start_hours'] && $c['end_hours']){
    							$catestart_time =  strtotime(date('Y-m-d '.$c['start_hours']));
    							$cateend_time =  strtotime(date('Y-m-d '.$c['end_hours']));
    							if(($catestart_time < $cateend_time && ($catestart_time > time() || $cateend_time < time())) || ($catestart_time >= $cateend_time && ($catestart_time > time() && $cateend_time < time()))){
    								return $this->json(['status'=>0,'msg'=>'商品购买时间'.$c['start_hours'].'-'.$c['end_hours'].'请稍后再来']);
    							}
    						}
    						
    					}
    				}
    
    
    
    
    
    				
    				if($key==0) $title = $product['name'];
    
    				$guige = Db::name('shop_guige')->where('aid',aid)->where('id',$sdata[1])->find();
    				if(!$guige) return $this->json(['status'=>0,'msg'=>'产品规格不存在或已下架']);
    				if($guige['stock'] < $sdata[2]){
    					return $this->json(['status'=>0,'msg'=>'库存不足']);
    				}
    				// 检查 gettj 是否为空，避免 explode() 报错
    				$gettj_str = $product['gettj'] ?? '';
    				$gettj = !empty($gettj_str) ? explode(',',$gettj_str) : [];
    				if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
    					if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
    					return $this->json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
    				}
    				if($product['perlimit'] > 0){
    					$buynum = $sdata[2] + Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
    					if($buynum > $product['perlimit']){
    						return $this->json(['status'=>0,'msg'=>'每人限购'.$product['perlimit'].'件']);
    					}
    				}
                    if($guige['limit_start'] > 0 && $sdata[2] < $guige['limit_start']){
                        return $this->json(['status'=>0,'msg'=>'['.$product['name'].']['.$guige['name'].'] '.$guige['limit_start'].'件起售']);
                    }
    				if($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']){
    					return $this->json(['status'=>0,'msg'=>$product['limit_start'].'件起售']);
    				}
    				if($product['perlimitdan'] > 0 && $sdata[2] > $product['perlimitdan']){
    					return $this->json(['status'=>0,'msg'=>'['.$product['name'].'] 每单限购'.$product['perlimitdan'].'件']);
    				}
                    if(getcustom('plug_tengrui')) {
                        //判断是否是否符合会员认证、会员关系、一户、用户组
                        $tr_check = new \app\common\TengRuiCheck();
                        $check_product = $tr_check->check_product($this->member,$product);
                        if($check_product && $check_product['status'] == 0){
                            return $this->json(['status'=>$check_product['status'],'msg'=>$check_product['msg']]);
                        }
                        $tr_roomId = $check_product['tr_roomId'];
                        $product['tr_roomId'] = $tr_roomId;
                    }
    				if($product['lvprice']==1) $guige = $this->formatguige($guige,$product['bid']);
    				if(getcustom('plug_xiongmao') && $data['prodataList']) {
    				    //自定义价格
                        if($data['prodataList'][$key]['guige']['sell_price'] < $guige['sell_price']) {
                            return $this->json(['status'=>0,'msg'=>'"'.$product['name'].'"不能小于原价']);
                        }
                        $guige['sell_price'] = $data['prodataList'][$key]['guige']['sell_price'];
                    }
    				$product_price += $guige['sell_price'] * $sdata[2];
    				if($product['balance']){
    					$balance_price += $product_price * $product['balance']*0.01;
    					$product_price = $product_price * (1-$product['balance']*0.01);
    				}
    				$product_priceArr[] = $guige['sell_price'] * $sdata[2];
    				if($product['lvprice']==0 && $product['no_discount'] == 0){ //未开启会员价
    					$needzkproduct_price += $guige['sell_price'] * $sdata[2];
    				}
    				$totalweight += $guige['weight'] * $sdata[2];
    				$totalnum += $sdata[2];
    				
    				
    				if($product['scoredkmaxset']==0){
    					if($sysset['scoredkmaxpercent'] == 0){
    						$scoredkmaxmoney += 0;
    					}else{
    						if($sysset['scoredkmaxpercent'] > 0 && $sysset['scoredkmaxpercent']<100){
    							$scoredkmaxmoney += $sysset['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
    						}else{
    							$scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
    						}
    					}
    				}elseif($product['scoredkmaxset']==1){
    					$scoremaxtype = 1;
    					$scoredkmaxmoney += $product['scoredkmaxval'] * 0.01 * $guige['sell_price'] * $sdata[2];
    				}elseif($product['scoredkmaxset']==2){
    					$scoremaxtype = 1;
    					$scoredkmaxmoney += $product['scoredkmaxval'] * $sdata[2];
    				}else{
    					$scoremaxtype = 1;
    					$scoredkmaxmoney += 0;
    				}
    				
    				
    				if($product['scoredkmaxsethei']==0){
        				if($sysset['scoredkmaxpercenthei'] == 0){
        					$scoremaxtypehei = 1;
        					$scoredkmaxmoneyhei += 0;
        				}else{
        					if($sysset['scoredkmaxpercenthei'] > 0 && $sysset['scoredkmaxpercenthei']<100){
        						$scoredkmaxmoneyhei += $sysset['scoredkmaxpercenthei'] * 0.01 * $guige['sell_price']* $sdata[2];
        					}else{
        						$scoredkmaxmoneyhei += $guige['sell_price'] * $sdata[2];
        					}
        				}
        			}elseif($product['scoredkmaxsethei']==1){
        				$scoremaxtypehei = 1;
        				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * 0.01 * $guige['sell_price'] * $sdata[2];
        			}elseif($product['scoredkmaxsethei']==2){
        				$scoremaxtypehei = 1;
        				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * $sdata[2];
        			}else{
        				$scoremaxtypehei = 1;
        				$scoredkmaxmoneyhei += 0;
        			}
        			
        			
        			//红包
        			if($product['scoredkmaxsethuang']==0){
        				if($sysset['scoredkmaxpercenthuang'] == 0){
        					$scoremaxtypehuang = 1;
        					$scoredkmaxmoneyhuang += 0;
        				}else{
        					if($sysset['scoredkmaxpercenthuang'] > 0 && $sysset['scoredkmaxpercenthuang']<100){
        						$scoredkmaxmoneyhuang += $sysset['scoredkmaxpercenthuang'] * 0.01 * $guige['sell_price']* $sdata[2];
        					}else{
        						$scoredkmaxmoneyhuang += $guige['sell_price'] * $sdata[2];
        					}
        				}
        			}elseif($product['scoredkmaxsethuang']==1){
        				$scoremaxtypehuang = 1;
        				$scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * 0.01 * $guige['sell_price'] * $sdata[2];
        			}elseif($product['scoredkmaxsethuang']==2){
        				$scoremaxtypehuang = 1;
        				$scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * $sdata[2];
        			}else{
        				$scoremaxtypehuang = 1;
        				$scoredkmaxmoneyhuang += 0;
        			}
    
    
    
    
    
    
                    //视力档案
                    $glass_record_id = 0;
                    if(getcustom('product_glass')){
                        $glass_record_id = $sdata[3]??0;
                    }
    
    				$prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>$sdata[2],'isSeckill'=>0,'glass_record_id'=>$glass_record_id];
    				
    				if($product['freighttype']==0){
    					// 检查 freightdata 是否为空，避免 explode() 报错
    					$freightdata_str = $product['freightdata'] ?? '';
    					$freightdata_arr = !empty($freightdata_str) ? explode(',',$freightdata_str) : [];
    					$fids = array_intersect($fids,$freightdata_arr);
    				}else{
    					$thisfreightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);
    					$thisfids = [];
    					foreach($thisfreightList as $v){
    						$thisfids[] = $v['id'];
    					}
    					$fids = array_intersect($fids,$thisfids);
    				}
    				$proids[] = $product['id'];
    				// 检查 cid 是否为空，避免 explode() 报错
    				$cid_str = $product['cid'] ?? '';
    				$cid_arr = !empty($cid_str) ? explode(',',$cid_str) : [];
    				$cids = array_merge($cids,$cid_arr);
    				if($product['givescore_time'] == 0){
    					$givescore += $guige['givescore'] * $sdata[2];
    					$givescorehuang+=$guige['givescorehuang'] * $sdata[2];
    				}else{
    					$givescore2 += $guige['givescore'] * $sdata[2];
    				}
    
    				if($product['to86yk_tid']){
    					$extendInput = [['key'=>'input','val1'=>'充值账号','val2'=>'请输入充值账号','val3'=>1],['key'=>'input','val1'=>'确认账号','val2'=>'请再次输入充值账号','val3'=>1]];
    				}
                    if(getcustom('pay_yuanbao') ){
                        if($product['yuanbao']>0){
                            $total_yuanbao += $product['yuanbao'];
                        }else{
                            $have_no_yuanbao = 1;
                        }
                    }
                    //如果存在，且数据存在
                    if($mjset && $mjdata){
                        //指定分类
                        if($mjset['fwtype']==1){
                            //指定分类数组
                            $cat_arr     = explode(",",$mjset['categoryids']);
                            //商品分类
                            $pro_cat_arr = explode(",",$product['cid']);
                            //交集
                            $j_arr = array_intersect($cat_arr,$pro_cat_arr);
                            if ($j_arr){
                                $mj_price += $guige['sell_price'] * $sdata[2];
                                if($product['balance']){
                                    $mj_price = $mj_price * (1-$product['balance']*0.01);
                                }
                            }
                        //指定商品
                        }else if($mjset['fwtype']==2){
                            $pro_arr = explode(",",$mjset['productids']);
                            //商品在指定商品内
                            if (in_array($product['id'], $pro_arr)){
                                $mj_price += $guige['sell_price'] * $sdata[2];
                                if($product['balance']){
                                    $mj_price = $mj_price * (1-$product['balance']*0.01);
                                }
                            }
                        }else{
                            $mj_price += $guige['sell_price'] * $sdata[2];
                            if($product['balance']){
                                $mj_price = $mj_price * (1-$product['balance']*0.01);
                            }
                        }
                    }
    			}
    			if(!$fids){
    				if(count($buydata['prodata'])>1){
    					return $this->json(['status'=>0,'msg'=>'所选择商品配送方式不同，请分别下单']);
    				}else{
    					return $this->json(['status'=>0,'msg'=>'获取配送方式失败']);
    				}
    			}
    			if(getcustom('guige_split')){
    				$rs = \app\model\ShopProduct::checkstock($prolist);
    				if($rs['status'] == 0) return $this->json($rs);
    			}
    			//会员折扣
    			$leveldk_money = 0;
    			if($userlevel && $userlevel['discount']>0 && $userlevel['discount']<10){
    				$leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
    			}
    			$totalprice = $product_price - $leveldk_money;
                $mj_price   = $mj_price - $leveldk_money;
    
    			$manjian_money = 0;
    			$moneyduan = 0;
    			if($mjdata && $mj_price>0){
                    //如果是总满减
                    if($mjset['total_status']==1){
                        //指定分类
                        if($mjset['fwtype']==1){
                            //查询他分类消费累计
                            $sum_money  = Db::name('shop_order_goods')
                                ->alias('sog')
                                ->join('shop_order so','so.id   = sog.orderid')
                                ->join('shop_product sp','sp.id = sog.proid')
                                ->where('sog.mid',mid)
                                ->where('so.status',3)
                                ->where('sp.cid','in',$mjset['categoryids'])
                                ->sum('sog.totalprice');
    
                            //分类退款累计
                            $refund_money = Db::name('shop_refund_order_goods')
                                ->alias('srog')
                                ->join('shop_order so','so.id   = srog.orderid')
                                ->join('shop_product sp','sp.id = srog.proid')
                                ->where('srog.mid',mid)
                                ->where('so.status',3)
                                ->where('so.refund_status',2)
                                ->where('sp.cid','in',$mjset['categoryids'])
                                ->sum('srog.refund_money');
    
                        //指定商品
                        }else if($mjset['fwtype']==2){
                            //查询他商品消费累计
                            $sum_money  = Db::name('shop_order_goods')
                                ->alias('sog')
                                ->join('shop_order so','so.id   = sog.orderid')
                                ->join('shop_product sp','sp.id = sog.proid')
                                ->where('sog.mid',mid)
                                ->where('so.status',3)
                                ->where('sog.proid','in',$mjset['productids'])
                                ->sum('sog.totalprice');
    
                            //商品退款累计
                            $refund_money = Db::name('shop_refund_order_goods')
                                ->alias('srog')
                                ->join('shop_order so','so.id   = srog.orderid')
                                ->join('shop_product sp','sp.id = srog.proid')
                                ->where('srog.mid',mid)
                                ->where('so.status',3)
                                ->where('so.refund_status',2)
                                ->where('srog.proid','in',$mjset['productids'])
                                ->sum('srog.refund_money');
                        //所有
                        }else{
                            //查询他累计消费多少
                            $sum_money    = Db::name('shop_order')->where('mid',mid)->where('status',3)->sum('totalprice');
                            $refund_money = Db::name('shop_order')->where('mid',mid)->where('status',3)->where('refund_status',2)->sum('refund_money');
                        }
                        $sj_money = $sum_money-$refund_money;
                        $sj_money = round($sj_money,2);
                        $all_price = $sj_money+$mj_price;
                        foreach($mjdata as $give){
                            if($all_price*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
                                $moneyduan = $give['money']*1;
                                $manjian_money = $give['jian']*1;
                            }
                        }
                    }else{
        				foreach($mjdata as $give){
        					if($mj_price*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
        						$moneyduan = $give['money']*1;
        						$manjian_money = $give['jian']*1;
        					}
        				}
                    }
    			}
    			if($manjian_money <= 0) $manjian_money = 0;
    			$totalprice = $totalprice - $manjian_money;
    			if($totalprice < 0) $totalprice = 0;
    
    			//运费
    			$freight_price = 0;
    			if($data['freight_id']){
    				$freight = Db::name('freight')->where('aid',aid)->where('id',$data['freight_id'])->find();
    				if($freight['pstype']==11){
    					$freight['type11key'] = $data['type11key'];
    				}
    				if($freight['minpriceset']==1 && $freight['minprice']>0 && $freight['minprice'] > $product_price){
    					return $this->json(['status'=>0,'msg'=>$freight['name'] . '满'.$freight['minprice'].'元起送']);
    				}
    				if(($address['name']=='' || $address['tel'] =='') && ($freight['pstype']==1 || $freight['pstype']==3) && $freight['needlinkinfo']==1){
    					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
    				}
    				
    				$rs = \app\model\Freight::getFreightPrice($freight,$address,$product_price,$totalnum,$totalweight);
    				if($rs['status']==0) return $this->json($rs);
    				$freight_price = $rs['freight_price'];
    				//判断配送时间选择是否符合要求
    				if($freight['pstimeset']==1){
    					$freight_times = explode('~',$data['freight_time']);
    					if($freight_times[1]){
    						$freighttime = strtotime(explode(' ',$freight_times[0])[0] . ' '.$freight_times[1]);
    					}else{
    						$freighttime = strtotime($freight_times[0]);
    					}
    					if(time() + $freight['psprehour']*3600 > $freighttime){
    						return $this->json(['status'=>0,'msg'=>(($freight['pstype']==0 || $freight['pstype']==2 || $freight['pstype']==10)?'配送':'提货').'时间必须在'.$freight['psprehour'].'小时之后']);
    					}
    				}
    			}elseif($product['freighttype']==3){
    				$freight = ['id'=>0,'name'=>'自动发货','pstype'=>3];
    				if(getcustom('cefang') && ($address['name']=='' || $address['tel'] =='')){
    					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
    				}
    			}elseif($product['freighttype']==4){
    				$freight = ['id'=>0,'name'=>'在线卡密','pstype'=>4];
    				if(getcustom('cefang') && ($address['name']=='' || $address['tel'] =='')){
    					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
    				}
    			}else{
    				$freight = ['id'=>0,'name'=>'包邮','pstype'=>0];
    			}
    			//优惠券
    			$new_freight_price = $freight_price;
    			$coupon_money = 0;
    			if($data['couponrid']){
    				$couponrids = explode(',',$data['couponrid']);
    				foreach($couponrids as $couponrid){
    					$couponrecord = Db::name('coupon_record')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('mid',mid)->where('id',$couponrid)->find();
    					if(!$couponrecord){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不存在']);
    					}elseif($couponrecord['status']!=0){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'已使用过了']);
    					}elseif($couponrecord['starttime'] > time()){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'尚未开始使用']);	
    					}elseif($couponrecord['endtime'] < time()){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'已过期']);	
    					}elseif($couponrecord['minprice'] > $totalprice){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);
    					}elseif($couponrecord['type']!=1 && $couponrecord['type']!=4 && $couponrecord['type']!=10){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);
    					}
                        if(empty($couponinfo)){
                            return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不存在或已作废']);
                        }
                        //0全场通用,1指定类目,2指定商品
                        if(!in_array($couponinfo['fwtype'],[0,1,2])){
                            return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'超出可用范围']);
                        }
    					if($couponrecord['from_mid']==0 && $couponinfo && $couponinfo['isgive']==2){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'仅可转赠']);
    					}
    					// 检查 usetj 是否为空，避免 explode() 报错
    					$usetj_str = $couponinfo['usetj'] ?? '';
    					$usetj = !empty($usetj_str) ? explode(',',$usetj_str) : [];
    					if(!in_array('-1',$usetj) && !in_array($this->member['levelid'],$usetj) && (!in_array('0',$usetj) || $this->member['subscribe']!=1)){
    						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不可用']);
    					}
    					if($couponinfo['fwtype']==2){//指定商品可用
    						// 检查 productids 是否为空，避免 explode() 报错
    						$productids_str = $couponinfo['productids'] ?? '';
    						$productids = !empty($productids_str) ? explode(',',$productids_str) : [];
    						if(!array_intersect($proids,$productids)){
    							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定商品可用']);
    						}
    						$thistotalprice = 0;
    						foreach($prolist as $k2=>$v2){
    							$product = $v2['product'];
    							if(in_array($product['id'],$productids)){
    								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
    							}
    						}
    						if($thistotalprice < $couponinfo['minprice']){
    							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定商品未达到'.$couponinfo['minprice'].'元']);
    						}
    					}
    					if($couponinfo['fwtype']==1){//指定类目可用
    						// 检查 categoryids 是否为空，避免 explode() 报错
    						$categoryids_str = $couponinfo['categoryids'] ?? '';
    						$categoryids = !empty($categoryids_str) ? explode(',',$categoryids_str) : [];
    						$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
    						foreach($clist as $kc=>$vc){
    							$categoryids[] = $vc['id'];
    							$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
    							$categoryids[] = $cate2['id'];
    						}
    						if(!array_intersect($cids,$categoryids)){
    							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定分类可用']);
    						}
    						$thistotalprice = 0;
    						foreach($prolist as $k2=>$v2){
    							$product = $v2['product'];
    							if(array_intersect(explode(',',$product['cid']),$categoryids)){
    								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
    							}
    						}
    						if($thistotalprice < $couponinfo['minprice']){
    							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定分类未达到'.$couponinfo['minprice'].'元']);
    						}
    					}
    
    					Db::name('coupon_record')->where('id',$couponrid)->update(['status'=>1,'usetime'=>time()]);
    					if($couponrecord['type']==4){//运费抵扣券
    						$new_freight_price = 0;
    					}elseif($couponrecord['type']==10){//折扣券
    						if($couponinfo['fwtype']==1 || $couponinfo['fwtype']==2){
    							$coupon_money += $thistotalprice * (100 - $couponrecord['discount']) * 0.01;
    						}else{
    							$coupon_money += $totalprice * (100 - $couponrecord['discount']) * 0.01;
    						}
    						if($coupon_money > $totalprice) $coupon_money = $totalprice;
    					}else{
    						$coupon_money += $couponrecord['money'];
    						if($coupon_money > $totalprice) $coupon_money = $totalprice;
    					}
    				}
    			}
    			//促销活动
                $cuxiaomoney = 0;
                if(in_array('multi_promotion',getcustom())){
                    if($data['cuxiaoid']){
                        foreach($data['cuxiaoid'] as $cuxiaoid){
                            if($cuxiaoid > 0){
                                $cuxiaoid = $cuxiaoid;
                                $cuxiaoinfo = Db::name('cuxiao')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('id',$cuxiaoid)->find();
                                if(!$cuxiaoinfo){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动不存在']);
                                }elseif($cuxiaoinfo['starttime'] > time()){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动尚未开始']);
                                }elseif($cuxiaoinfo['endtime'] < time()){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动已结束']);
                                }elseif($cuxiaoinfo['type']!=5 && $cuxiaoinfo['type']!=6 && $cuxiaoinfo['minprice'] > $totalprice){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                                }elseif(($cuxiaoinfo['type']==5 || $cuxiaoinfo['type']==6) && $cuxiaoinfo['minnum'] > $totalnum){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                                }
                                if($cuxiaoinfo['fwtype']==2){//指定商品可用
                                    $productids = explode(',',$cuxiaoinfo['productids']);
                                    if(!array_intersect($proids,$productids)){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定商品可用']);
                                    } 
                                    if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
                                        $thistotalprice = 0;
                                        foreach($prolist as $k2=>$v2){
                                            $product = $v2['product'];
                                            if(in_array($product['id'],$productids)){
                                                $thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
                                            }
                                        }
                                        if($thistotalprice < $cuxiaoinfo['minprice']){
                                            return $this->json(['status'=>0,'msg'=>'该促销活动指定商品总价未达到'.$cuxiaoinfo['minprice'].'元']);
                                        }
                                    }
                                    if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定商品是否达到件数要求
                                        $thistotalnum = 0;
                                        foreach($prolist as $k2=>$v2){
                                            $product = $v2['product'];
                                            if(in_array($product['id'],$productids)){
                                                $thistotalnum += $v2['num'];
                                            }
                                        }
                                        if($thistotalnum < $cuxiaoinfo['minnum']){
                                            return $this->json(['status'=>0,'msg'=>'该促销活动指定商品总数未达到'.$cuxiaoinfo['minnum'].'件']);
                                        }
                                    }
                                }
                                if($cuxiaoinfo['fwtype']==1){//指定类目可用
                                    $categoryids = explode(',',$cuxiaoinfo['categoryids']);
                                    $clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
                                    foreach($clist as $kc=>$vc){
                                        $categoryids[] = $vc['id'];
                                        $cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
                                        $categoryids[] = $cate2['id'];
                                    }
                                    if(!array_intersect($cids,$categoryids)){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定分类可用']);
                                    }
    								if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
                                        $thistotalprice = 0;
                                        foreach($prolist as $k2=>$v2){
                                            $product = $v2['product'];
                                            if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                                $thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
                                            }
                                        }
                                        if($thistotalprice < $cuxiaoinfo['minprice']){
                                            return $this->json(['status'=>0,'msg'=>'该促销活动指定分类总价未达到'.$cuxiaoinfo['minprice'].'元']);
                                        }
                                    }
                                    if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定类目内商品是否达到件数要求
                                        $thistotalnum = 0;
                                        foreach($prolist as $k2=>$v2){
                                            $product = $v2['product'];
                                            if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                                $thistotalnum += $v2['num'];
                                            }
                                        }
                                        if($thistotalnum < $cuxiaoinfo['minnum']){
                                            return $this->json(['status'=>0,'msg'=>'该促销活动指定分类总数未达到'.$cuxiaoinfo['minnum'].'件']);
                                        }
                                    }
                                }
                                if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==6){//满额立减 满件立减
                                    $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                                    $cuxiaomoney += $cuxiaoinfo['money'] * -1;
                                }elseif($cuxiaoinfo['type']==2){//满额赠送
                                    $cuxiaomoney += 0;
                                    $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                                    $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                                    if(!$product) return $this->json(['status'=>0,'msg'=>'赠送产品不存在']);
                                    if(!$guige) return $this->json(['status'=>0,'msg'=>'赠送产品规格不存在']);
                                    if($guige['stock'] < 1){
                                        return $this->json(['status'=>0,'msg'=>'赠送产品库存不足']);
                                    }
                                    $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                                }elseif($cuxiaoinfo['type']==3){//加价换购
                                    $cuxiaomoney += $cuxiaoinfo['money'];
                                    $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                                    $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                                    if(!$product) return $this->json(['status'=>0,'msg'=>'换购产品不存在']);
                                    if(!$guige) return $this->json(['status'=>0,'msg'=>'换购产品规格不存在']);
                                    if($guige['stock'] < 1){
                                        return $this->json(['status'=>0,'msg'=>'换购产品库存不足']);
                                    }
                                    $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                                }elseif($cuxiaoinfo['type']==4 || $cuxiaoinfo['type']==5){//满额打折 满件打折
                                    $cuxiaomoney4 = 0;
                                    if($cuxiaoinfo['fwtype']==2){
                                        $prozkArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['prozk']));
                                        $pronumArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['pronum']));
                                        foreach($prolist as $k=>$v){
                                            $product = $v['product'];
                                            if($prozkArr[$product['id']]){
                                                $prozk = $prozkArr[$product['id']];
                                            }elseif(isset($prozkArr[$product['id']])){
                                                $prozk = $cuxiaoinfo['zhekou'];
                                            }else{
                                                $prozk = 10;
                                            }
                                            if($cuxiaoinfo['type']==5 && $pronumArr[$product['id']] && intval($pronumArr[$product['id']]) > $v['num']){
                                                $prozk = 10;
                                            }
                                            $cuxiaomoney4 += $product_priceArr[$k] * (1 - $prozk * 0.1);
                                        }
                                    }elseif($cuxiaoinfo['fwtype']==1) {
                                        //分类
                                        $categoryPrice = 0;
    
                                        foreach ($prolist as $k2 => $v2) {
                                            $product = $v2['product'];
                                            $cids2 = explode(',', $product['cid']);
                                            if(array_intersect($cids2, $categoryids)) {
                                                $categoryPrice += $v2['guige']['sell_price'] * $v2['num'];
                                            }
                                        }
                                        $cuxiaomoney4 = $categoryPrice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                                    }else{
                                        $cuxiaomoney4 = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                                    }
                                    $cuxiaomoney4 = round($cuxiaomoney4,2);
                                    $manjian_money = $manjian_money + $cuxiaomoney4;
                                    $cuxiaomoney += $cuxiaomoney4 * -1;
                                }else{
                                    $cuxiaomoney += 0;
                                }
                            }else{
                                $cuxiaomoney += 0;
                            }
                        }
                    }
                }else{
                    if($data['cuxiaoid'] > 0){
                        $cuxiaoid = $data['cuxiaoid'];
                        $cuxiaoinfo = Db::name('cuxiao')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('id',$cuxiaoid)->find();
                        if(!$cuxiaoinfo){
                            return $this->json(['status'=>0,'msg'=>'该促销活动不存在']);
                        }elseif($cuxiaoinfo['starttime'] > time()){
                            return $this->json(['status'=>0,'msg'=>'该促销活动尚未开始']);
                        }elseif($cuxiaoinfo['endtime'] < time()){
                            return $this->json(['status'=>0,'msg'=>'该促销活动已结束']);
                        }elseif($cuxiaoinfo['type']!=5 && $cuxiaoinfo['type']!=6 && $cuxiaoinfo['minprice'] > $totalprice){
                            return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                        }elseif(($cuxiaoinfo['type']==5 || $cuxiaoinfo['type']==6) && $cuxiaoinfo['minnum'] > $totalnum){
                            return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                        }
                        if($cuxiaoinfo['fwtype']==2){//指定商品可用
                            $productids = explode(',',$cuxiaoinfo['productids']);
                            if(!array_intersect($proids,$productids)){
                                return $this->json(['status'=>0,'msg'=>'该促销活动指定商品可用']);
                            }
    						if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
    							$thistotalprice = 0;
    							foreach($prolist as $k2=>$v2){
    								$product = $v2['product'];
    								if(in_array($product['id'],$productids)){
    									$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
    								}
    							}
    							if($thistotalprice < $cuxiaoinfo['minprice']){
    								return $this->json(['status'=>0,'msg'=>'该促销活动指定商品未达到'.$cuxiaoinfo['minprice'].'元']);
    							}
    						}
                            if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定商品是否达到件数要求
                                $thistotalnum = 0;
                                foreach($prolist as $k2=>$v2){
                                    $product = $v2['product'];
                                    if(in_array($product['id'],$productids)){
                                        $thistotalnum += $v2['num'];
                                    }
                                }
                                if($thistotalnum < $cuxiaoinfo['minnum']){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动指定商品未达到'.$cuxiaoinfo['minnum'].'件']);
                                }
                            }
                        }
                        if($cuxiaoinfo['fwtype']==1){//指定类目可用
                            $categoryids = explode(',',$cuxiaoinfo['categoryids']);
                            $clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
                            foreach($clist as $kc=>$vc){
                                $categoryids[] = $vc['id'];
                                $cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
                                $categoryids[] = $cate2['id'];
                            }
                            if(!array_intersect($cids,$categoryids)){
                                return $this->json(['status'=>0,'msg'=>'该促销活动指定分类可用']);
                            }
    						if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
    							$thistotalprice = 0;
    							foreach($prolist as $k2=>$v2){
    								$product = $v2['product'];
    								if(array_intersect(explode(',',$product['cid']),$categoryids)){
    									$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
    								}
    							}
    							if($thistotalprice < $cuxiaoinfo['minprice']){
    								return $this->json(['status'=>0,'msg'=>'该促销活动指定分类未达到'.$cuxiaoinfo['minprice'].'元']);
    							}
    						}
                            if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定类目内商品是否达到件数要求
                                $thistotalnum = 0;
                                foreach($prolist as $k2=>$v2){
                                    $product = $v2['product'];
                                    if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                        $thistotalnum += $v2['num'];
                                    }
                                }
                                if($thistotalnum < $cuxiaoinfo['minnum']){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动指定分类未达到'.$cuxiaoinfo['minnum'].'件']);
                                }
                            }
                        }
                        if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==6){//满额立减 满件立减
                            $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                            $cuxiaomoney = $cuxiaoinfo['money'] * -1;
                        }elseif($cuxiaoinfo['type']==2){//满额赠送
                            $cuxiaomoney = 0;
                            $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                            $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                            if(!$product) return $this->json(['status'=>0,'msg'=>'赠送产品不存在']);
                            if(!$guige) return $this->json(['status'=>0,'msg'=>'赠送产品规格不存在']);
                            if($guige['stock'] < 1){
                                return $this->json(['status'=>0,'msg'=>'赠送产品库存不足']);
                            }
                            $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                        }elseif($cuxiaoinfo['type']==3){//加价换购
                            $cuxiaomoney = $cuxiaoinfo['money'];
                            $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                            $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                            if(!$product) return $this->json(['status'=>0,'msg'=>'换购产品不存在']);
                            if(!$guige) return $this->json(['status'=>0,'msg'=>'换购产品规格不存在']);
                            if($guige['stock'] < 1){
                                return $this->json(['status'=>0,'msg'=>'换购产品库存不足']);
                            }
                            $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                        }elseif($cuxiaoinfo['type']==4 || $cuxiaoinfo['type']==5){//满额打折 满件打折
                            if($cuxiaoinfo['fwtype']==2){
                                $cuxiaomoney = 0;
                                $prozkArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['prozk']));
                                $pronumArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['pronum']));
                                foreach($prolist as $k=>$v){
                                    $product = $v['product'];
                                    if($prozkArr[$product['id']]){
                                        $prozk = $prozkArr[$product['id']];
                                    }elseif(isset($prozkArr[$product['id']])){
                                        $prozk = $cuxiaoinfo['zhekou'];
                                    }else{
                                        $prozk = 10;
                                    }
                                    if($cuxiaoinfo['type']==5 && $pronumArr[$product['id']] && intval($pronumArr[$product['id']]) > $v['num']){
                                        $prozk = 10;
                                    }
                                    $cuxiaomoney += $product_priceArr[$k] * (1 - $prozk * 0.1);
                                }
                            }elseif($v['fwtype']==1) {
    							//分类
    							$categoryPrice = 0;
    							foreach ($prolist as $k2 => $v2) {
    								$product = $v2['product'];
    								$cids2 = explode(',', $product['cid']);
    								if(array_intersect($cids2, $categoryids)) {
    									$categoryPrice += $v2['guige']['sell_price'] * $v2['num'];
    								}
    							}
    							$cuxiaomoney = $categoryPrice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
    						}else{
                                $cuxiaomoney = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                            }
                            $cuxiaomoney = round($cuxiaomoney,2);
                            $manjian_money = $manjian_money + $cuxiaomoney;
                            $cuxiaomoney = $cuxiaomoney * -1;
                        }else{
                            $cuxiaomoney = 0;
                        }
                        if(getcustom('plug_tengrui')) {
                            $tr_check = new \app\common\TengRuiCheck();
                            //判断是否是否符合会员认证、会员关系、一户
                            $check_cuxiao = $tr_check->check_cuxiao($this->member,$cuxiaoinfo);
                            if($check_cuxiao && $check_cuxiao['status'] == 0){
                                $cuxiaomoney = 0;
                            }
                            $cuxiao_tr_roomId = $check_cuxiao['tr_roomId'];
                        }
                    }else{
                        $cuxiaomoney = 0;
                    }
                }
    
    			$totalprice = $totalprice - $coupon_money + $cuxiaomoney;
    			$totalprice = $totalprice + $new_freight_price;
    
                //发票
                $invoice_money = 0;
                if($store_info['invoice'] && $store_info['invoice_rate'] > 0 && $data['invoice']){
                    $invoice_money = round($totalprice * $store_info['invoice_rate'] / 100,2);
                    $totalprice = $totalprice + $invoice_money;
                }
    			//积分抵扣
    			$scoredkscore = 0;
    			$scoredk_money = 0;
    			if($post['usescore']==1){
    				$adminset = Db::name('admin_set')->where('aid',aid)->find();
    				$score2money = $adminset['score2money'];
    				$scoredkmaxpercent = $adminset['scoredkmaxpercent'];
    				$scorebdkyf = $adminset['scorebdkyf'];
    				$scoredk_money = $this->member['score'] * $score2money;
    				if($scorebdkyf == 1){//积分不抵扣运费
    					if($scoredk_money > $totalprice - $new_freight_price) $scoredk_money = $totalprice - $new_freight_price;
    				}else{
    					if($scoredk_money > $totalprice) $scoredk_money = $totalprice;
    				}
    				if($scoremaxtype == 0){
    					if($scoredkmaxpercent >= 0 && $scoredkmaxpercent < 100 && $scoredk_money > 0 && $scoredk_money > $totalprice * $scoredkmaxpercent * 0.01){
    						$scoredk_money = $totalprice * $scoredkmaxpercent * 0.01;
    					}
    				}else{
    					if($scoredk_money > $scoredkmaxmoney) $scoredk_money = $scoredkmaxmoney;
    				}
    				$totalprice = $totalprice - $scoredk_money;
    				$totalprice = round($totalprice*100)/100;
    				if($scoredk_money > 0){
    					$scoredkscore = intval($scoredk_money / $score2money);
    				}
    			}
    			
    			//现金券抵扣
    			$scoredkscorehei = 0;
    			$scoredk_moneyhei = 0;
    			if($post['usescorehei']==1){
    				$adminset = Db::name('admin_set')->where('aid',aid)->find();
    				$score2moneyhei = $adminset['score2moneyhei'];
    				$scoredkmaxpercenthei = $adminset['scoredkmaxpercenthei'];
    				$scorebdkyfhei = $adminset['scorebdkyfhei'];
    				$scoredk_moneyhei = $this->member['heiscore'] * $score2moneyhei;
    				if($scorebdkyfhei == 1){//积分不抵扣运费
    					if($scoredk_moneyhei > $totalprice - $new_freight_price) $scoredk_moneyhei = $totalprice - $new_freight_price;
    				}else{
    					if($scoredk_moneyhei > $totalprice) $scoredk_moneyhei = $totalprice;
    				}
    
    				if($scoremaxtypehei == 0){
    					if($scoredkmaxpercenthei >= 0 && $scoredkmaxpercenthei < 100 && $scoredk_moneyhei > 0 && $scoredk_moneyhei > $totalprice * $scoredkmaxpercenthei * 0.01){
    						$scoredk_moneyhei = $totalprice * $scoredkmaxpercenthei * 0.01;
    					}
    				}else{
    					if($scoredk_moneyhei > $scoredkmaxmoneyhei) $scoredk_moneyhei = $scoredkmaxmoneyhei;
    				}
    				$totalprice = $totalprice - $scoredk_moneyhei;
    				$totalprice = round($totalprice*100)/100;
    				if($scoredk_moneyhei > 0){
    					$scoredkscorehei = intval($scoredk_moneyhei / $score2moneyhei);
    				}
    			}
    			
    	        //$scoremaxtypehuang
        		//$scoredkmaxmoneyhuang
                //红包抵扣
                $scoredkscorehuang = 0;
                $scoredk_moneyhuang = 0;
                if($post['usescorehuang']==1){
                
                    $adminset = Db::name('admin_set')->where('aid',aid)->find();
                    $score2moneyhuang = $adminset['score2moneyhuang'];
                    $scoredkmaxpercenthuang = $adminset['scoredkmaxpercenthuang'];
                    $scorebdkyfhuang = $adminset['scorebdkyfhuang'];
                    $scoredk_moneyhuang = $this->member['scorehuang'] * $score2moneyhuang;
                    if($scorebdkyfhuang == 1){//积分不抵扣运费
                        if($scoredk_moneyhuang > $totalprice - $new_freight_price) $scoredk_moneyhuang = $totalprice - $new_freight_price;
                    }else{
                        if($scoredk_moneyhuang > $totalprice) $scoredk_moneyhuang = $totalprice;
                    }
    
                    if($scoremaxtypehuang == 0){
                        if($scoredkmaxpercenthuang >= 0 && $scoredkmaxpercenthuang < 100 && $scoredk_moneyhuang > 0 && $scoredk_moneyhuang > $totalprice * $scoredkmaxpercenthuang * 0.01){
                            $scoredk_moneyhuang = $totalprice * $scoredkmaxpercenthuang * 0.01;
                        }
                    }else{
                        if($scoredk_moneyhuang > $scoredkmaxmoneyhuang) $scoredk_moneyhuang = $scoredkmaxmoneyhuang;
                    }
    
                    $totalprice = $totalprice - $scoredk_moneyhuang;
                    $totalprice = round($totalprice*100)/100;
                    if($scoredk_moneyhuang > 0){
                        $scoredkscorehuang = intval($scoredk_moneyhuang / $score2moneyhuang);
                    }
                }
                
      
    
    
    			//余额抵扣
    			$scoredkscoreyu = 0;
    			$scoredk_moneyyu = 0;
    			if($post['usescoreyu']==1){
    				$adminset = Db::name('admin_set')->where('aid',aid)->find();
    				$score2moneyyu = $adminset['score2moneyyu'];
    				$scoredkmaxpercentyu = $adminset['scoredkmaxpercentyu'];
    				$scorebdkyfyu = $adminset['scorebdkyfyu'];
    				$scoredk_moneyyu = $this->member['money'] * $score2moneyyu;
    				if($scorebdkyfyu == 1){//积分不抵扣运费
    					if($scoredk_moneyyu > $totalprice - $new_freight_price) $scoredk_moneyyu = $totalprice - $new_freight_price;
    				}else{
    					if($scoredk_moneyyu > $totalprice) $scoredk_moneyyu = $totalprice;
    				}
    				
    				if($scoremaxtypeyu == 0){
    					if($scoredkmaxpercentyu >= 0 && $scoredkmaxpercentyu < 100 && $scoredk_moneyyu > 0 && $scoredk_moneyyu > $totalprice * $scoredkmaxpercentyu * 0.01){
    						$scoredk_moneyyu = $totalprice * $scoredkmaxpercentyu * 0.01;
    					}
    				}else{
    					if($scoredk_moneyyu > $scoredkmaxmoneyyu) $scoredk_moneyyu = $scoredkmaxmoneyyu;
    				}
    				$totalprice = $totalprice - $scoredk_moneyyu;
    				$totalprice = round($totalprice*100)/100;
    				if($scoredk_moneyyu > 0){
    					$scoredkscoreyu = intval($scoredk_moneyyu / $score2moneyyu);
    				}
    			}
    			
    			$orderdata = [];
    			$orderdata['aid'] = aid;
    			$orderdata['mid'] = mid;
    			$orderdata['bid'] = $data['bid'];
    			if(count($buydata) > 1){
    				$orderdata['ordernum'] = $ordernum.'_'.$i;
    			}else{
    				$orderdata['ordernum'] = $ordernum;
    			}
    			$orderdata['title'] = $title.(count($prodata)>1?'等':'');
                $orderdata['ti'] = 1;
    			$orderdata['linkman'] = $address['name'];
                $orderdata['company'] = $address['company'];
    			$orderdata['tel'] = $address['tel'];
    			$orderdata['area'] = $address['area'];
    			$orderdata['address'] = $address['address'];
    			$orderdata['longitude'] = $address['longitude'];
    			$orderdata['latitude'] = $address['latitude'];
    			$orderdata['area2'] = $address['province'].','.$address['city'].','.$address['district'];
    			
    // 			$orderdata['totalprice'] = $product_price;
    				// $orderdata['product_price'] = $product_price;
    			$orderdata['totalprice'] = 0;
    			$orderdata['product_price'] = $product_price;
    			$orderdata['leveldk_money'] = $leveldk_money;	//会员折扣
    			$orderdata['manjian_money'] = $manjian_money;	//满减活动
    			$orderdata['scoredk_money'] = $scoredk_money;	//积分抵扣
    			$orderdata['scoredk_moneyhei'] = $scoredk_moneyhei;	//现金券抵扣
                $orderdata['scoredk_moneyhuang'] = $scoredk_moneyhuang;	//红包抵扣
    			$orderdata['scoredk_moneyyu'] = $scoredk_moneyyu;	//余额抵扣
    
    			$orderdata['coupon_money'] = $coupon_money + $freight_price - $new_freight_price;	//优惠券抵扣
    			$orderdata['scoredkscore'] = $scoredkscore;		//抵扣掉的积分
    			$orderdata['scoredkscorehei'] = $scoredkscorehei;		//抵扣掉的现金券
                $orderdata['scoredkscorehuang'] = $scoredkscorehuang;		//抵扣掉的红包
    			$orderdata['scoredkscoreyu'] = $scoredkscoreyu;		//抵扣掉的现金券
                $orderdata['balance_price'] = $balance_price;	//尾款金额 定制的
    			$orderdata['coupon_rid'] = $data['couponrid'];
    			$orderdata['freight_price'] = $freight_price; //运费
                $orderdata['invoice_money'] = $invoice_money; //发票
    			$orderdata['givescore'] = $givescore;
    			$orderdata['givescorehuang'] = $givescorehuang;
    			$orderdata['givescore2'] = $givescore2;
    			if($freight && ($freight['pstype']==0 || $freight['pstype']==10)){ //快递
    				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
    				$orderdata['freight_type'] = $freight['pstype'];
    			}elseif($freight && $freight['pstype']==1){ //到店自提
    				$orderdata['mdid'] = $data['storeid'];
    				$mendian = Db::name('mendian')->where('aid',aid)->where('id',$data['storeid'])->find();
    				$orderdata['freight_text'] = $freight['name'].'['.$mendian['name'].']';
    				$orderdata['area2'] = $mendian['area'];
    				$orderdata['freight_type'] = 1;
                }elseif($freight && $freight['pstype']==5){ //门店配送
                    $orderdata['mdid'] = $data['storeid'];
                    $mendian = Db::name('mendian')->where('aid',aid)->where('id',$data['storeid'])->find();
                    $orderdata['freight_text'] = $freight['name'].'['.$mendian['name'].']';
                    $orderdata['area2'] = $mendian['area'];
                    $orderdata['freight_type'] = 5;
    			}elseif($freight && $freight['pstype']==2){ //同城配送
    				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
    				$orderdata['freight_type'] = 2;
    			}elseif($freight && $freight['pstype']==12){ //app配送
    				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
    				$orderdata['freight_type'] = 2;
    			}elseif($freight && ($freight['pstype']==3 || $freight['pstype']==4)){ //自动发货 在线卡密
    				$orderdata['freight_text'] = $freight['name'];
    				$orderdata['freight_type'] = $freight['pstype'];
    			}elseif($freight && $freight['pstype']==11){ //选择物流配送
    				$type11pricedata = json_decode($freight['type11pricedata'],true);
    				$orderdata['freight_text'] = $type11pricedata[$freight['type11key']]['name'].'('.$freight_price.'元)';
    				$orderdata['freight_type'] = $freight['pstype'];
    				$orderdata['freight_content'] = jsonEncode($type11pricedata[$freight['type11key']]);
    			}else{
    				$orderdata['freight_text'] = '包邮';
    			}
    
    			if($sysset['areafenhong_checktype'] == 1 && $orderdata['tel']){
    				$addressrs = getaddressfromtel($orderdata['tel']);
    				if($addressrs && $addressrs['province']){
    					$orderdata['area2'] = $addressrs['province'].','.$addressrs['city'];
    				}
    			}
    
    			$orderdata['freight_id'] = $freight['id'];
    			$orderdata['freight_time'] = $data['freight_time']; //配送时间
    			$orderdata['createtime'] = time();
    			$orderdata['platform'] = platform;
    			$orderdata['hexiao_code'] = random(16);
    			$orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=shop&co='.$orderdata['hexiao_code']));
                if(getcustom('hexiao_member')){
                    $orderdata['hexiao_code_member'] = mt_rand(1000,9999);
                }
    
                if(getcustom('buy_selectmember')){
                    if($post['checkmemid']) $orderdata['checkmemid'] = $post['checkmemid'];
                }
                if(getcustom('pay_yuanbao')){
                    $orderdata['total_yuanbao']   = $total_yuanbao;
                    $orderdata['have_no_yuanbao'] = $have_no_yuanbao;
                }
                if(getcustom('plug_tengrui')){
                    $orderdata['manjian_tr_roomId']  = $manjian_tr_roomId?$manjian_tr_roomId:0;
                    $orderdata['cuxiao_tr_roomId']   = $cuxiao_tr_roomId?$cuxiao_tr_roomId:0;
                    $orderdata['cuxiao_money']       = abs($cuxiaomoney);
                    $orderdata['cuxiao_id']          = $cuxiaoid?$cuxiaoid:0;
                }
                
                $orderdata['kfj_shengyu']=$orderdata['totalprice'];
                $orderdata['cyjj_shengyu']=$orderdata['totalprice'];
                $orderdata['status'] = 1;
                
    			$orderid = Db::name('shop_order')->insertGetId($orderdata);
    
    			\app\model\Freight::saveformdata($orderid,'shop_order',$freight['id'],$data['formdata'],$extendInput);
    // 			$payorderid = \app\model\Payorder::createorder(aid,$orderdata['bid'],$orderdata['mid'],'shop',$orderid,$orderdata['ordernum'],$orderdata['title'],$orderdata['totalprice'],$orderdata['scoredkscore']);
    
    			if($balance_price > 0){
    				$balancedata = [];
    				$balancedata['aid'] = aid;
    				$balancedata['bid'] = $orderdata['bid'];
    				$balancedata['mid'] = $orderdata['mid'];
    				$balancedata['orderid'] = $orderid;
    				$balancedata['ordernum'] = $orderdata['ordernum'];
    				$balancedata['title'] = $orderdata['title'];
    				$balancedata['money'] = $orderdata['balance_price'];
    				$balancedata['type'] = 'balance';
    				$balancedata['score'] = 0;
    				$balancedata['createtime'] = time();
    				$balancedata['status'] = 0;
    				$balance_pay_orderid = Db::name('payorder')->insertGetId($balancedata);
    				Db::name('shop_order')->where('id',$orderid)->update(['balance_pay_orderid'=>$balance_pay_orderid]);
    			}
    
    			$alltotalprice += $orderdata['totalprice'];
    			$alltotalscore += $orderdata['scoredkscore'];
    			
    			//是否是复购
    			$hasordergoods = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('status','in','1,2,3')->find();
    			if($hasordergoods){
    				$isfg = 1;
    			}else{
    				$isfg = 0;
    			}
    
                if(getcustom('everyday_hongbao')) {
                    $hd = Db::name('hongbao_everyday')->where('aid', aid)->find();
                }
    			$istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
    			$istc2 = 0;
    			$istc3 = 0;
    			$istc11 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
    			$istc22 = 0;
    			$istc33 = 0;
    		
    			foreach($prolist as $key=>$v){
    				$product = $v['product'];
    				$guige = $v['guige'];
    				$num = $v['num'];
    				
    				$ogdata = [];
    				$ogdata['ti'] = 1;
    				$ogdata['aid'] = aid;
    				$ogdata['bid'] = $product['bid'];
    				$ogdata['mid'] = mid;
    				$ogdata['orderid'] = $orderid;
    				$ogdata['ordernum'] = $orderdata['ordernum'];
    				$ogdata['proid'] = $product['id'];
    				$ogdata['name'] = $product['name'];
    				$ogdata['pic'] = $product['pic'];
    				$ogdata['procode'] = $product['procode'];
                    $ogdata['barcode'] = $product['barcode'];
    				$ogdata['ggid'] = $guige['id'];
    				$ogdata['ggname'] = $guige['name'];
    				$ogdata['cid'] = $product['cid'];
    				$ogdata['num'] = $num;
    				$ogdata['cost_price'] = $guige['cost_price'];
    				$ogdata['sell_price'] = $guige['sell_price'];
    				$ogdata['totalprice'] = 0;
                    $ogdata['total_weight'] = $num * $guige['weight'];
    				$ogdata['status'] = 1;
    				$ogdata['createtime'] = time();
    				$ogdata['isfg'] = $isfg;
    				if($product['to86yk_tid']){
    					$ogdata['to86yk_tid'] = $product['to86yk_tid'];
    				}
    				if($product['fenhongset'] == 0){ //不参与分红
    					$ogdata['isfenhong'] = 2;
    				}
    				
    				$og_totalprice = $ogdata['totalprice'];
    				if($product['balance'] > 0){
    					$og_totalprice = $og_totalprice * (1 - $product['balance']*0.01);
    				}
    
                    $allproduct_price = $product_price;
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    $og_hei_money = 0;
                    if($allproduct_price > 0 && $og_totalprice > 0){
                        if($leveldk_money){
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if($coupon_money){
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if($scoredk_money){
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if($manjian_money){
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                        
                        //黑积分抵现
                        if($scoredk_moneyhei){
                            $og_hei_money = $og_totalprice / $allproduct_price * $scoredk_moneyhei;
                        }
                    }
                
                    $ogdata['scoredk_money'] = $og_scoredk_money;
                    $ogdata['leveldk_money'] = $og_leveldk_money;
                    $ogdata['manjian_money'] = $og_manjian_money;
                    $ogdata['coupon_money'] = $og_coupon_money;
                    $ogdata['hei_money'] = $og_hei_money;
                    if($product['bid'] > 0) {
                        $totalprice_business = $og_totalprice - $og_manjian_money - $og_coupon_money - $og_hei_money;
                        //商品独立费率
                        if($product['feepercent'] != '' && $product['feepercent'] != null && $product['feepercent'] >= 0) {
                            $ogdata['business_total_money'] = $totalprice_business * (100-$product['feepercent']) * 0.01;
                        } else {
                            //商户费率
                            $ogdata['business_total_money'] = $totalprice_business * (100-$store_info['feepercent']) * 0.01;
                        }
                    }
    
    				//计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
    				if($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2){
    					$og_totalprice = $og_totalprice - $og_leveldk_money - $og_scoredk_money - $og_manjian_money - $og_hei_money;
    					if($couponrecord['type']!=4) {//运费抵扣券
    						$og_totalprice -= $og_coupon_money;
    					}
    					$og_totalprice = round($og_totalprice,2);
    					if($og_totalprice < 0) $og_totalprice = 0;
    				}
    				
    				
    				$ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额
    				
    				//计算佣金的商品金额
    				$commission_totalprice =0;
    				if($sysset['fxjiesuantype']==1){ //按成交价格
    					$commission_totalprice = $ogdata['real_totalprice'];
    					if($commission_totalprice < 0) $commission_totalprice = 0;
    				}
    				$commission_totalpriceCache = $commission_totalprice;
    				if($sysset['fxjiesuantype']==2){ //按销售利润
    					$commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
    					if($commission_totalprice < 0) $commission_totalprice = 0;
    				}
                    if(getcustom('pay_yuanbao')){
                        $ogdata['yuanbao']       = $product['yuanbao'];
                        $ogdata['total_yuanbao'] = $num*$product['yuanbao'];
                    }
                    if(getcustom('plug_tengrui')) {
                        $ogdata['tr_roomId'] = $product['tr_roomId'];
                    }
                    if(getcustom('product_glass')){
                        $glass_record_id = $v['glass_record_id'];
                        if($glass_record_id) {
                            $glassrecord = Db::name('glass_record')->where('aid', aid)->where('id', $glass_record_id)->find();
                            if ($glassrecord) {
                                $orderglassrecord = [
                                    'name' => $glassrecord['name'],
                                    'desc' => $glassrecord['desc'],
                                    'degress_left' => $glassrecord['degress_left'],
                                    'degress_right' => $glassrecord['degress_right'],
                                    'ipd' => $glassrecord['ipd'],
                                    'correction_left' => $glassrecord['correction_left'],
                                    'correction_right' => $glassrecord['correction_right'],
                                    'is_ats' => $glassrecord['is_ats'],
                                    'ats_left' => $glassrecord['ats_left'],
                                    'ats_right' => $glassrecord['ats_right'],
                                    'ats_zright' => $glassrecord['ats_zright'],
                                    'ats_zleft' => $glassrecord['ats_zleft'],
                                    'type' => $glassrecord['type'],
                                    'mid' => $glassrecord['mid'],
                                    'createtime' => time(),
                                    'aid' => aid,
                                    'bid' => $orderdata['bid'] ?? 0,
                                    'glass_record_id' => $glassrecord['id']
                                ];
                                $order_glass_record_id = Db::name('order_glass_record')->insertGetId($orderglassrecord);
                                $ogdata['glass_record_id'] = $order_glass_record_id;
                            }
                        }
                    }
                    //
                    $ogdata['fenhongbili'] =$product['fenhongbili'];
                    $ogdata['jiandianjiangli'] =$product['jiandian'];
                    $ogdata['koulvjifen'] =0;
                    $ogdata['xiaofeizhifan'] =0;
                     $ogdata['is_yeji'] = $product['is_yeji'];
                    //扣除绿积分和返消费值
                    if($product['xiaofeizhi'])
                    {
                        $xiaofeizhi = json_decode($product['xiaofeizhi'],1);
                        $xiaofeifan = $xiaofeizhi[$this->member['levelid']];
                        $xiaofeifan = $xiaofeifan*$num;
                        $xiaofeizhicount = $xiaofeifan;
                        //转换绿积分
                        //绿积分的价值 $sysset['lvjifenjiazhi']
                         //消费值绿积分转换 xiaofeizhizhuan
                          $kouchulvjifen = $xiaofeizhicount*$sysset['xiaofeizhizhuan']*0.01/$sysset['lvjifenjiazhi'];
                          $kouchulvjifen = round($kouchulvjifen,2);
                          $ogdata['koulvjifen'] =$kouchulvjifen;
                          $ogdata['xiaofeizhifan'] =$xiaofeizhicount;
                    }
                    //计算静态佣金和静态积分
                    $agleveldata = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
                    
                    $jingyongcommission_totalprice = $ogdata['totalprice']*$agleveldata['fenyong_zong']*0.01;
                    $ogdata['jingyong'] =round($jingyongcommission_totalprice*$agleveldata['fenyong_yongjin']*0.01,2);
                    $ogdata['jingji'] =round($jingyongcommission_totalprice*$agleveldata['fenyong_jifen']*0.01,2);
                    // var_dump($ogdata);die;
    				$ogid = Db::name('shop_order_goods')->insertGetId($ogdata);
    				$parent1 = [];$parent2 = [];$parent3 = [];$parent4 = [];
    				$ogupdate = [];
    				$ogupdate['fhparent2commission'] = 0;
                    $ogupdate['fhparent3commission'] = 0;
                    $ogupdate['fhparent4commission'] = 0;
    				$agleveldata = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
    				if($agleveldata['can_agent'] > 0 && $agleveldata['commission1own']==1){
    					$this->member['pid'] = mid;
    				}
    				if($product['commissionset']!=-1){
    					if($this->member['pid']){
    						$parent1 = Db::name('member')->where('aid',aid)->where('id',$this->member['pid'])->find();
    						if($parent1){
    							$agleveldata1 = Db::name('member_level')->where('aid',aid)->where('id',$parent1['levelid'])->find();
    							// 检查 commission_appointlevelid 是否为空，避免 explode() 报错
    							$appointlevelid_str = $agleveldata1['commission_appointlevelid'] ?? '';
    							$appointlevelid_arr = !empty($appointlevelid_str) ? explode(',',$appointlevelid_str) : [];
    							if($agleveldata1['can_agent']!=0 && (!$agleveldata1['commission_appointlevelid'] || in_array($this->member['levelid'],$appointlevelid_arr))){
    								$ogupdate['parent1'] = $parent1['id'];
    							}
    						}
    					}
    					if($parent1['pid']){
    						$parent2 = Db::name('member')->where('aid',aid)->where('id',$parent1['pid'])->find();
    						if($parent2){
    							$agleveldata2 = Db::name('member_level')->where('aid',aid)->where('id',$parent2['levelid'])->find();
    							if($agleveldata2['can_agent']>1 && (!$agleveldata2['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata2['commission_appointlevelid'])))){
    								$ogupdate['parent2'] = $parent2['id'];
    							}
    						}
    					}
    					if($parent2['pid']){
    						$parent3 = Db::name('member')->where('aid',aid)->where('id',$parent2['pid'])->find();
    						if($parent3){
    							$agleveldata3 = Db::name('member_level')->where('aid',aid)->where('id',$parent3['levelid'])->find();
    							if($agleveldata3['can_agent']>2 && (!$agleveldata3['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata3['commission_appointlevelid'])))){
    								$ogupdate['parent3'] = $parent3['id'];
    							}
    						}
    					}
                        if($parent3['pid']){
                            $parent4 = Db::name('member')->where('aid',aid)->where('id',$parent3['pid'])->find();
                            if($parent4){
                                $agleveldata4 = Db::name('member_level')->where('aid',aid)->where('id',$parent4['levelid'])->find();
    							if($product['commissionpingjiset'] != 0){
    								if($product['commissionpingjiset'] == 1){
    									$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
    									$agleveldata4['commission_parent_pj'] = $commissionpingjidata1[$agleveldata4['id']];
    								}elseif($product['commissionpingjiset'] == 2){
    									$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
    									$agleveldata4['commission_parent_pj'] = $commissionpingjidata2[$agleveldata4['id']];
    								}else{
    									$agleveldata4['commission_parent_pj'] = 0;
    								}
    							}
                                //持续推荐奖励
                                if($agleveldata4['can_agent'] > 0 && ($agleveldata4['commission_parent'] > 0 || ($parent4['levelid']==$parent3['levelid'] && $agleveldata4['commission_parent_pj'] > 0))){
                                    $ogupdate['parent4'] = $parent4['id'];
                                }else{
                                     $ogupdate['parent4'] = $parent4['id'];
                                }
                            }
                        }
                        if($parent4['pid']){
    						$parent5 = Db::name('member')->where('aid',aid)->where('id',$parent4['pid'])->find();
    						if($parent5){
    							$agleveldata5 = Db::name('member_level')->where('aid',aid)->where('id',$parent5['levelid'])->find();
    							if($agleveldata5['can_agent']>2 && (!$agleveldata5['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata5['commission_appointlevelid'])))){
    								$ogupdate['parent5'] = $parent5['id'];
    							}
    						}
    					}
    					
    					if($parent5['pid']){
    						$parent6 = Db::name('member')->where('aid',aid)->where('id',$parent5['pid'])->find();
    						if($parent6){
    							$agleveldata6 = Db::name('member_level')->where('aid',aid)->where('id',$parent6['levelid'])->find();
    							if($agleveldata6['can_agent']>2 && (!$agleveldata6['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata6['commission_appointlevelid'])))){
    								$ogupdate['parent6'] = $parent6['id'];
    							}
    						}
    					}
    					if($parent6['pid']){
    						$parent7 = Db::name('member')->where('aid',aid)->where('id',$parent6['pid'])->find();
    						if($parent7){
    							$agleveldata7 = Db::name('member_level')->where('aid',aid)->where('id',$parent7['levelid'])->find();
    							if($agleveldata7['can_agent']>2 && (!$agleveldata7['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata7['commission_appointlevelid'])))){
    								$ogupdate['parent7'] = $parent7['id'];
    							}
    						}
    					}
    					if($parent7['pid']){
    						$parent8 = Db::name('member')->where('aid',aid)->where('id',$parent7['pid'])->find();
    						if($parent8){
    							$agleveldata8 = Db::name('member_level')->where('aid',aid)->where('id',$parent8['levelid'])->find();
    							if($agleveldata8['can_agent']>2 && (!$agleveldata8['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata8['commission_appointlevelid'])))){
    								$ogupdate['parent8'] = $parent8['id'];
    							}
    						}
    					}
    					if($parent8['pid']){
    						$parent9 = Db::name('member')->where('aid',aid)->where('id',$parent8['pid'])->find();
    						if($parent9){
    							$agleveldata9 = Db::name('member_level')->where('aid',aid)->where('id',$parent9['levelid'])->find();
    							if($agleveldata9['can_agent']>2 && (!$agleveldata9['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata9['commission_appointlevelid'])))){
    								$ogupdate['parent9'] = $parent9['id'];
    							}
    						}
    					}
    					if($parent9['pid']){
    						$parent10 = Db::name('member')->where('aid',aid)->where('id',$parent9['pid'])->find();
    						if($parent10){
    							$agleveldata10 = Db::name('member_level')->where('aid',aid)->where('id',$parent10['levelid'])->find();
    							if($agleveldata10['can_agent']>2 && (!$agleveldata10['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata10['commission_appointlevelid'])))){
    								$ogupdate['parent10'] = $parent10['id'];
    							}
    						}
    					}
    					if($parent10['pid']){
    						$parent11 = Db::name('member')->where('aid',aid)->where('id',$parent10['pid'])->find();
    						if($parent11){
    							$agleveldata11 = Db::name('member_level')->where('aid',aid)->where('id',$parent11['levelid'])->find();
    							if($agleveldata11['can_agent']>2 && (!$agleveldata11['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata11['commission_appointlevelid'])))){
    								$ogupdate['parent11'] = $parent11['id'];
    							}
    						}
    					}
    					if($parent11['pid']){
    						$parent12 = Db::name('member')->where('aid',aid)->where('id',$parent11['pid'])->find();
    						if($parent12){
    							$agleveldata12 = Db::name('member_level')->where('aid',aid)->where('id',$parent12['levelid'])->find();
    							if($agleveldata12['can_agent']>2 && (!$agleveldata12['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata12['commission_appointlevelid'])))){
    								$ogupdate['parent12'] = $parent12['id'];
    							}
    						}
    					}
    					if($parent12['pid']){
    						$parent13 = Db::name('member')->where('aid',aid)->where('id',$parent12['pid'])->find();
    						if($parent13){
    							$agleveldata13 = Db::name('member_level')->where('aid',aid)->where('id',$parent13['levelid'])->find();
    							if($agleveldata13['can_agent']>2 && (!$agleveldata13['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata13['commission_appointlevelid'])))){
    								$ogupdate['parent13'] = $parent13['id'];
    							}
    						}
    					}
    					if($parent13['pid']){
    						$parent14 = Db::name('member')->where('aid',aid)->where('id',$parent13['pid'])->find();
    						if($parent14){
    							$agleveldata14 = Db::name('member_level')->where('aid',aid)->where('id',$parent14['levelid'])->find();
    							if($agleveldata14['can_agent']>2 && (!$agleveldata14['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata14['commission_appointlevelid'])))){
    								$ogupdate['parent14'] = $parent14['id'];
    							}
    						}
    					}
    					if($parent14['pid']){
    						$parent15 = Db::name('member')->where('aid',aid)->where('id',$parent14['pid'])->find();
    						if($parent15){
    							$agleveldata15 = Db::name('member_level')->where('aid',aid)->where('id',$parent15['levelid'])->find();
    							if($agleveldata15['can_agent']>2 && (!$agleveldata15['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata15['commission_appointlevelid'])))){
    								$ogupdate['parent15'] = $parent15['id'];
    							}
    						}
    					}
    					if($parent15['pid']){
    						$parent16 = Db::name('member')->where('aid',aid)->where('id',$parent15['pid'])->find();
    						if($parent16){
    							$agleveldata16 = Db::name('member_level')->where('aid',aid)->where('id',$parent16['levelid'])->find();
    							if($agleveldata16['can_agent']>2 && (!$agleveldata16['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata16['commission_appointlevelid'])))){
    								$ogupdate['parent16'] = $parent16['id'];
    							}
    						}
    					}
    					if($parent16['pid']){
    						$parent17 = Db::name('member')->where('aid',aid)->where('id',$parent16['pid'])->find();
    						if($parent17){
    							$agleveldata17 = Db::name('member_level')->where('aid',aid)->where('id',$parent17['levelid'])->find();
    							if($agleveldata17['can_agent']>2 && (!$agleveldata17['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata17['commission_appointlevelid'])))){
    								$ogupdate['parent17'] = $parent17['id'];
    							}
    						}
    					}
    					if($parent17['pid']){
    						$parent18 = Db::name('member')->where('aid',aid)->where('id',$parent17['pid'])->find();
    						if($parent18){
    							$agleveldata18 = Db::name('member_level')->where('aid',aid)->where('id',$parent18['levelid'])->find();
    							if($agleveldata18['can_agent']>2 && (!$agleveldata18['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata18['commission_appointlevelid'])))){
    								$ogupdate['parent18'] = $parent18['id'];
    							}
    						}
    					}
    					if($parent18['pid']){
    						$parent19 = Db::name('member')->where('aid',aid)->where('id',$parent18['pid'])->find();
    						if($parent19){
    							$agleveldata19 = Db::name('member_level')->where('aid',aid)->where('id',$parent19['levelid'])->find();
    							if($agleveldata19['can_agent']>2 && (!$agleveldata19['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata19['commission_appointlevelid'])))){
    								$ogupdate['parent19'] = $parent19['id'];
    							}
    						}
    					}
    					if($parent19['pid']){
    						$parent20 = Db::name('member')->where('aid',aid)->where('id',$parent19['pid'])->find();
    						if($parent20){
    							$agleveldata20 = Db::name('member_level')->where('aid',aid)->where('id',$parent20['levelid'])->find();
    							if($agleveldata20['can_agent']>2 && (!$agleveldata20['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata20['commission_appointlevelid'])))){
    								$ogupdate['parent20'] = $parent20['id'];
    							}
    						}
    					}
    				// 	var_dump($ogupdate);die;
    					if($product['commissionset']==1){//按商品设置的分销比例
    						$commissiondata = json_decode($product['commissiondata1'],true);
    						if($commissiondata){
    							if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
    							if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
    							if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
    							if($agleveldata4) $ogupdate['parent4commission'] = $commissiondata[$agleveldata4['id']]['commission4'] * $commission_totalprice * 0.01;
    							if($agleveldata5) $ogupdate['parent5commission'] = $commissiondata[$agleveldata5['id']]['commission5'] * $commission_totalprice * 0.01;
    							if($agleveldata6) $ogupdate['parent6commission'] = $commissiondata[$agleveldata6['id']]['commission6'] * $commission_totalprice * 0.01;
    							if($agleveldata7) $ogupdate['parent7commission'] = $commissiondata[$agleveldata7['id']]['commission7'] * $commission_totalprice * 0.01;
    							if($agleveldata8) $ogupdate['parent8commission'] = $commissiondata[$agleveldata8['id']]['commission8'] * $commission_totalprice * 0.01;
    							if($agleveldata9) $ogupdate['parent9commission'] = $commissiondata[$agleveldata9['id']]['commission9'] * $commission_totalprice * 0.01;
    							if($agleveldata10) $ogupdate['parent10commission'] = $commissiondata[$agleveldata10['id']]['commission10'] * $commission_totalprice * 0.01;
    							if($agleveldata11) $ogupdate['parent11commission'] = $commissiondata[$agleveldata11['id']]['commission11'] * $commission_totalprice * 0.01;
    							if($agleveldata12) $ogupdate['parent12commission'] = $commissiondata[$agleveldata12['id']]['commission12'] * $commission_totalprice * 0.01;
    							if($agleveldata13) $ogupdate['parent13commission'] = $commissiondata[$agleveldata13['id']]['commission13'] * $commission_totalprice * 0.01;
    							if($agleveldata14) $ogupdate['parent14commission'] = $commissiondata[$agleveldata14['id']]['commission14'] * $commission_totalprice * 0.01;
    							if($agleveldata15) $ogupdate['parent15commission'] = $commissiondata[$agleveldata15['id']]['commission15'] * $commission_totalprice * 0.01;
    							if($agleveldata16) $ogupdate['parent16commission'] = $commissiondata[$agleveldata16['id']]['commission16'] * $commission_totalprice * 0.01;
    							if($agleveldata17) $ogupdate['parent17commission'] = $commissiondata[$agleveldata17['id']]['commission17'] * $commission_totalprice * 0.01;
    							if($agleveldata18) $ogupdate['parent18commission'] = $commissiondata[$agleveldata18['id']]['commission18'] * $commission_totalprice * 0.01;
    							if($agleveldata19) $ogupdate['parent19commission'] = $commissiondata[$agleveldata19['id']]['commission19'] * $commission_totalprice * 0.01;
    							if($agleveldata20) $ogupdate['parent20commission'] = $commissiondata[$agleveldata20['id']]['commission20'] * $commission_totalprice * 0.01;
    						}
    					}elseif($product['commissionset']==2){//按固定金额
    						$commissiondata = json_decode($product['commissiondata2'],true);
    						if($commissiondata){
    							if(getcustom('fengdanjiangli') && $product['fengdanjiangli']){
    								
    							}else{
    								if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
    								if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
    								if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
    								if($agleveldata4) $ogupdate['parent4commission'] = $commissiondata[$agleveldata4['id']]['commission4'] * $num;
    								if($agleveldata5) $ogupdate['parent5commission'] = $commissiondata[$agleveldata5['id']]['commission5'] * $num;
    								if($agleveldata6) $ogupdate['parent6commission'] = $commissiondata[$agleveldata6['id']]['commission6'] * $num;
    								if($agleveldata7) $ogupdate['parent7commission'] = $commissiondata[$agleveldata7['id']]['commission7'] * $num;
    								if($agleveldata8) $ogupdate['parent8commission'] = $commissiondata[$agleveldata8['id']]['commission8'] * $num;
    								if($agleveldata9) $ogupdate['parent9commission'] = $commissiondata[$agleveldata9['id']]['commission9'] * $num;
    								if($agleveldata10) $ogupdate['parent10commission'] = $commissiondata[$agleveldata10['id']]['commission10'] * $num;
    								if($agleveldata11) $ogupdate['parent11commission'] = $commissiondata[$agleveldata11['id']]['commission11'] * $num;
        							if($agleveldata12) $ogupdate['parent12commission'] = $commissiondata[$agleveldata12['id']]['commission12'] * $num;
        							if($agleveldata13) $ogupdate['parent13commission'] = $commissiondata[$agleveldata13['id']]['commission13'] * $num;
        							if($agleveldata14) $ogupdate['parent14commission'] = $commissiondata[$agleveldata14['id']]['commission14'] * $num;
        							if($agleveldata15) $ogupdate['parent15commission'] = $commissiondata[$agleveldata15['id']]['commission15'] * $num;
        							if($agleveldata16) $ogupdate['parent16commission'] = $commissiondata[$agleveldata16['id']]['commission16'] * $num;
        							if($agleveldata17) $ogupdate['parent17commission'] = $commissiondata[$agleveldata17['id']]['commission17'] * $num;
        							if($agleveldata18) $ogupdate['parent18commission'] = $commissiondata[$agleveldata18['id']]['commission18'] * $num;
        							if($agleveldata19) $ogupdate['parent19commission'] = $commissiondata[$agleveldata19['id']]['commission19'] * $num;
        							if($agleveldata20) $ogupdate['parent20commission'] = $commissiondata[$agleveldata20['id']]['commission20'] * $num;
    							}
    						}
    					}elseif($product['commissionset']==3){//提成是积分
    						$commissiondata = json_decode($product['commissiondata3'],true);
    						if($commissiondata){
    							if($agleveldata1) $ogupdate['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
    							if($agleveldata2) $ogupdate['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
    							if($agleveldata3) $ogupdate['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
    							if($agleveldata4) $ogupdate['parent4score'] = $commissiondata[$agleveldata4['id']]['commission4'] * $num;
    							if($agleveldata5) $ogupdate['parent5score'] = $commissiondata[$agleveldata5['id']]['commission5'] * $num;
    							if($agleveldata6) $ogupdate['parent6score'] = $commissiondata[$agleveldata6['id']]['commission6'] * $num;
    							if($agleveldata7) $ogupdate['parent7score'] = $commissiondata[$agleveldata7['id']]['commission7'] * $num;
    							if($agleveldata8) $ogupdate['parent8score'] = $commissiondata[$agleveldata8['id']]['commission8'] * $num;
    							if($agleveldata9) $ogupdate['parent9score'] = $commissiondata[$agleveldata9['id']]['commission9'] * $num;
    							if($agleveldata10) $ogupdate['parent10score'] = $commissiondata[$agleveldata10['id']]['commission10'] * $num;
    							if($agleveldata11) $ogupdate['parent11score'] = $commissiondata[$agleveldata11['id']]['commission11'] * $num;
    							if($agleveldata12) $ogupdate['parent12score'] = $commissiondata[$agleveldata12['id']]['commission12'] * $num;
    							if($agleveldata13) $ogupdate['parent13score'] = $commissiondata[$agleveldata13['id']]['commission13'] * $num;
    							if($agleveldata14) $ogupdate['parent14score'] = $commissiondata[$agleveldata14['id']]['commission14'] * $num;
    							if($agleveldata15) $ogupdate['parent15score'] = $commissiondata[$agleveldata15['id']]['commission15'] * $num;
    							if($agleveldata16) $ogupdate['parent16score'] = $commissiondata[$agleveldata16['id']]['commission16'] * $num;
    							if($agleveldata17) $ogupdate['parent17score'] = $commissiondata[$agleveldata17['id']]['commission17'] * $num;
    							if($agleveldata18) $ogupdate['parent18score'] = $commissiondata[$agleveldata18['id']]['commission18'] * $num;
    							if($agleveldata19) $ogupdate['parent19score'] = $commissiondata[$agleveldata19['id']]['commission19'] * $num;
    							if($agleveldata20) $ogupdate['parent20score'] = $commissiondata[$agleveldata20['id']]['commission20'] * $num;
    						}
    					}else{ //按会员等级设置的分销比例
    						if($agleveldata1){
    							if(getcustom('plug_ttdz') && $isfg == 1){
    								$agleveldata1['commission1'] = $agleveldata1['commission4'];
    							}
    							if($agleveldata1['commissiontype']==1){ //固定金额按单
    								if($istc1==0){
    									$ogupdate['parent1commission'] = $agleveldata1['commission1'];
    									$istc1 = 1;
    								}
    							}else{
    								$ogupdate['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
    							}
    							if($agleveldata1['is_zuigao_money'] == 1)
    							{
    							    $agleveldata1['zuigao_money'] =  $commission_totalprice*$agleveldata1['zuigao_money']*0.01;
    							}
    							if($ogupdate['parent1commission'] > $agleveldata1['zuigao_money'] && $agleveldata1['zuigao_money'] >0)
    							{
    							    $ogupdate['parent1commission'] = $agleveldata1['zuigao_money'];
    							}
    						}
    					
    						if($agleveldata2){
    							if(getcustom('plug_ttdz') && $isfg == 1){
    								$agleveldata2['commission2'] = $agleveldata2['commission5'];
    							}
    							if($agleveldata2['commissiontype']==1){
    								if($istc2==0){
    									$ogupdate['parent2commission'] = $agleveldata2['commission2'];
    									$istc2 = 1;
                                        //持续推荐奖励
                                        if($agleveldata2['commission_parent'] > 0 && $ogupdate['parent1']) {
                                            $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent'];
                                        }
    								}
    							}else{
    								$ogupdate['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                                    //持续推荐奖励
                                    if($agleveldata2['commission_parent'] > 0 && $ogupdate['parent1commission'] > 0 && $ogupdate['parent1']) {
                                        $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent'] * 0.01;
                                    }
    							}
    							if($agleveldata2['is_zuigao_money'] == 1)
    							{
    							    $agleveldata2['zuigao_money'] =  $commission_totalprice*$agleveldata2['zuigao_money']*0.01;
    							}
    							if($ogupdate['parent2commission'] > $agleveldata2['zuigao_money']  && $agleveldata2['zuigao_money'] >0)
    							{
    							    $ogupdate['parent2commission'] = $agleveldata2['zuigao_money'];
    							}
    
    						}
    						if($agleveldata3){
    							if(getcustom('plug_ttdz') && $isfg == 1){
    								$agleveldata3['commission3'] = $agleveldata3['commission6'];
    							}
    							
    							if($agleveldata3['commissiontype']==1){
    								if($istc3==0){
    									$ogupdate['parent3commission'] = $agleveldata3['commission3'];
    									$istc3 = 1;
                                        //持续推荐奖励
                                        if($agleveldata3['commission_parent'] > 0 && $ogupdate['parent2']) {
                                            $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent'];
                                        }
    								}
    							}else{
    								$ogupdate['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                                    //持续推荐奖励
                                    if($agleveldata3['commission_parent'] > 0 && $ogupdate['parent2commission'] > 0 && $ogupdate['parent2']) {
                                        $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent'] * 0.01;
                                    }
    							}
    							if($agleveldata3['is_zuigao_money'] == 1)
    							{
    							    $agleveldata3['zuigao_money'] =  $commission_totalprice*$agleveldata3['zuigao_money']*0.01;
    							}
    							if($ogupdate['parent3commission'] > $agleveldata3['zuigao_money'] && $agleveldata3['zuigao_money'] >0)
    							{
    							    $ogupdate['parent3commission'] = $agleveldata3['zuigao_money'];
    							}
    						}
    						//持续推荐奖励
                            if($agleveldata4['commission_parent'] > 0 && $ogupdate['parent3']) {
                                if($agleveldata3['commissiontype']==1){
                                    $ogupdate['parent4commission'] = $agleveldata4['commission_parent'];
                                } else {
                                    $ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent'] * 0.01;
                                }
                                if($agleveldata4['is_zuigao_money'] == 1)
    							{
    							    $agleveldata4['zuigao_money'] =  $commission_totalprice*$agleveldata4['zuigao_money']*0.01;
    							}
                                if($ogupdate['parent4commission'] > $agleveldata4['zuigao_money'] &&$agleveldata4['zuigao_money']>0)
    							{
    							    $ogupdate['parent4commission'] = $agleveldata4['zuigao_money'];
    							}
                            }
                            //提成积分
                            if($agleveldata1){
    							if($agleveldata1['commissiontype']==1){ 
    								if($istc11==0){
    									$ogupdate['parent1commissionscore'] = $agleveldata1['commission1_score'];
    									$istc11 = 1;
    								}
    							}else{
    								$ogupdate['parent1commissionscore'] = $agleveldata1['commission1_score'] * $commission_totalprice * 0.01;
    							}
    							 if($agleveldata1['is_zuigao_score'] == 1)
    							{
    							    $agleveldata1['zuigao_score'] =  $commission_totalprice*$agleveldata1['zuigao_score']*0.01;
    							}
    							if($ogupdate['parent1commissionscore'] > $agleveldata1['zuigao_score'] && $agleveldata1['zuigao_score'] >0)
    							{
    							    $ogupdate['parent1commissionscore'] = $agleveldata1['zuigao_score'];
    							}
    						}
    						
    						if($agleveldata2){
    							if($agleveldata2['commissiontype']==1){
    								if($istc22==0){
    									$ogupdate['parent2commissionscore'] = $agleveldata2['commission2_score'];
    									$istc22 = 1;
                                        //持续推荐奖励
                                        if($agleveldata2['commission_parent_score'] > 0 && $ogupdate['parent1']) {
                                            $ogupdate['parent2commissionscore'] = $ogupdate['parent2commissionscore'] + $agleveldata2['commission_parent_score'];
                                        }
    								}
    							}else{
    								$ogupdate['parent2commissionscore'] = $agleveldata2['commission2_score'] * $commission_totalprice * 0.01;
                                    //持续推荐奖励
                                    if($agleveldata2['commission_parent_score'] > 0 && $ogupdate['parent1commissionscore'] > 0 && $ogupdate['parent1']) {
                                        $ogupdate['parent2commissionscore'] = $ogupdate['parent2commissionscore'] + $ogupdate['parent1commissionscore'] * $agleveldata2['commission_parent_score'] * 0.01;
                                    }
    							}
    							 if($agleveldata2['is_zuigao_score'] == 1)
    							{
    							    $agleveldata2['zuigao_score'] =  $commission_totalprice*$agleveldata2['zuigao_score']*0.01;
    							}
    							if($ogupdate['parent2commissionscore'] > $agleveldata2['zuigao_score'] && $agleveldata2['zuigao_score'] >0)
    							{
    							    $ogupdate['parent2commissionscore'] = $agleveldata2['zuigao_score'];
    							}
    
    						}
    						
    						if($agleveldata3){
    							if($agleveldata3['commissiontype']==1){
    								if($istc33==0){
    									$ogupdate['parent3commissionscore'] = $agleveldata3['commission3_score'];
    									$istc33 = 1;
                                        //持续推荐奖励
                                        if($agleveldata3['commission_parent_score'] > 0 && $ogupdate['parent2']) {
                                            $ogupdate['parent3commissionscore'] = $ogupdate['parent3commissionscore'] + $agleveldata3['commission_parent_score'];
                                        }
    								}
    							}else{
    								$ogupdate['parent3commissionscore'] = $agleveldata3['commission3_score'] * $commission_totalprice * 0.01;
                                    //持续推荐奖励
                                    if($agleveldata3['commission_parent_score'] > 0 && $ogupdate['parent2commissionscore'] > 0 && $ogupdate['parent2']) {
                                        $ogupdate['parent3commissionscore'] = $ogupdate['parent3commissionscore'] + $ogupdate['parent2commissionscore'] * $agleveldata3['commission_parent_score'] * 0.01;
                                    }
    							}
    							if($agleveldata3['is_zuigao_score'] == 1)
    							{
    							    $agleveldata3['zuigao_score'] =  $commission_totalprice*$agleveldata3['zuigao_score']*0.01;
    							}
    							if($ogupdate['parent3commissionscore'] > $agleveldata3['zuigao_score'] && $agleveldata3['zuigao_score'] >0)
    							{
    							    $ogupdate['parent3commissionscore'] = $agleveldata3['zuigao_score'];
    							}
    						}
                            	//持续推荐奖励
                            if($agleveldata4['commission_parent_score'] > 0 && $ogupdate['parent3']) {
                                if($agleveldata3['commissiontype']==1){
                                    $ogupdate['parent4commissionscore'] = $agleveldata4['commission_parent_score'];
                                } else {
                                    $ogupdate['parent4commissionscore'] = $ogupdate['parent3commissionscore'] * $agleveldata4['commission_parent_score'] * 0.01;
                                }
                                	if($agleveldata4['is_zuigao_score'] == 1)
    							{
    							    $agleveldata4['zuigao_score'] =  $commission_totalprice*$agleveldata4['zuigao_score']*0.01;
    							}
                                if($ogupdate['parent4commissionscore'] > $agleveldata4['zuigao_score'] && $agleveldata4['zuigao_score'] >0)
    							{
    							    $ogupdate['parent4commissionscore'] = $agleveldata4['zuigao_score'];
    							}
                            }
    					}
    					//分红佣金
    					if($agleveldata2){
    						if($ogupdate['parent1commission']>0&&$agleveldata2['fh_commission2']>0){
    						    $ogupdate['fhparent2commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission2']* 0.01;
    						}
    					}
    					if($agleveldata3){
    						if($ogupdate['parent1commission']>0&&$agleveldata3['fh_commission3']>0){
    						    $ogupdate['fhparent3commission'] = $ogupdate['parent1commission']*$agleveldata3['fh_commission3']* 0.01;
    						}	
    					}
    					if($agleveldata4){
    						if($ogupdate['parent1commission']>0&&$agleveldata4['fh_commission4']>0){
    						    $ogupdate['fhparent4commission'] = $ogupdate['parent1commission']*$agleveldata4['fh_commission4']* 0.01;
    						}	
    					}
    					//消费值
    					$ogupdate['xfzparent2commission'] = 0;
                        $ogupdate['xfzparent3commission'] = 0;
                        $ogupdate['xfzparent4commission'] = 0;
                        if($product['commissionset'] == 0){
        					if($agleveldata1){
        						if($agleveldata1['xfz_commission2']>0){
        						     $ogupdate['xfzparent2id']  = $parent1['id'];
        						    if($agleveldata1['commissiontype'] == 1)
        						    {
        						         $ogupdate['xfzparent2commission'] = $agleveldata1['xfz_commission2']*$num;
        						    }else{
        						        $ogupdate['xfzparent2commission'] = $commission_totalprice*$agleveldata1['xfz_commission2']* 0.01;
        						    }
        						}
        					}
        					if($agleveldata2){
        						if($agleveldata2['xfz_commission3']>0){
        						    $ogupdate['xfzparent3id']  = $parent2['id'];
        						    if($agleveldata2['commissiontype'] == 1)
        						    {
        						        $ogupdate['xfzparent3commission'] = $agleveldata2['xfz_commission3']*$num;
        						    }else{
        						        $ogupdate['xfzparent3commission'] = $commission_totalprice*$agleveldata2['xfz_commission3']* 0.01;
        						    }
        						}	
        					}
        					if($agleveldata3){
        						if($agleveldata3['xfz_commission4']>0){
        						    $ogupdate['xfzparent4id']  = $parent3['id'];
        						    if($agleveldata3['commissiontype'] == 1)
        						    {
        						        $ogupdate['xfzparent4commission'] = $agleveldata3['xfz_commission4']*$num;
        						    }else{
        						        $ogupdate['xfzparent4commission'] = $commission_totalprice*$agleveldata3['xfz_commission4']* 0.01;
        						    }
        						}	
        					}
                        }
    					//平级奖
    					if($agleveldata4 && $ogupdate['parent3'] && $ogupdate['parent3commission'] > 0 && $agleveldata3['id'] == $agleveldata4['id']){
    						$agleveldata4['commissionpingjitype'] = 0;
    						if($product['commissionpingjiset'] != 0){
    							if($product['commissionpingjiset'] == 1){
    								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
    								$agleveldata4['commission_parent_pj'] = $commissionpingjidata1[$agleveldata4['id']]['commission'];
    							}elseif($product['commissionpingjiset'] == 2){
    								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
    								$agleveldata4['commission_parent_pj'] = $commissionpingjidata2[$agleveldata4['id']]['commission'];
    								$agleveldata4['commissionpingjitype'] = 1;
    							}else{
    								$agleveldata4['commission_parent_pj'] = 0;
    							}
    						}
    						if($agleveldata4['commission_parent_pj'] > 0) {
    							if($agleveldata4['commissionpingjitype']==0){
    								$ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent_pj'] * 0.01;
    							} else {
    								$ogupdate['parent4commission'] = $agleveldata4['commission_parent_pj'];
    							}
    							$ogupdate['parent4'] = $parent4['id'];
    						}
    					}
    					if($agleveldata3 && $ogupdate['parent2'] && $ogupdate['parent2commission'] > 0 && $agleveldata2['id'] == $agleveldata3['id']){
    						$agleveldata3['commissionpingjitype'] = 0;
    						if($product['commissionpingjiset'] != 0){
    							if($product['commissionpingjiset'] == 1){
    								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
    								$agleveldata3['commission_parent_pj'] = $commissionpingjidata1[$agleveldata3['id']]['commission'];
    							}elseif($product['commissionpingjiset'] == 2){
    								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
    								$agleveldata3['commission_parent_pj'] = $commissionpingjidata2[$agleveldata3['id']]['commission'];
    								$agleveldata3['commissionpingjitype'] = 1;
    							}else{
    								$agleveldata3['commission_parent_pj'] = 0;
    							}
    						}
    						if($agleveldata3['commission_parent_pj'] > 0){
    							if(!$ogupdate['parent3']){
    								$ogupdate['parent3commission'] = 0;
    								$ogupdate['parent3'] = $parent3['id'];
    							}
    							if($agleveldata3['commissionpingjitype'] == 0){
    								$ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent_pj'] * 0.01;
    							}else{
    								$ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent_pj'];
    							}
    						}
    					}
    					if($agleveldata2 && $ogupdate['parent1'] && $ogupdate['parent1commission'] > 0 && $agleveldata1['id'] == $agleveldata2['id']){
    						$agleveldata2['commissionpingjitype'] = 0;
    						if($product['commissionpingjiset'] != 0){
    							if($product['commissionpingjiset'] == 1){
    								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
    								$agleveldata2['commission_parent_pj'] = $commissionpingjidata1[$agleveldata2['id']]['commission'];
    							}elseif($product['commissionpingjiset'] == 2){
    								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
    								$agleveldata2['commission_parent_pj'] = $commissionpingjidata2[$agleveldata2['id']]['commission'];
    								$agleveldata2['commissionpingjitype'] = 1;
    							}else{
    								$agleveldata2['commission_parent_pj'] = 0;
    							}
    						}
    						if($agleveldata2['commission_parent_pj'] > 0){
    							if(!$ogupdate['parent2']){
    								$ogupdate['parent2commission'] = 0;
    								$ogupdate['parent2'] = $parent2['id'];
    							}
    							if($agleveldata2['commissionpingjitype'] == 0){
    								$ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent_pj'] * 0.01;
    							}else{
    								$ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent_pj'];
    							}
    						}
    					}
    				}
    				if($ogupdate){
    					Db::name('shop_order_goods')->where('id',$ogid)->update($ogupdate);
    				}
    				
    				$totalcommission = 0;
    				if($product['commissionset']!=4){
    				    if($parent2 && $ogupdate['fhparent2commission']>0){
    							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent2['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent2commission'],'score'=>0,'remark'=>'下二级购买商品分红','createtime'=>time(),'jl_type'=>2]);
    				// 			$totalcommission += $ogupdate['parent2commission'];
    						}
    					if($parent3 && $ogupdate['fhparent3commission']>0){
    							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent3['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent3commission'],'score'=>0,'remark'=>'下三级购买商品分红','createtime'=>time(),'jl_type'=>2]);
    				// 			$totalcommission += $ogupdate['parent2commission'];
    						}
    					if($parent4 && $ogupdate['fhparent4commission']>0){
    							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent4['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent4commission'],'score'=>0,'remark'=>'下四级购买商品分红','createtime'=>time(),'jl_type'=>2]);
    				// 			$totalcommission += $ogupdate['parent2commission'];
    						}
    				// 			$totalcommission += $ogupdate['parent2commission'];
    				// 		}	
    				//      $memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
    				// 	 $newbus_total = $this->member['bus_total'];
    				// 	 $comzong = $ogupdate['parent1commission']+$ogupdate['parent2commission']+$ogupdate['parent3commission']+$ogupdate['parent4commission']+$ogupdate['parent5commission']+$ogupdate['parent6commission']+$ogupdate['parent7commission']+$ogupdate['parent8commission']+$ogupdate['parent9commission']+$ogupdate['parent10commission']+$ogupdate['parent11commission']+$ogupdate['parent12commission']+$ogupdate['parent13commission']+$ogupdate['parent14commission']+$ogupdate['parent15commission']+$ogupdate['parent16commission']+$ogupdate['parent17commission']+$ogupdate['parent18commission']+$ogupdate['parent19commission']+$ogupdate['parent20commission'];
     				// 	  if($comzong <= $newbus_total ){
             					if(getcustom('plug_ttdz') && $isfg == 1){
             						if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
             							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级复购奖励','createtime'=>time()]);
             							$totalcommission += $ogupdate['parent1commission'];
             						}
             						if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
             							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级复购奖励','createtime'=>time()]);
             							$totalcommission += $ogupdate['parent2commission'];
             						}
             						if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
             							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级复购奖励','createtime'=>time()]);
             							$totalcommission += $ogupdate['parent3commission'];
             						}
             					}else{
             					   // 下二十级购买商品奖励
                                    $Parentcommission=0;   //收益抽佣奖励：享受下级
             						if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
             						    //查下1下面的创业值
             						  //  如果够就返,不够就返0
             						    if($parent1['bus_total'] >= $ogupdate['parent1commission']){
                 							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级购买商品奖励11','createtime'=>time()]);
                 							$totalcommission += $ogupdate['parent1commission'];
                                            $Parentcommission=$ogupdate['parent1commission'];
             						    }else{
                                            Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent1['bus_total'],'score'=>$ogupdate['parent1score'],'remark'=>'下级购买商品奖励','createtime'=>time()]);
                                            $totalcommission += $parent1['bus_total'];
                                            $Parentcommission=$parent1['bus_total'];
             						    }
                                        //添加记录
                                        \app\common\Member::addParentcommissionLogs(aid,$ogupdate['parent1'],$orderid,$ogid,$Parentcommission,'下级分佣奖励');
    
            						}
            						if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
            						    if($parent2['bus_total'] >= $ogupdate['parent2commission']){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent2commission'];
            						    }else{
            						        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent2['bus_total'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $parent2['bus_total'];
            						    }
            						}
            						if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
            						    if($parent3['bus_total'] >= $ogupdate['parent3commission']){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent3commission'];
            						    }else{
            						        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent3['bus_total'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级购买商品奖励','createtime'=>time()]);
                							$totalcommission +=$parent3['bus_total'];
            						    }
            						}
            						if($product['commissionset']  ==1 || $product['commissionset']  ==2 || $product['commissionset']  ==3){
            						    if($ogupdate['parent4'] && ($ogupdate['parent4commission'] || $ogupdate['parent4score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commission'],'score'=>$ogupdate['parent4score'],'remark'=>'下四级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent4commission'];
                						}
                						if($ogupdate['parent5'] && ($ogupdate['parent5commission'] || $ogupdate['parent5score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent5'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent5commission'],'score'=>$ogupdate['parent5score'],'remark'=>'下五级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent5commission'];
                						}
                						if($ogupdate['parent6'] && ($ogupdate['parent6commission'] || $ogupdate['parent6score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent6'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent6commission'],'score'=>$ogupdate['parent6score'],'remark'=>'下六级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent6commission'];
                						}
                						if($ogupdate['parent7'] && ($ogupdate['parent7commission'] || $ogupdate['parent7score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent7'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent7commission'],'score'=>$ogupdate['parent7score'],'remark'=>'下七级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent7commission'];
                						}
                						if($ogupdate['parent8'] && ($ogupdate['parent8commission'] || $ogupdate['parent8score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent8'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent8commission'],'score'=>$ogupdate['parent8score'],'remark'=>'下八级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent8commission'];
                						}
                						if($ogupdate['parent9'] && ($ogupdate['parent9commission'] || $ogupdate['parent9score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent9'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent9commission'],'score'=>$ogupdate['parent9score'],'remark'=>'下九级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent9commission'];
                						}
                						if($ogupdate['parent10'] && ($ogupdate['parent10commission'] || $ogupdate['parent10score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent10'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent10commission'],'score'=>$ogupdate['parent10score'],'remark'=>'下十级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent10commission'];
                						}
                						if($ogupdate['parent11'] && ($ogupdate['parent11commission'] || $ogupdate['parent11score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent11'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent11commission'],'score'=>$ogupdate['parent11score'],'remark'=>'下十一级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent11commission'];
                						}
                						if($ogupdate['parent12'] && ($ogupdate['parent12commission'] || $ogupdate['parent12score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent12'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent12commission'],'score'=>$ogupdate['parent12score'],'remark'=>'下十二级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent12commission'];
                						}
                						if($ogupdate['parent13'] && ($ogupdate['parent13commission'] || $ogupdate['parent13score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent13'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent13commission'],'score'=>$ogupdate['parent13score'],'remark'=>'下十三级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent13commission'];
                						}
                						if($ogupdate['parent14'] && ($ogupdate['parent14commission'] || $ogupdate['parent14score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent14'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent14commission'],'score'=>$ogupdate['parent14score'],'remark'=>'下十四级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent14commission'];
                						}
                						if($ogupdate['parent15'] && ($ogupdate['parent15commission'] || $ogupdate['parent15score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent15'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent15commission'],'score'=>$ogupdate['parent15score'],'remark'=>'下十五级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent15commission'];
                						}
                						if($ogupdate['parent16'] && ($ogupdate['parent16commission'] || $ogupdate['parent16score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent16'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent16commission'],'score'=>$ogupdate['parent16score'],'remark'=>'下十六级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent16commission'];
                						}
                						if($ogupdate['parent17'] && ($ogupdate['parent17commission'] || $ogupdate['parent17score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent17'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent17commission'],'score'=>$ogupdate['parent17score'],'remark'=>'下十七级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent17commission'];
                						}
                						if($ogupdate['parent18'] && ($ogupdate['parent18commission'] || $ogupdate['parent18score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent18'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent18commission'],'score'=>$ogupdate['parent18score'],'remark'=>'下十八级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent18commission'];
                						}
                						if($ogupdate['parent19'] && ($ogupdate['parent19commission'] || $ogupdate['parent19score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent19'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent19commission'],'score'=>$ogupdate['parent19score'],'remark'=>'下十九级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent19commission'];
                						}
                						if($ogupdate['parent20'] && ($ogupdate['parent20commission'] || $ogupdate['parent20score'])){
                							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent20'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent20commission'],'score'=>$ogupdate['parent20score'],'remark'=>'下二十级购买商品奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent20commission'];
                						}
            						}else{
                                        if($ogupdate['parent4'] && ($ogupdate['parent4commission'])){
                                            Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commission'],'score'=>0,'remark'=>'持续推荐奖励','createtime'=>time()]);
                							$totalcommission += $ogupdate['parent4commission'];
                                        }
            						}
            					}
    				// 	if($ogupdate['parent1'] && $ogupdate['parent1commissionshouyi'])
    				// 	{
    				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commissionshouyi'],'score'=>0,'remark'=>'下一级购买商品收益抽佣','createtime'=>time()]);
    				// 	}
    				// 	if($ogupdate['parent2'] && $ogupdate['parent2commissionshouyi'])
    				// 	{
    				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commissionshouyi'],'score'=>0,'remark'=>'下二级购买商品收益抽佣','createtime'=>time()]);
    				// 	}
    				// 	if($ogupdate['parent3'] && $ogupdate['parent3commissionshouyi'])
    				// 	{
    				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commissionshouyi'],'score'=>0,'remark'=>'下三级购买商品收益抽佣','createtime'=>time()]);
    				// 	}
    				// 	if($ogupdate['parent4'] && $ogupdate['parent4commissionshouyi'])
    				// 	{
    				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commissionshouyi'],'score'=>0,'remark'=>'下四级购买商品收益抽佣','createtime'=>time()]);
    				// 	}
    					if($post['checkmemid'] && $commission_totalprice > 0){
    						$checkmember = Db::name('member')->where('aid',aid)->where('id',$post['checkmemid'])->find();
    						if($checkmember){
    							$buyselect_commission = Db::name('member_level')->where('id',$checkmember['levelid'])->value('buyselect_commission');
    							$checkmemcommission = $buyselect_commission * $commission_totalprice * 0.01;
    							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$checkmember['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$checkmemcommission,'score'=>0,'remark'=>'购买商品时指定奖励','createtime'=>time()]);
    						}
    					}
    					
    					
    				}
    
    				
    				if($product['commissionset4']==1 && $product['lvprice']==1){ //极差分销
    					if(getcustom('jicha_removecommission')){ //算极差时先减去分销的钱
    						$commission_totalpriceCache = $commission_totalpriceCache - $totalcommission;
    					}
    					if($this->member['path']){
    						$parentList = Db::name('member')->where('id','in',$this->member['path'])->order(Db::raw('field(id,'.$this->member['path'].')'))->select()->toArray();
    						if($parentList){
    							$parentList = array_reverse($parentList);
    							$lvprice_data = json_decode($guige['lvprice_data'],true);
    							$nowprice = $commission_totalpriceCache;
    							$giveidx = 0;
    							foreach($parentList as $k=>$parent){
    								if($parent['levelid'] && $lvprice_data[$parent['levelid']]){
    									$thisprice = floatval($lvprice_data[$parent['levelid']]) * $num;
    									if($nowprice > $thisprice){
    										$commission = $nowprice - $thisprice;
    										$nowprice = $thisprice;
    										$giveidx++;
    										//if($giveidx <=3){
    										//	$ogupdate['parent'.$giveidx] = $parent['id'];
    										//	$ogupdate['parent'.$giveidx.'commission'] = $commission;
    										//}
    										Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$commission,'score'=>0,'remark'=>'下级购买商品差价','createtime'=>time()]);
    									}
    								}
    							}
    						}
    					}
    				}
    
    				if(getcustom('commission_givedown')){
    					$commission_recordlist = Db::name('member_commission_record')->field("mid,sum(commission) totalcommission")->where('aid',aid)->where('orderid',$orderid)->where('ogid',$ogid)->where('type','shop')->where('commission','>',0)->group('mid')->select()->toArray();
    					foreach($commission_recordlist as $record){
    						$thismember = Db::name('member')->where('id',$record['mid'])->find();
    						$memberlevel = Db::name('member_level')->where('id',$thismember['levelid'])->find();
    						if($memberlevel && ($memberlevel['givedown_percent'] > 0 || $memberlevel['givedown_commission'] > 0)){
    							$downmemberlist = Db::name('member')->where('aid',aid)->where('pid',$record['mid'])->select()->toArray();
    							if(!$downmemberlist) continue;
    							$downcommission = $memberlevel['givedown_commission'] / count($downmemberlist) + $record['totalcommission'] * $memberlevel['givedown_percent'] * 0.01 / count($downmemberlist);
    							foreach($downmemberlist as $downmember){
    								Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$downmember['id'],'frommid'=>$thismember['id'],'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$downcommission,'score'=>0,'remark'=>$memberlevel['givedown_txt'],'createtime'=>time()]);
    							}
    						}
    					}
    				}
    
    				if(getcustom('everyday_hongbao')) {
                        $hongbaoEdu = 0;
                        if($product['everyday_hongbao_bl'] === null) {
                            $hongbaoEdu = $og_totalprice * $hd['shop_product_hongbao_bl'] / 100;
                        } elseif($product['everyday_hongbao_bl'] > 0 ) {
                            $hongbaoEdu = $og_totalprice * $product['everyday_hongbao_bl'] / 100;
                        }
                        $hongbaoEdu = round($hongbaoEdu,2);
                        if($hongbaoEdu > 0)
                            Db::name('shop_order_goods')->where('id',$ogid)->update(['hongbaoEdu' => $hongbaoEdu]);
                    }
                     //减库存
                    \app\common\Member::yunkucunlog(aid,mid,$ogdata['proid'],$ogdata['ggid'],'-'.$ogdata['num'],$ogdata,'提货');
    			}
    		}
    		
    		return $this->json(['status'=>1,'msg'=>'提货成功']);
        }else{
             return $this->json(['status'=>0,'msg'=>'云库存未开启']); 
        }
    }
 
    public function myxingjilog()
    {
        $pagenum = input('post.pagenum');
        $pernum = 20;
        $datalist = Db::name('member_xingjilog')->alias('xj')
        ->field("xj.*,m.headimg,from_unixtime(xj.createtime)createtime,m.nickname")
        ->leftJoin('member m','m.id=xj.formid')
        ->where('xj.mid',mid)->where('xj.aid',aid)->page($pagenum,$pernum)->order('xj.id desc')->select()->toArray();
        if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist]);
    }

	//佣金转余额
	public function commission2money(){
        try{
            Db::startTrans();
            $post = input('post.');
            $set = Db::name('admin_set')->where('aid',aid)->find();
            if($set['commission2money'] !=1){
                return $this->json(['status'=>0,'msg'=>'该功能未启用']);
            }
            $money = floatval($post['money']);
            $member = Db::name('member')->where('aid',aid)->where('id',mid)->lock(true)->find();
            if($money <= 0 || $money > $member['commission']){
                return $this->json(['status'=>0,'msg'=>'转入金额不正确']);
            }

            // 检查是否需要扣除贡献值
            $deduct_gongxianzhi = isset($set['commission2money_deduct_gongxianzhi']) ? intval($set['commission2money_deduct_gongxianzhi']) : 0;
            $gongxianzhi_rate = isset($set['commission2money_gongxianzhi_rate']) ? floatval($set['commission2money_gongxianzhi_rate']) : 100;

            if($deduct_gongxianzhi == 1) {
                // 计算需要扣除的贡献值
                $deduct_gongxianzhi_amount = $money * ($gongxianzhi_rate / 100);

                // 检查用户贡献值是否足够
                if($member['contribution_num'] < $deduct_gongxianzhi_amount) {
                    return $this->json(['status'=>0,'msg'=>t('贡献值').'不足，无法转换。需要'.t('贡献值').'：'.$deduct_gongxianzhi_amount.'，当前'.t('贡献值').'：'.$member['contribution_num']]);
                }
            }

            // 获取佣金转余额的分配比例
            $money_percent = isset($set['commission2money_money']) ? floatval($set['commission2money_money']) : 100;
            $score_percent = isset($set['commission2money_score']) ? floatval($set['commission2money_score']) : 0;
            $fee_percent = isset($set['commission2money_fee']) ? floatval($set['commission2money_fee']) : 0;
            
            // 检查比例总和是否为100%，如果不是则使用默认值
            $total_percent = $money_percent + $score_percent + $fee_percent;
            if(abs($total_percent - 100) > 0.01) {
                $money_percent = 100;
                $score_percent = 0;
                $fee_percent = 0;
            }
            
            // 计算各部分金额
            $to_money = $money * ($money_percent / 100);
            $to_score = $money * ($score_percent / 100);
            $fee = $money * ($fee_percent / 100);
            
            // 记录日志
            $log_desc = t('佣金').'转'.t('余额').'：';
            if($money_percent > 0) {
                $log_desc .= number_format($money_percent, 2).'%到'.t('余额').'，';
            }
            if($score_percent > 0) {
                $log_desc .= number_format($score_percent, 2).'%到'.t('积分').'，';
            }
            if($fee_percent > 0) {
                $log_desc .= number_format($fee_percent, 2).'%手续费';
            }
            $log_desc = rtrim($log_desc, '，');

            // 扣除佣金
            \app\common\Member::addcommission(aid, mid, 0, -$money, $log_desc);

            // 扣除贡献值（如果开启）
            if($deduct_gongxianzhi == 1 && isset($deduct_gongxianzhi_amount) && $deduct_gongxianzhi_amount > 0) {
                \app\common\Member::addgongxianzhi(aid, mid, -$deduct_gongxianzhi_amount, t('佣金').'转'.t('余额').'扣除'.t('贡献值').'（比例：'.$gongxianzhi_rate.'%）');
            }

            // 添加余额
            if($to_money > 0) {
                \app\common\Member::addmoney(aid, mid, $to_money, t('佣金').'转'.t('余额'));
            }

            // 添加积分
            if($to_score > 0) {
                \app\common\Member::addscore(aid, mid, $to_score, t('佣金').'转'.t('积分'));
            }

            // 记录操作日志
            \app\common\System::plog(t('佣金').'转'.t('余额').'：金额'.$money.'，'.($deduct_gongxianzhi == 1 ? '扣除'.t('贡献值').(isset($deduct_gongxianzhi_amount) ? $deduct_gongxianzhi_amount : 0) : '未扣除'.t('贡献值')));

            Db::commit();
            return $this->json(['status'=>1,'msg'=>'转入成功']);
        } catch(Exception $e) {
            Db::rollback();
            return $this->json(['status'=>0,'msg'=>'转入失败：'.$e->getMessage()]);
        }
	}
	//佣金明细
	public function commissionlog(){
		$st = input('param.st');
        $type = input('param.type');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		if(input('param.keyword')) $where[] = ['remark', 'like', '%'.input('param.keyword').'%'];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		if($st ==1){//提现记录
			$datalist = Db::name('member_commission_withdrawlog')->field("id,money,txmoney,`status`,from_unixtime(createtime)createtime,reason,wx_state")->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		}else{ //佣金明细
            if($type ==1) {
                $where[] = ['remark','=','投资分红'];
            }
            $moeny_weishu =2;
            if(getcustom('fenhong_money_weishu')){
                $moeny_weishu = Db::name('admin_set')->where('aid',aid)->value('fenhong_money_weishu');
            }
            $moeny_weishu =$moeny_weishu?$moeny_weishu:2;
			$datalist = Db::name('member_commissionlog')->field("id,commission money,`after`,from_unixtime(createtime)createtime,remark,service_fee,fhid")->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
            foreach ($datalist as $key=>$val){
                $datalist[$key]['commission'] = dd_money_format($val['commission'],$moeny_weishu);
                $datalist[$key]['money'] = dd_money_format($val['money'],$moeny_weishu);
                $datalist[$key]['after'] = dd_money_format($val['after'],$moeny_weishu);
                $datalist[$key]['service_fee'] = dd_money_format($val['service_fee'],$moeny_weishu);
                if(getcustom('commission_record_with_order') && $val['fhid']){
                    $log = db('member_fenhonglog')->where('aid',aid)->where('id',$val['fhid'])->find();
                    if($log['module']){
                        if(\app\common\Order::hasOrderGoodsTable($log['module'])){
                            $orderlist = db($log['module'].'_order_goods')->where('aid',aid)->where('id','in',$log['ogids'])->select()->toArray();
                        }else{
                            $orderlist = db($log['module'].'_order')->where('aid',aid)->where('id',$log['ogids'])->select()->toArray();
                        }
                        if($orderlist){
                            $totalprice = 0;
                            foreach ($orderlist as $order){
                                if($order['bid']>0){
                                    $business = Db::name('business')->where('aid',aid)->where('id',$order['bid'])->field('id,name,logo')->find();
                                } else {
                                    $business = Db::name('admin_set')->where('aid',aid)->field('name,logo')->find();
                                }
                                $totalprice += $order['totalprice'] ? $order['totalprice'] : $order['paymoney'];
                            }
                            $datalist[$key]['order'] = [
                                'totalprice'=>$totalprice,
                                'member'=> db('member')->field('id,nickname,headimg')->where('aid',aid)->where('id',$order['mid'])->find(),
                                'business'=>$business??[],
                                'bid'=>$order['bid']
                            ];
                        }
                    }
                }
            }
		}
		if(!$datalist) $datalist = [];

        if($pagenum == 1) {
            $field = 'aid,name,logo';
            if(getcustom('commission_service_fee')) {
                $field .= ',commission_service_fee_show';
            }

            $set = Db::name('admin_set')->field($field)->where('aid', aid)->find();
        }

		return $this->json(['status'=>1,'data'=>$datalist,'set'=>$set?$set:[]]);
	}
	
	
	//红包明细
	public function commissionloghuang()
	{
	    $st = input('param.st');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		if(input('param.keyword')) $where[] = ['remark', 'like', '%'.input('param.keyword').'%'];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		 //红包明细
		$datalist = Db::name('member_scoreloghuang')->field("id,score money,`after`,from_unixtime(createtime)createtime,remark")->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist]);
	}
	
	//伞下订单
	public function ordergoodsinfo()
	{
	    $st = input('param.st');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		
        $ids = Db::name('member')->where('path',"like",'%,'.mid.'%')->column('id');
        
        $datalist = Db::name('payorder')->alias('p')->field('p.orderid,p.ordernum,p.title,p.paytype,from_unixtime(p.paytime)createtime,g.name as goods_name,m.nickname,p.money')->leftJoin('shop_order_goods g','p.orderid=g.orderid')->leftJoin('member m','m.id=p.mid')->whereIn('p.mid',$ids)->where('g.proid',96)->page($pagenum,$pernum)->select()->toArray();
       
        $num = Db::name('payorder')->alias('p')->field('p.orderid,p.ordernum,p.title,p.paytype,from_unixtime(p.paytime)createtime,g.name as goods_name,m.nickname,p.money')->leftJoin('shop_order_goods g','p.orderid=g.orderid')->leftJoin('member m','m.id=p.mid')->whereIn('p.mid',$ids)->where('g.proid',96)->count('p.id');
    //   print_r($datalist);die;
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist,'num'=>$num]);
	}
	
	public function commissionloglv()
	{
	    $st = input('param.st');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		if(input('param.keyword')) $where[] = ['remark', 'like', '%'.input('param.keyword').'%'];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		if($st ==1){//打赏记录
			$datalist = Db::name('zhuanxiaofeizhi_log')->field("id,money,phone,from_unixtime(createtime)createtime")->where($where)->where('money','like','%-%')->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		}else{ //绿积分明细
			$datalist = Db::name('member_scoreloglv')->field("id,score money,`after`,from_unixtime(createtime)createtime,remark")->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		}
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist]);
		
	}
	public function commissionlogxiaofeizhi()
	{
	    $st = input('param.st');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		if(input('param.keyword')) $where[] = ['remark', 'like', '%'.input('param.keyword').'%'];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		if($st ==1){//打赏记录
			$datalist = Db::name('zhuanxiaofeizhi_log')->field("id,money,phone,from_unixtime(createtime)createtime")->where($where)->where('money','like','%+%')->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		}else{ //绿积分明细
			$datalist = Db::name('member_scorelogxiaofeizhi')->field("id,score money,`after`,from_unixtime(createtime)createtime,remark")->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		}
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist]);
		
	}
	
	//佣金记录
    public function commissionrecord(){
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;

		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		$st = input('param.st');
		if($st == 0){
			$where[] = ['status','in','0,1'];
		}elseif($st == 1){
			$where[] = ['status','=',1];
		}elseif($st == 2){
			$where[] = ['status','=',0];
		}

		$datalist = Db::name('member_commission_record')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		foreach($datalist as $k=>$v){
			if($v['type'] == 'levelup'){
				$datalist[$k]['orderstatus'] = 1;
			}else{
				$datalist[$k]['orderstatus'] = Db::name($v['type'].'_order')->where('id',$v['orderid'])->value('status');
			}
			if($v['frommid']){
				$frommember = Db::name('member')->where('id',$v['frommid'])->find();
				if($frommember){
					$datalist[$k]['fromheadimg'] = $frommember['headimg'];
					$datalist[$k]['fromnickname'] = $frommember['nickname'];
				}else{
					$datalist[$k]['fromheadimg'] = '';
					$datalist[$k]['fromnickname'] = '';
				}
			}
		}
		return $this->json(['status'=>1,'data'=>$datalist]);
    }
//佣金提现
	public function commissionWithdraw(){
	// 获取设置信息，包括新的 comwithdrawkouxianquan 字段
        $set = Db::name('admin_set')->where('aid', aid)->field('withbeishu,withdraw_autotransfer,comwithdraw,comwithdrawmin,comwithdrawfee,comwithdrawbl,comwithdrawdate,withdraw_weixin,withdraw_aliaccount,withdraw_bankcard,comwithdrawkouxianquan')->find();
		if(request()->isPost()){
			$post = input('post.');
			if($set['comwithdraw'] == 0){
				return $this->json(['status'=>0,'msg'=>t('佣金').'提现功能未开启']);
			}
			$memberlevel = Db::name('member_level')->where('id',$this->member['levelid'])->find();
			if($memberlevel['comwithdraw'] == 0){
				return $this->json(['status'=>0,'msg'=>t('佣金').'提现功能未开启']);
			}

			if($post['paytype']=='支付宝' && $this->member['aliaccount']==''){
				return $this->json(['status'=>0,'msg'=>'请先设置支付宝账号']);
			}
			if($post['paytype']=='银行卡' && ($this->member['bankname']==''||$this->member['bankcarduser']==''||$this->member['bankcardnum']=='')){
				return $this->json(['status'=>0,'msg'=>'请先设置完整银行卡信息']);
			}
            $cancommission = $this->member['commission'];
            if($memberlevel['fhcommissionstatus']>0){
                $cancommission += $this->member['commission2'];
            }
			$money = $post['money'];
			if($money<=0 || $money < $set['comwithdrawmin']){
				return $this->json(['status'=>0,'msg'=>'提现金额必须大于'.($set['comwithdrawmin']?$set['comwithdrawmin']:0)]);
			}
			if($money > $cancommission){
				return $this->json(['status'=>0,'msg'=>'可提现'.t('余额').'不足']);
			}
			
			// 检查并应用现金券扣除规则 - 修复百分比计算问题
        if ($set['comwithdrawkouxianquan']) {
            // 将百分比转换为小数进行计算
            $kouxianquanRate = floatval($set['comwithdrawkouxianquan']) / 100;
            $needCashVoucher = $money * $kouxianquanRate;
            
            // 检查现金券是否足够
            if ($this->member['heiscore'] >= $needCashVoucher) {
                // 扣除现金券
                \app\common\Member::addhei(aid, mid, -$needCashVoucher, '佣金提现');
                \think\facade\Log::write('佣金提现扣除现金券，提现金额:'.$money.'，扣除比例:'.$set['comwithdrawkouxianquan'].'%，扣除现金券:'.$needCashVoucher);
            } else {
                // 现金券不足，提示用户
                return $this->json(['status'=>0,'msg'=>'您的'.t('现金券').'不足以完成此次提现，需要'.$needCashVoucher.'现金券']);
            }
        }
        
        
            $memberlevel = Db::name('member_level')->where('id',$this->member['levelid'])->find();
			$beishuyongjin = $memberlevel['yongjinbeishu'];
			$beishuyongjin2 = $post['money']/$beishuyongjin;
		if ($beishuyongjin == 0 || $beishuyongjin2 == 0) {
                // 不做任何处理或直接跳过此检查
            } else {
                // 使用 fmod 检查是否为倍数
                if (fmod($post['money'], $beishuyongjin) != 0) {
                    return $this->json(['status' => 0, 'msg' => '当前等级佣金提现必须为' . $beishuyongjin . '的倍数']);
                }
            }
				//获取用户的订单金额
		    $ordercount = Db::name('shop_order')->where('aid',aid)->where('mid',mid)->where('status','<>',0)->where('status','<>',4)->sum('totalprice');
		  //  等级提现额度
		    $ordercount2 = $memberlevel['ti_edu'];
		  //  $ordercount = $ordercount+$ordercount2;
    		//查看倍数
    		if($set['withbeishu'] >0)
    		{
    		    //总共可提现额度 $tixiancount
    		    $tixiancount = $ordercount *$set['withbeishu'];
    		    $tixiancount = $ordercount2+$tixiancount;
    		    //计算已经申请的额度
    		    $tixianshenqingcount = Db::name('member_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status','<>',2)->sum('txmoney');
    		    $tixiancount = $tixiancount-$tixianshenqingcount;
    		    if($tixiancount <$money)
    		    {
    		        return $this->json(['status'=>0,'msg'=>'需要原点购买后才能提现']);
    		    }
    		}
			$ordernum = date('ymdHis').aid.rand(1000,9999);
			$record['aid'] = aid;
			$record['mid'] = mid;
			$record['createtime']= time();
			$record['money'] = $money*(1-$set['comwithdrawfee']*0.01);
			$record['txmoney'] = $money;
			if($post['paytype']=='支付宝'){
				$record['aliaccountname'] = $this->member['aliaccountname'];
				$record['aliaccount'] = $this->member['aliaccount'];
			}
			if($post['paytype']=='银行卡'){
				$record['bankname'] = $this->member['bankname'].($this->member['bankaddress'] ? ' '.$this->member['bankaddress'] : '');;
				$record['bankcarduser'] = $this->member['bankcarduser'];
				$record['bankcardnum'] = $this->member['bankcardnum'];
			}
			$record['ordernum'] = $ordernum;
			$record['paytype'] = $post['paytype'];
			$record['platform'] = platform;
			$recordid = Db::name('member_commission_withdrawlog')->insertGetId($record);
            if($money>=$this->member['commission']){
                \app\common\Member::addcommission(aid,mid,0,-($this->member['commission']),t('佣金').'提现');
                \app\common\Member::addcommission2(aid,mid,0,-($money-$this->member['commission']),t('佣金').'提现');
            }else{
                \app\common\Member::addcommission(aid,mid,0,-($money),t('佣金').'提现');
            }

			
			$tmplcontent = array();
			$tmplcontent['first'] = '有客户申请'.t('佣金').'提现';
			$tmplcontent['remark'] = '点击进入查看~';
			$tmplcontent['keyword1'] = $this->member['nickname'];
			$tmplcontent['keyword2'] = date('Y-m-d H:i');
			$tmplcontent['keyword3'] = $money.'元';
			$tmplcontent['keyword4'] = $post['paytype'];
			\app\common\Wechat::sendhttmpl(aid,0,'tmpl_withdraw',$tmplcontent,m_url('admin/finance/comwithdrawlog'));

			$tmplcontent['thing4'] = '提现到'.$post['paytype'];
			\app\common\Wechat::sendhtwxtmpl(aid,0,'tmpl_withdraw',$tmplcontent,'admin/finance/comwithdrawlog');

			if($set['withdraw_autotransfer']){
				if($set['comwithdrawbl'] > 0 && $set['comwithdrawbl'] < 100){
					$paymoney = round($record['money'] * $set['comwithdrawbl'] * 0.01,2);
					$tomoney = round($record['money'] - $paymoney,2);
				}else{
					$paymoney = $record['money'];
					$tomoney = 0;
				}
				$rs = \app\common\Wxpay::transfers(aid,mid,$paymoney,$record['ordernum'],platform,t('佣金').'提现');
				if($rs['status']==0){
					return json(['status'=>1,'msg'=>'提交成功,请等待打款']);
				}else{
					if($tomoney > 0){
						\app\common\Member::addmoney(aid,mid,$tomoney,t('佣金').'提现');
					}
					Db::name('member_commission_withdrawlog')->where('aid',aid)->where('id',$recordid)->update(['status'=>3,'paytime'=>time(),'paynum'=>$rs['resp']['payment_no']]);
					//提现成功通知
					$tmplcontent = [];
					$tmplcontent['first'] = '您的提现申请已打款，请留意查收';
					$tmplcontent['remark'] = '请点击查看详情~';
					$tmplcontent['money'] = (string) round($record['money'],2);
					$tmplcontent['timet'] = date('Y-m-d H:i',$record['createtime']);
					\app\common\Wechat::sendtmpl(aid,$record['mid'],'tmpl_tixiansuccess',$tmplcontent,m_url('pages/my/usercenter'));
					//订阅消息
					$tmplcontent = [];
					$tmplcontent['amount1'] = $record['money'];
					$tmplcontent['thing3'] = '微信打款';
					$tmplcontent['time5'] = date('Y-m-d H:i');
					
					$tmplcontentnew = [];
					$tmplcontentnew['amount3'] = $record['money'];
					$tmplcontentnew['phrase9'] = '微信打款';
					$tmplcontentnew['date8'] = date('Y-m-d H:i');
					\app\common\Wechat::sendwxtmpl(aid,$record['mid'],'tmpl_tixiansuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);
					//短信通知
					if($this->member['tel']){
						\app\common\Sms::send(aid,$this->member['tel'],'tmpl_tixiansuccess',['money'=>$record['money']]);
					}
					return json(['status'=>1,'msg'=>$rs['msg']]);
				}
			}
			return $this->json(['status'=>1,'msg'=>'提交成功,请等待打款']);
		}
		if($set['comwithdraw'] == 0){
			return $this->json(['status'=>-4,'msg'=>t('佣金').'提现功能未开启']);
		}
		$memberlevel = Db::name('member_level')->where('id',$this->member['levelid'])->find();
		if( ['comwithdraw'] == 0){
			return $this->json(['status'=>-4,'msg'=>t('佣金').'提现未开启','url'=>'/activity/commission/index']);
		}
		//获取用户的订单金额
		$ordercount = Db::name('shop_order')->where('aid',aid)->where('mid',$this->member['id'])->where('status','<>',0)->where('status','<>',4)->sum('totalprice');
		//  等级提现额度
	    $ordercount2 = $memberlevel['ti_edu'];
	    $ordercount = $ordercount;
		//查看倍数
		if($set['withbeishu'] >0)
		{
		    //总共可提现额度 $tixiancount
		    $tixiancount = $ordercount *$set['withbeishu'];
		    $tixiancount = $ordercount2+$tixiancount;
		    $zhanshi = 1;
		    //计算已经申请的额度
		    $tixianshenqingcount = Db::name('member_withdrawlog')->where('aid',aid)->where('mid',mid)->where('status','<>',2)->sum('txmoney');
		    $tixiancount = $tixiancount-$tixianshenqingcount;
		}else{
		   $tixiancount = 0; 
		   $zhanshi = 0;
		}
		//订阅消息
		$wx_tmplset = Db::name('wx_tmplset')->where('aid',aid)->find();
		$tmplids = [];
		if($wx_tmplset['tmpl_tixiansuccess_new']){
			$tmplids[] = $wx_tmplset['tmpl_tixiansuccess_new'];
		}elseif($wx_tmplset['tmpl_tixiansuccess']){
			$tmplids[] = $wx_tmplset['tmpl_tixiansuccess'];
		}
		if($wx_tmplset['tmpl_tixianerror_new']){
			$tmplids[] = $wx_tmplset['tmpl_tixianerror_new'];
		}elseif($wx_tmplset['tmpl_tixianerror']){
			$tmplids[] = $wx_tmplset['tmpl_tixianerror'];
		}
	
		$rdata = [];
		$rdata['userinfo'] = Db::name('member')->where('id',mid)->field('id,headimg,nickname,money,score,commission,commission2,aliaccount,bankname,bankcarduser,bankcardnum,scorehuang,heiscore,contribution_num')->find();
		$rdata['userinfo']['tixiancount']= $tixiancount;
		$rdata['userinfo']['zhanshi']= $zhanshi;
		$rdata['sysset'] = $set;
        $rdata['memberlevel'] = $memberlevel;
		$rdata['tmplids'] = $tmplids;
		return $this->json($rdata);
	}
	//脱离记录
	public function myteam(){
	   	$st = input('param.st/d');
	   	$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		$order = 'member_moneylog.id desc';
		$where =[];
		$where[] = ['member_moneylog.aid','=',aid];
	   	if($st ==1)
	   	{
	   	   	$where[] = ['member_moneylog.type','=',0]; 
	   	   	$where[] = ['member_moneylog.ytpid','=',mid]; 
	   	}else{
	   	    $where[] = ['member_moneylog.type','=',1];
	   	    $where[] = ['member_moneylog.newpid','=',mid]; 
	   	}
		
		if(!mid){
			$datalist = [];
		}else{
		    $datalist = Db::name('liandong_tuoli_log')->alias('member_moneylog')
			->field('member.nickname,member.levelid,member.headimg,member1.nickname as oldnickname,member1.headimg as oldheadimg,member2.nickname as newnickname,member2.headimg as newheadimg,member_moneylog.*,from_unixtime(member.createtime)createtime1,from_unixtime(member_moneylog.createtime)createtime')
			->join('member member','member.id=member_moneylog.mid')
			->leftjoin('member member1','member1.id=member_moneylog.ytpid')
			->leftjoin('member member2','member2.id=member_moneylog.newpid')
			->where($where)->page($pagenum,$pernum)->order($order)->select()->toArray();
		}
		if(!$datalist) $datalist = [];
		foreach($datalist as $k=>$v){
			$level = Db::name('member_level')->where('aid',aid)->where('id',$v['levelid'])->find();
            $datalist[$k]['levelname'] = $level['name'];
            $datalist[$k]['levelsort'] = $level['sort'];
			if($userlevel['team_showtel'] == 0){
				$datalist[$k]['tel'] = '';
			}
		}
		$rdata = [];
		$rdata['datalist'] = $datalist;
		$rdata['st'] = $st;
		return $this->json($rdata);
	}
	
	
	//我的团队
	public function team(){
		
		$userinfo = Db::name('member')->field('id,nickname,headimg,levelid')->where('aid',aid)->where('id',mid)->find();
		// 获取新增的团队列表设置
		$team_settings = Db::name('admin_set')->where('aid', aid)->field('team_list_yeji_show, team_list_relation_show, team_list_commission_show, team_list_subnum_show, team_list_member_count_show, team_list_enable, team_list_direct_count_show, team_list_statistics_show, team_list_consume_show')->find();
		
		// 检查团队列表是否开启
		if (($team_settings['team_list_enable'] ?? 0) == 0) {
			return $this->json(['status' => 0, 'msg' => '团队列表功能已关闭']);
		}
		
		// 获取时间筛选参数
		$start_time = input('param.start_time', '');
		$end_time = input('param.end_time', '');
		$time_where = '';
		
		// 处理时间筛选条件
		if ($start_time && $end_time) {
			$start_timestamp = strtotime($start_time . ' 00:00:00');
			$end_timestamp = strtotime($end_time . ' 23:59:59');
			$time_where = " and createtime >= $start_timestamp and createtime <= $end_timestamp";
		}
		
		// 获取联动2+1功能状态
		$liandong_set = Db::name('liandong_set')->where('aid', aid)->find();
		$liandong_status = $liandong_set ? ($liandong_set['status'] ?? 0) : 0;
		
		// 计算团队总人数（无限级下级成员）
		$team_total_count = 0;
		$topids = [mid];
		while(true){
			$child_where = "aid=".aid." and isxuni=0";
			// 如果有时间筛选条件，添加到查询中
			if ($start_time && $end_time) {
				$child_where .= " and createtime >= $start_timestamp and createtime <= $end_timestamp";
			}
			
			$childs = Db::name('member')->whereIn('pid',$topids)->where($child_where)->select()->toArray();
		
			if(empty($childs)){
				break;
			}else{
				$topids = [];
				foreach ($childs as $item){
					$topids[] = $item['id'];
					$team_total_count += 1; // 统计所有下级成员，不限制levelid
				}
			}
		}
		
		// 循环遍历团队总人数（保留原有逻辑用于兼容性）
        $teamnum = 0;
        $levelids = Db::name('member_level')->where('aid',aid)->where('id','>=',2)->column('id');
        $topids = [mid];
        while(true){
            $childs = Db::name('member')->whereIn('pid',$topids)->select()->toArray();
        
            if(empty($childs)){
                break;
            }else{
                $topids = [];
                foreach ($childs as $item){
                    $topids[] = $item['id'];
                    if($item['levelid'] >= 2){
                         $teamnum+=1;
                    }
                   
                }
            }
        }
        $userinfo['teamnum'] = $teamnum;
        $userinfo['team_total_count'] = $team_total_count; // 新增团队总人数

		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$userinfo['levelid'])->find();

		$downdeep = input('param.st/d');
		$pernum = 20;
		$pagenum = input('post.pagenum');
		$keyword = input('post.keyword');
		$where2 = "1=1";
		if($keyword) $where2 = "(nickname like '%{$keyword}%' or realname like '%{$keyword}%' or tel like '%{$keyword}%')";
		if(!$pagenum) $pagenum = 1;
		if(!$downdeep) $downdeep = 1;
		if(!mid){
			$datalist = [];
		}else{
			// 添加时间筛选条件到查询中
			$base_where = "aid=".aid." and isxuni =0 $time_where";
			
			if($downdeep == 1){
				$datalist = Db::name('member')->field("id,nickname,headimg,tel,totalcommission,from_unixtime(createtime)createtime,levelid")->where($base_where." and pid=".mid."")->where($where2)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
			}elseif($downdeep == 2){
				$datalist = Db::query("select id,nickname,headimg,tel,totalcommission,from_unixtime(createtime)createtime,levelid from ".table_name('member')." where $base_where and $where2 and pid in(select id from ".table_name('member')." where pid=".mid.") order by id desc limit ".($pagenum*$pernum-$pernum).','.$pernum);
			}elseif($downdeep == 3){
				$datalist = Db::query("select id,nickname,headimg,tel,totalcommission,from_unixtime(createtime)createtime,levelid from ".table_name('member')." where $base_where and pid in(select id from ".table_name('member')." where aid=".aid." and $where2 and pid in(select id from ".table_name('member')." where pid=".mid.")) order by id desc limit ".($pagenum*$pernum-$pernum).','.$pernum);
			}
		}
		if(!$datalist) $datalist = [];
		
		// 统计数据计算
		$statistics = [];
		if (($team_settings['team_list_statistics_show'] ?? 0) == 1) {
			// 计算真正的直推/层级总人数（不分页）
			$total_count = 0;
			if ($downdeep == 1) {
				// 一级直推总人数
				$total_count = Db::name('member')->where($base_where." and pid=".mid."")->where($where2)->count();
			} elseif ($downdeep == 2) {
				// 二级总人数
				$total_count = Db::query("select count(1) as count from ".table_name('member')." where $base_where and $where2 and pid in(select id from ".table_name('member')." where pid=".mid.")")[0]['count'];
			} elseif ($downdeep == 3) {
				// 三级总人数
				$total_count = Db::query("select count(1) as count from ".table_name('member')." where $base_where and pid in(select id from ".table_name('member')." where aid=".aid." and $where2 and pid in(select id from ".table_name('member')." where pid=".mid."))")[0]['count'];
			}
			$statistics['total_count'] = $total_count;
			
			// 添加团队总人数统计
			$statistics['team_total_count'] = $team_total_count;
			
			// 添加当前页数据数量统计
			$statistics['current_page_count'] = count($datalist);
			
			// 添加日志记录统计信息
		//	\think\facade\Log::write('团队统计数据计算 - 用户ID:'.mid.' 真实总人数:'.$total_count.' 当前页数量:'.count($datalist).' 团队总人数:'.$team_total_count.' 层级:'.$downdeep);
			
			// 统计总下单金额（包含买单金额）
			$total_amount = 0;
			$member_ids = array_column($datalist, 'id');
			
			if (!empty($member_ids)) {
				// 计算下单金额（普通订单）
				$order_amount_sql = "SELECT SUM(totalprice) as amount FROM ".table_name('shop_order')." WHERE aid=".aid." AND mid IN (".implode(',', $member_ids).") AND status IN (1,2,3)";
				if ($start_time && $end_time) {
					$order_amount_sql .= " AND createtime >= $start_timestamp AND createtime <= $end_timestamp";
				}
				$order_amount = Db::query($order_amount_sql);
				$total_amount += ($order_amount[0]['amount'] ?? 0);
				
				// 计算买单金额（如果有买单功能）
				$maidan_amount_sql = "SELECT SUM(money) as amount FROM ".table_name('maidan_order')." WHERE aid=".aid." AND mid IN (".implode(',', $member_ids).") AND status IN (1,2)";
				if ($start_time && $end_time) {
					$maidan_amount_sql .= " AND createtime >= $start_timestamp AND createtime <= $end_timestamp";
				}
				$maidan_result = Db::query($maidan_amount_sql);
				if ($maidan_result) {
					$total_amount += ($maidan_result[0]['amount'] ?? 0);
				}
			}
			
			$statistics['total_amount'] = number_format($total_amount, 2);
		}
		
		foreach($datalist as $k=>$v){
			//if($downdeep==1){
			//	$commission = Db::name('shop_order_goods')->where('aid',aid)->where('mid',$v['id'])->where('parent1',mid)->where('status',3)->sum('parent1commission');
			//}
			//if($downdeep==2){
			//	$commission = Db::name('shop_order_goods')->where('aid',aid)->where('mid',$v['id'])->where('parent2',mid)->where('status',3)->sum('parent2commission');
			//}
			//if($downdeep==3){
			//	$commission = Db::name('shop_order_goods')->where('aid',aid)->where('mid',$v['id'])->where('parent3',mid)->where('status',3)->sum('parent3commission');
			//}

			$commission = Db::name('member_commission_record')->where('aid',aid)->where('mid',mid)->where('frommid',$v['id'])->where('status',1)->sum('commission');

			$datalist[$k]['commission'] = 0 + $commission;
			$datalist[$k]['downcount'] = 0 + Db::name('member')->where('aid',aid)->where('pid',$v['id'])->count();
			
			// 计算每个成员的订单数量（包括普通订单和买单订单）
			$order_where = "aid=".aid." and mid=".$v['id']." and status in (1,2,3)";
			$maidan_where = "aid=".aid." and mid=".$v['id']." and status in (1,2)";
			
			// 如果有时间筛选条件，添加到订单查询中
			if ($start_time && $end_time) {
				$order_where .= " and createtime >= $start_timestamp and createtime <= $end_timestamp";
				$maidan_where .= " and createtime >= $start_timestamp and createtime <= $end_timestamp";
			}
			
			// 统计普通订单数量
			$shop_order_count = Db::name('shop_order')->where($order_where)->count();
			// 统计买单订单数量
			$maidan_order_count = Db::name('maidan_order')->where($maidan_where)->count();
			// 总订单数量 = 普通订单 + 买单订单
			$datalist[$k]['ordercount'] = 0 + $shop_order_count + $maidan_order_count;
			
			// 直推人数统计（如果开启）
			if (($team_settings['team_list_direct_count_show'] ?? 0) == 1) {
				$datalist[$k]['direct_count'] = 0 + Db::name('member')->where('aid',aid)->where('pid',$v['id'])->count();
			}
			
			// 使用系统现有的方法获取团队下级成员ID
			$levelids = Db::name('member_level')->where('aid',aid)->column('id');
			$allxiaji = \app\common\Member::getteammids(aid, $v['id'], 999, $levelids);

			// 如果没有下级，至少包含自己
			if (empty($allxiaji)) {
				$allxiaji = [$v['id']];
			} else {
				// 包含自己和所有下级
				$allxiaji[] = $v['id'];
			}

			// 计算团队业绩（包含自己和所有下级的订单金额）
			// 1. 计算普通商品订单业绩
			$shop_performance = Db::name('shop_order')
				->where('aid', aid)
				->whereIn('mid', $allxiaji)
				->whereIn('status', [1, 2, 3])
				->sum('totalprice');

			// 2. 计算买单订单业绩
			$maidan_performance = Db::name('maidan_order')
				->where('aid', aid)
				->whereIn('mid', $allxiaji)
				->where('status', 1) // 买单订单状态1表示已支付
				->sum('paymoney'); // 买单使用paymoney字段

			// 3. 团队总业绩 = 商品订单业绩 + 买单订单业绩
			$team_performance = $shop_performance + $maidan_performance;
			$datalist[$k]['tuanduiyeji'] = number_format($team_performance, 2);

			// 添加调试日志
			\think\facade\Log::write('团队业绩计算 - 用户ID:'.$v['id'].' 用户昵称:'.$v['nickname'].' 团队成员IDs:'.implode(',',$allxiaji).' 商品订单业绩:'.$shop_performance.' 买单订单业绩:'.$maidan_performance.' 总业绩:'.$team_performance, 'info');
			$level = Db::name('member_level')->where('aid',aid)->where('id',$v['levelid'])->find();
            $datalist[$k]['levelname'] = $level['name'];
            $datalist[$k]['levelsort'] = $level['sort'];
			if($userlevel['team_showtel'] == 0){
				$datalist[$k]['tel'] = '';
			}
			
			// 只有在联动2+1功能开启时才显示原推荐人
			if ($liandong_status == 1) {
				// 查询是否有原推荐人记录
				$tuoliLog = Db::name('liandong_tuoli_log')->where('aid', aid)->where('mid', $v['id'])->where('type', 0)->order('id desc')->find();
				if ($tuoliLog && $tuoliLog['ytpid'] > 0) {
					// 获取原推荐人信息
					$originalRecommender = Db::name('member')->field('id,nickname,headimg,tel')->where('aid', aid)->where('id', $tuoliLog['ytpid'])->find();
					if ($originalRecommender) {
						$datalist[$k]['has_original_recommender'] = 1;
						$datalist[$k]['original_recommender'] = $originalRecommender;
					} else {
						$datalist[$k]['has_original_recommender'] = 0;
					}
				} else {
					$datalist[$k]['has_original_recommender'] = 0;
				}
			} else {
				$datalist[$k]['has_original_recommender'] = 0;
			}
			
			// 计算消费金额（只计算当前用户自己的消费，不包含下级）
			if (($team_settings['team_list_consume_show'] ?? 0) == 1) {
				// 1. 计算普通商品订单消费金额
				$shop_consume = Db::name('shop_order')
					->where('aid', aid)
					->where('mid', $v['id'])
					->whereIn('status', [1, 2, 3])
					->sum('totalprice');

				// 2. 计算买单订单消费金额
				$maidan_consume = Db::name('maidan_order')
					->where('aid', aid)
					->where('mid', $v['id'])
					->where('status', 1) // 买单订单状态1表示已支付
					->sum('paymoney'); // 买单使用paymoney字段

				// 3. 总消费金额 = 商品订单消费 + 买单订单消费
				$consume_amount = $shop_consume + $maidan_consume;
				$datalist[$k]['consume_amount'] = number_format($consume_amount, 2);

				// 添加调试日志
				\think\facade\Log::write('消费金额计算 - 用户ID:'.$v['id'].' 用户昵称:'.$v['nickname'].' 商品订单消费:'.$shop_consume.' 买单订单消费:'.$maidan_consume.' 总消费:'.$consume_amount, 'info');
			}
		}
		if($pagenum == 1){
			//我的团队
			$userinfo['myteamCount1'] = 0 + Db::name('member')->where("aid=".aid." and pid=".mid." and levelid >=".'5 and isxuni =0')->count();
			$myteamCount2 = Db::query("select count(1) c,levelid from ".table_name('member')." where aid=".aid." and isxuni =0 and pid in(select id from ".table_name('member')." where pid=".mid.") group by levelid having levelid > 5");
			$userinfo['myteamCount2'] = 0 + $myteamCount2[0]['c'];
			$myteamCount3 = Db::query("select count(1)c,levelid from ".table_name('member')." where aid=".aid." and isxuni =0 and pid in(select id from ".table_name('member')." where aid=".aid." and pid in(select id from ".table_name('member')." where pid=".mid.")) group by levelid having levelid > 5");
			$userinfo['myteamCount3'] = 0 + $myteamCount3[0]['c'];
			$userinfo['myteamCount'] = $userinfo['myteamCount1'] + $userinfo['myteamCount2'];
			//分销订单
			$userinfo['fxorderCount'] = 0 + Db::name("shop_order_goods")->where("aid=".aid." and (parent1=".mid." or parent2=".mid." or parent3=".mid." or parent4=".mid." or parent5=".mid." or parent6=".mid." or parent7=".mid." or parent8=".mid." or parent9=".mid." or parent10=".mid." or parent11=".mid." or parent12=".mid." or parent13=".mid." or parent14=".mid." or parent15=".mid." or parent16=".mid." or parent17=".mid." or parent18=".mid." or parent19=".mid." or parent20=".mid.")")->count();
            // 检查 team_levelup_id 是否为空，避免 explode() 报错
            $team_levelup_id = $userlevel['team_levelup_id'] ?? '';
            if (!empty($team_levelup_id)) {
                $levelList = Db::name('member_level')->field('id,sort,name')->where('aid',aid)->where('id', 'in', explode(',',$team_levelup_id))->order('sort', 'asc')->select();
            } else {
                $levelList = [];
            }
            $newKey = 'id';
            $levelList = $levelList->dictionary(null, $newKey);
		}
		$rdata = [];
		$rdata['datalist'] = $datalist;
		$rdata['userinfo'] = $userinfo;
		$rdata['userlevel'] = $userlevel;
		$rdata['levelList'] = $levelList;
		$rdata['st'] = $downdeep;
		// 添加团队列表设置到返回数据
		$rdata['team_settings'] = [
			'show_team_performance' => $team_settings['team_list_yeji_show'] ?? 0,
			'show_relation_chart' => $team_settings['team_list_relation_show'] ?? 0,
			'show_other_commission' => $team_settings['team_list_commission_show'] ?? 0,
			'show_subordinate_count' => $team_settings['team_list_subnum_show'] ?? 0,
			'show_member_count' => $team_settings['team_list_member_count_show'] ?? 0,
			'team_list_enable' => $team_settings['team_list_enable'] ?? 0,
			'show_direct_count' => $team_settings['team_list_direct_count_show'] ?? 0,
			'show_statistics' => $team_settings['team_list_statistics_show'] ?? 0,
			// 添加原推荐人显示设置（只有在联动2+1功能开启时才启用）
			'show_original_recommender' => $liandong_status,
			'show_consume_amount' => $team_settings['team_list_consume_show'] ?? 0
		];
		$rdata['statistics'] = $statistics;
		return $this->json($rdata);
	}
	//分销订单
	public function agorder(){
		//拼团的佣金订单
		$pernum = 20;
		$pagenum = input('param.pagenum') ? input('param.pagenum') :1;
		$where = '';
		$st = input('param.st/d');
		if(!$st) $st = 0;
		if($st==1){//待付款
			$where = " and o.status=0";
		}
		if($st==2){//已付款
			$where = " and (o.status=1 or o.status=2)";
		}
		if($st==3){//已完成
			$where = " and o.status=3";
		}
		if($st==5){//售后
			$where = " and o.refund_money>0";
		}
		$datalist = Db::query("select og.id,og.mid,o.ordernum,o.status,o.createtime,o.paytime,og.name,og.proid,og.pic,og.num,og.parent1,og.parent2,og.parent3,og.parent4,og.parent5,og.parent6,og.parent7,og.parent8,og.parent9,og.parent10,og.parent11,og.parent12,og.parent13,og.parent14,og.parent15,og.parent16,og.parent17,og.parent18,og.parent19,og.parent20,og.parent1commission,og.parent2commission,og.parent3commission,og.parent4commission,og.parent5commission,og.parent6commission,og.parent7commission,og.parent8commission,og.parent9commission,og.parent10commission,og.parent11commission,og.parent12commission,og.parent13commission,og.parent14commission,og.parent15commission,og.parent16commission,og.parent17commission,og.parent18commission,og.parent19commission,og.parent20commission,og.parent1score,og.parent2score,og.parent3score,og.parent4score,og.parent5score,og.parent6score,og.parent7score,og.parent8score,og.parent9score,og.parent10score,og.parent11score,og.parent12score,og.parent13score,og.parent14score,og.parent15score,og.parent16score,og.parent17score,og.parent18score,og.parent19score,og.parent20score    from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." $where and  ( og.parent1=".mid." or og.parent2=".mid." or og.parent3=".mid." or og.parent4=".mid." or og.parent5=".mid." or og.parent6=".mid." or og.parent7=".mid." or og.parent8=".mid." or og.parent9=".mid." or og.parent10=".mid." or og.parent11=".mid." or og.parent12=".mid." or og.parent13=".mid." or og.parent14=".mid." or og.parent15=".mid." or og.parent16=".mid." or og.parent17=".mid." or og.parent18=".mid." or og.parent19=".mid." or og.parent20=".mid.") order by og.id desc limit ".($pagenum*$pernum-$pernum).','.$pernum);
// 		var_dump($datalist);die;
		if(!$datalist) $datalist = [];
		$newdatalist = [];
		foreach($datalist as $k=>$v){
			$data = [];
			$data['ordernum'] = $v['ordernum'];
			$data['name'] = $v['name'];
			$data['pic'] = $v['pic'];
			$data['num'] = $v['num'];
			$data['createtime'] = date('Y-m-d H:i',$v['createtime']);
			$data['status'] = $v['status'];
			$memberinfo = Db::name('member')->where('id',$v['mid'])->field('nickname,headimg,tel')->find();
			$data['nickname'] = $memberinfo['nickname'];
			$data['headimg'] = $memberinfo['headimg'];
        	$data['tel'] = $memberinfo['tel'];
			if($v['parent1'] == mid){
				$data['dengji'] = '一级';
				if($v['parent1score'] > 0){
					$data['commission'] = $v['parent1score'].t('积分');
				}else{
					$data['commission'] = $v['parent1commission'].'元';
				}
				$data['dannum'] = 0;
				}
			if($v['parent2'] == mid){
				$data['dengji'] = '二级';
				if($v['parent2score'] > 0){
					$data['commission'] = $v['parent2score'].t('积分');
				}else{
					$data['commission'] = $v['parent2commission'].'元';
				}
				$data['dannum'] = 0;
				}
			if($v['parent3'] == mid){
				$data['dengji'] = '三级';
				if($v['parent3score'] > 0){
					$data['commission'] = $v['parent3score'].t('积分');
				}else{
					$data['commission'] = $v['parent3commission'].'元';
				}
				$data['dannum'] = 0;
				}
			if($v['parent4'] == mid){
				$data['dengji'] = '四级';
				if($v['parent4score'] > 0){
					$data['commission'] = $v['parent4score'].t('积分');
				}else{
					$data['commission'] = $v['parent4commission'].'元';
				}
				$data['dannum'] = 0;
				}
		    if($v['parent5'] == mid){
				$data['dengji'] = '五级';
				if($v['parent5score'] > 0){
					$data['commission'] = $v['parent5score'].t('积分');
				}else{
					$data['commission'] = $v['parent5commission'].'元';
				}
				$data['dannum'] = 0;
				}	
		    if($v['parent6'] == mid){
				$data['dengji'] = '六级';
				if($v['parent6score'] > 0){
					$data['commission'] = $v['parent6score'].t('积分');
				}else{
					$data['commission'] = $v['parent6commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent7'] == mid){
				$data['dengji'] = '七级';
				if($v['parent7score'] > 0){
					$data['commission'] = $v['parent7score'].t('积分');
				}else{
					$data['commission'] = $v['parent7commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent8'] == mid){
				$data['dengji'] = '八级';
				if($v['parent8score'] > 0){
					$data['commission'] = $v['parent8score'].t('积分');
				}else{
					$data['commission'] = $v['parent8commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent9'] == mid){
				$data['dengji'] = '九级';
				if($v['parent9score'] > 0){
					$data['commission'] = $v['parent9score'].t('积分');
				}else{
					$data['commission'] = $v['parent9commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent10'] == mid){
				$data['dengji'] = '十级';
				if($v['parent10score'] > 0){
					$data['commission'] = $v['parent10score'].t('积分');
				}else{
					$data['commission'] = $v['parent10commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent11'] == mid){
				$data['dengji'] = '十一级';
				if($v['parent11score'] > 0){
					$data['commission'] = $v['parent11score'].t('积分');
				}else{
					$data['commission'] = $v['parent11commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent12'] == mid){
				$data['dengji'] = '十二级';
				if($v['parent12score'] > 0){
					$data['commission'] = $v['parent12score'].t('积分');
				}else{
					$data['commission'] = $v['parent12commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent13'] == mid){
				$data['dengji'] = '十三级';
				if($v['parent13score'] > 0){
					$data['commission'] = $v['parent13score'].t('积分');
				}else{
					$data['commission'] = $v['parent13commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent14'] == mid){
				$data['dengji'] = '十四级';
				if($v['parent14score'] > 0){
					$data['commission'] = $v['parent14score'].t('积分');
				}else{
					$data['commission'] = $v['parent14commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent15'] == mid){
				$data['dengji'] = '十五级';
				if($v['parent15score'] > 0){
					$data['commission'] = $v['parent15score'].t('积分');
				}else{
					$data['commission'] = $v['parent15commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent16'] == mid){
				$data['dengji'] = '十六级';
				if($v['parent16score'] > 0){
					$data['commission'] = $v['parent16score'].t('积分');
				}else{
					$data['commission'] = $v['parent16commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent17'] == mid){
				$data['dengji'] = '十七级';
				if($v['parent17score'] > 0){
					$data['commission'] = $v['parent17score'].t('积分');
				}else{
					$data['commission'] = $v['parent17commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent18'] == mid){
				$data['dengji'] = '十八级';
				if($v['parent18score'] > 0){
					$data['commission'] = $v['parent18score'].t('积分');
				}else{
					$data['commission'] = $v['parent18commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent19'] == mid){
				$data['dengji'] = '十九级';
				if($v['parent19score'] > 0){
					$data['commission'] = $v['parent19score'].t('积分');
				}else{
					$data['commission'] = $v['parent19commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if($v['parent20'] == mid){
				$data['dengji'] = '二十级';
				if($v['parent20score'] > 0){
					$data['commission'] = $v['parent20score'].t('积分');
				}else{
					$data['commission'] = $v['parent20commission'].'元';
				}
				$data['dannum'] = 0;
				}	
			if(false){}else{
				$data['order_info'] = false;
			}
			$newdatalist[] = $data;
		}
		if(request()->isPost()){
			return $this->json(['data'=>$newdatalist]);
		}

		$count = Db::query("select count(1)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (og.parent1=".mid." or og.parent2=".mid." or og.parent3=".mid." or og.parent4=".mid." or og.parent5=".mid." or og.parent6=".mid." or og.parent7=".mid." or og.parent8=".mid." or og.parent9=".mid." or og.parent10=".mid." or og.parent11=".mid." or og.parent12=".mid." or og.parent13=".mid." or og.parent14=".mid." or og.parent15=".mid." or og.parent16=".mid." or og.parent17=".mid." or og.parent18=".mid." or og.parent19=".mid." or og.parent20=".mid.")");
		$count = 0 + $count[0]['c'];

		$count1 = Db::query("select count(1)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and o.status=0 and (og.parent1=".mid." or og.parent2=".mid." or og.parent3=".mid." or og.parent4=".mid." or og.parent5=".mid." or og.parent6=".mid." or og.parent7=".mid." or og.parent8=".mid." or og.parent9=".mid." or og.parent10=".mid." or og.parent11=".mid." or og.parent12=".mid." or og.parent13=".mid." or og.parent14=".mid." or og.parent15=".mid." or og.parent16=".mid." or og.parent17=".mid." or og.parent18=".mid." or og.parent19=".mid." or og.parent20=".mid.")");
		$count1 = 0 + $count1[0]['c'];

		$count2 = Db::query("select count(1)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and (og.parent1=".mid." or og.parent2=".mid." or og.parent3=".mid." or og.parent4=".mid." or og.parent5=".mid." or og.parent6=".mid." or og.parent7=".mid." or og.parent8=".mid." or og.parent9=".mid." or og.parent10=".mid." or og.parent11=".mid." or og.parent12=".mid." or og.parent13=".mid." or og.parent14=".mid." or og.parent15=".mid." or og.parent16=".mid." or og.parent17=".mid." or og.parent18=".mid." or og.parent19=".mid." or og.parent20=".mid.")");
		$count2 = 0 + $count2[0]['c'];

		$count3 = Db::query("select count(1)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and o.status=3 and (og.parent1=".mid." or og.parent2=".mid." or og.parent3=".mid." or og.parent4=".mid." or og.parent5=".mid." or og.parent6=".mid." or og.parent7=".mid." or og.parent8=".mid." or og.parent9=".mid." or og.parent10=".mid." or og.parent11=".mid." or og.parent12=".mid." or og.parent13=".mid." or og.parent14=".mid." or og.parent15=".mid." or og.parent16=".mid." or og.parent17=".mid." or og.parent18=".mid." or og.parent19=".mid." or og.parent20=".mid.")");
		$count3 = 0 + $count3[0]['c'];

		$commissionyj1 = Db::query("select sum(parent1commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent1=".mid."");
		$commissionyj2 = Db::query("select sum(parent2commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent2=".mid."");
		$commissionyj3 = Db::query("select sum(parent3commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent3=".mid."");
		$commissionyj4 = Db::query("select sum(parent4commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent4=".mid."");
		$commissionyj5 = Db::query("select sum(parent5commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent5=".mid."");
		$commissionyj6 = Db::query("select sum(parent6commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent6=".mid."");
		$commissionyj7 = Db::query("select sum(parent7commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent7=".mid."");
		$commissionyj8 = Db::query("select sum(parent8commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent8=".mid."");
		$commissionyj9 = Db::query("select sum(parent9commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent9=".mid."");
		$commissionyj10 = Db::query("select sum(parent10commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent10=".mid."");
		$commissionyj11 = Db::query("select sum(parent11commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent11=".mid."");
		$commissionyj12 = Db::query("select sum(parent12commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent12=".mid."");
		$commissionyj13 = Db::query("select sum(parent13commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent13=".mid."");
		$commissionyj14 = Db::query("select sum(parent14commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent14=".mid."");
		$commissionyj15 = Db::query("select sum(parent15commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent15=".mid."");
		$commissionyj16 = Db::query("select sum(parent16commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent16=".mid."");
		$commissionyj17 = Db::query("select sum(parent17commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent17=".mid."");
		$commissionyj18 = Db::query("select sum(parent18commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent18=".mid."");
		$commissionyj19 = Db::query("select sum(parent19commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent19=".mid."");
		$commissionyj20 = Db::query("select sum(parent20commission)c from ".table_name('shop_order_goods')." og left join ".table_name('shop_order')." o on o.id=og.orderid where og.aid=".aid." and (o.status=1 or o.status=2) and og.parent20=".mid."");

		$commissionyj = 0 + $commissionyj1[0]['c'] + $commissionyj2[0]['c'] + $commissionyj3[0]['c']+ $commissionyj4[0]['c']+ $commissionyj5[0]['c']+ $commissionyj6[0]['c']+ $commissionyj7[0]['c']+ $commissionyj8[0]['c']+ $commissionyj9[0]['c']+ $commissionyj10[0]['c']+ $commissionyj11[0]['c']+ $commissionyj12[0]['c']+ $commissionyj13[0]['c']+ $commissionyj14[0]['c']+ $commissionyj15[0]['c']+ $commissionyj16[0]['c']+ $commissionyj17[0]['c']+ $commissionyj18[0]['c']+ $commissionyj19[0]['c']+ $commissionyj20[0]['c'];
		
		$rdata = [];
		$rdata['count'] = $count;
		$rdata['count1'] = $count1;
		$rdata['count2'] = $count2;
		$rdata['count3'] = $count3;
		$rdata['commissionyj'] = round($commissionyj,2);
		$rdata['datalist'] = $newdatalist;
		$rdata['st'] = $st;
		return $this->json($rdata);
	}

	//股东分红
	public function fenhong(){
		$pernum = 20;
		$pagenum = input('param.pagenum') ? input('param.pagenum') :1;
		$where = '';
		$st = input('param.st/d');
		if(!$st) $st = 1;

		//待结算
		$newoglist = [];
		if($st == 1){
			$rs = \app\common\Fenhong::gdfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$newoglist = $rs['oglist'];
			$commissionyj = $rs['commissionyj'];
		}

		if($st==2){//已结算
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['mid','=',mid];
			$where[] = ['type','=','fenhong'];
			$pernum = 20;
			$pagenum = input('param.pagenum');
			if(!$pagenum) $pagenum = 1;
			$count = Db::name('member_fenhonglog')->where($where)->count();
			$datalist = Db::name('member_fenhonglog')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
			if(!$datalist) $datalist = [];
			foreach($datalist as $k=>$v){
				if($v['ogids']){
					if($v['module'] == 'yuyue'){
						$oglist = Db::name('yuyue_order')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.proname name,og.propic pic,og.num,og.totalprice real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}else{
						$oglist = Db::name('shop_order_goods')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.name,og.pic,og.num,og.real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}
				}else{
					$oglist = [];
				}
				$datalist[$k]['oglist'] = $oglist;
			}
		}
		$rdata = [];
		$rdata['count'] = $count + count($newoglist);
		$rdata['commissionyj'] = round($commissionyj,2);
		if($st == 1){
			$rdata['datalist'] = $newoglist;
		}else{
			$rdata['datalist'] = $datalist;
		}
		$rdata['st'] = $st;
		return $this->json($rdata);
	}
	//团队分红
	public function teamfenhong(){
		$pernum = 20;
		$pagenum = input('param.pagenum') ? input('param.pagenum') :1;
		$where = '';
		$st = input('param.st/d');
		if(!$st) $st = 1;

		//待结算
		$newoglist = [];
		if($st == 1){
			$rs1 = \app\common\Fenhong::teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs2 = \app\common\Fenhong::product_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs3 = \app\common\Fenhong::level_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);

			$newoglist = [];
			$commissionyj = 0;
			if($rs1 && $rs1['oglist']){
				$newoglist = array_merge($newoglist,$rs1['oglist']);
				$commissionyj += $rs1['commissionyj'];
			}
			if($rs2 && $rs2['oglist']){
				$newoglist = array_merge($newoglist,$rs2['oglist']);
				$commissionyj += $rs2['commissionyj'];
			}
			if($rs3 && $rs3['oglist']){
				$newoglist = array_merge($newoglist,$rs3['oglist']);
				$commissionyj += $rs3['commissionyj'];
			}
		}

		if($st==2){//已结算
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['mid','=',mid];
			$where[] = ['type','=','teamfenhong'];
			$pernum = 20;
			$pagenum = input('param.pagenum');
			if(!$pagenum) $pagenum = 1;
			$count = Db::name('member_fenhonglog')->where($where)->count();
			$datalist = Db::name('member_fenhonglog')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
			if(!$datalist) $datalist = [];
			foreach($datalist as $k=>$v){
				if($v['ogids']){
					if($v['module'] == 'yuyue'){
						$oglist = Db::name('yuyue_order')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.proname name,og.propic pic,og.num,og.totalprice real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}else{
						$oglist = Db::name('shop_order_goods')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.name,og.pic,og.num,og.real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}
				}else{
					$oglist = [];
				}
				$datalist[$k]['oglist'] = $oglist;
			}
		}
		$rdata = [];
		$rdata['count'] = $count + count($newoglist);
		$rdata['commissionyj'] = round($commissionyj,2);
		if($st == 1){
			$rdata['datalist'] = $newoglist;
		}else{
			$rdata['datalist'] = $datalist;
		}
		$rdata['st'] = $st;
		return $this->json($rdata);
	}
	//区域代理分红
	public function areafenhong(){
		$pernum = 20;
		$pagenum = input('param.pagenum') ? input('param.pagenum') :1;
		$where = '';
		$st = input('param.st/d');
		if(!$st) $st = 1;

		//待结算
		$newoglist = [];
		if($st == 1){
			$rs = \app\common\Fenhong::areafenhong(aid,$this->sysset,[],0,time(),1,mid);
			$newoglist = $rs['oglist'];
			$commissionyj = $rs['commissionyj'];
		}

		if($st==2){//已结算
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['mid','=',mid];
			$where[] = ['type','=','areafenhong'];
			$pernum = 20;
			$pagenum = input('param.pagenum');
			if(!$pagenum) $pagenum = 1;
			$count = Db::name('member_fenhonglog')->where($where)->count();
			$datalist = Db::name('member_fenhonglog')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
			if(!$datalist) $datalist = [];
			foreach($datalist as $k=>$v){
				if($v['ogids']){
					if($v['module'] == 'yuyue'){
						$oglist = Db::name('yuyue_order')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.proname name,og.propic pic,og.num,og.totalprice real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}else{
						$oglist = Db::name('shop_order_goods')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.name,og.pic,og.num,og.real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}
				}else{
					$oglist = [];
				}
				$datalist[$k]['oglist'] = $oglist;
			}
		}
		$rdata = [];
		$rdata['count'] = $count + count($newoglist);
		$rdata['commissionyj'] = round($commissionyj,2);
		if($st == 1){
			$rdata['datalist'] = $newoglist;
		}else{
			$rdata['datalist'] = $datalist;
		}
		$rdata['st'] = $st;
		return $this->json($rdata);
	}

	//分红订单
	public function fhorder(){
		$pernum = 20;
		$pagenum = input('param.pagenum') ? input('param.pagenum') :1;
		$where = '';
		$st = input('param.st/d');
		if(!$st) $st = 1;

		$fhjiesuanbusiness = $this->sysset['fhjiesuanbusiness'];
        $fhjiesuantype = $this->sysset['fhjiesuantype'];
		$fhjiesuantime_type = $this->sysset['fhjiesuantime_type'];

		//待结算
		$newoglist = [];
		if($st == 1){
			$rs1 = \app\common\Fenhong::gdfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs2 = \app\common\Fenhong::teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs3 = \app\common\Fenhong::product_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs4 = \app\common\Fenhong::level_teamfenhong(aid,$this->sysset,[],0,time(),1,mid);
			$rs5 = \app\common\Fenhong::areafenhong(aid,$this->sysset,[],0,time(),1,mid);

			$newoglist = [];
			$commissionyj = 0;
			if($rs1 && $rs1['oglist']){
				$newoglist = array_merge($newoglist,$rs1['oglist']);
				$commissionyj += $rs1['commissionyj'];
			}
			if($rs2 && $rs2['oglist']){
				$newoglist = array_merge($newoglist,$rs2['oglist']);
				$commissionyj += $rs2['commissionyj'];
			}
			if($rs3 && $rs3['oglist']){
				$newoglist = array_merge($newoglist,$rs3['oglist']);
				$commissionyj += $rs3['commissionyj'];
			}
			if($rs4 && $rs4['oglist']){
				$newoglist = array_merge($newoglist,$rs4['oglist']);
				$commissionyj += $rs4['commissionyj'];
			}
			if($rs5 && $rs5['oglist']){
				$newoglist = array_merge($newoglist,$rs5['oglist']);
				$commissionyj += $rs5['commissionyj'];
			}
		}

		if($st==2){//已结算
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['mid','=',mid];
			$pernum = 20;
			$pagenum = input('param.pagenum');
			if(!$pagenum) $pagenum = 1;
			$count = Db::name('member_fenhonglog')->where($where)->count();
			$datalist = Db::name('member_fenhonglog')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
			if(!$datalist) $datalist = [];
			foreach($datalist as $k=>$v){
				if($v['ogids']){
					if($v['module'] == 'yuyue'){
						$oglist = Db::name('yuyue_order')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.proname name,og.propic pic,og.num,og.totalprice real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}else{
						$oglist = Db::name('shop_order_goods')->alias('og')->join('member m','og.mid=m.id')->field('og.ordernum,og.name,og.pic,og.num,og.real_totalprice,og.createtime,og.status,m.nickname,m.headimg')->where('og.id','in',$v['ogids'])->select()->toArray();
					}
				}else{
					$oglist = [];
				}
				$datalist[$k]['oglist'] = $oglist;
			}
		}
		$rdata = [];
		$rdata['count'] = $count + count($newoglist);
		$rdata['commissionyj'] = round($commissionyj,2);
		if($st == 1){
			$rdata['datalist'] = $newoglist;
		}else{
			$rdata['datalist'] = $datalist;
		}
		$rdata['st'] = $st;
		return $this->json($rdata);
	}


    //门店服务订单
    public function orderMendian(){
        return $this->json([]);
    }

	//分红记录
	public function fhlog(){
		$st = input('param.st');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['mid','=',mid];
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		$datalist = Db::name('member_fenhonglog')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist]);
	}
	//分享海报
	public function poster(){
		$member = $this->member;
		$platform = platform;
		$page = $this->indexurl;
		$scene = 'pid_'.$this->member['id'];
		//if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
		//	$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
		//}

		if(input('param.posterid')){
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','index')->where('platform',$platform)->where('id',input('param.posterid'))->find();
		}else{
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','index')->where('platform',$platform)->order('id')->find();
		}
		$posterdata = Db::name('member_poster')->where('aid',aid)->where(['mid'=>mid,'posterid'=>$posterset['id'],'qu'=>0])->find();
		$sysset = Db::name('admin_set')->field('id,name,logo,desc')->where('aid',aid)->find();
		if(!$posterdata){
			$textReplaceArr = [
				'[头像]'=>$member['headimg'],
				'[昵称]'=>$member['nickname'],
				'[姓名]'=>$member['realname'],
				'[手机号]'=>$member['tel'],
				'[商城名称]'=>$sysset['name'],
			];
			$poster = $this->_getposter(aid,$platform,$posterset['content'],$page,$scene,$textReplaceArr);
			$posterdata = [];
			$posterdata['aid'] = aid;
			$posterdata['mid'] = $member['id'];
			$posterdata['scene'] = $scene;
			$posterdata['page'] = $page;
			$posterdata['poster'] = $poster;
			$posterdata['posterid'] = $posterset['id'];
			$posterdata['createtime'] = time();
			Db::name('member_poster')->insert($posterdata);
		}

		$posterlist = Db::name('admin_set_poster')->field('id')->where('aid',aid)->where('type','index')->where('platform',$platform)->order('id')->select()->toArray();

		$rdata = [];
		$rdata['poster'] = $posterdata['poster'];
		$rdata['guize'] = $posterset['guize'];
		$rdata['posterid'] = $posterset['id'];
		$rdata['posterlist'] = $posterlist;
		$rdata['postercount'] = count($posterlist);
		return $this->json($rdata);
	}
	//分享等级海报
	public function posterdengji(){
	    $member = $this->member;
		$platform = platform;
		$page = $this->indexurl;
		$scene = 'pid_'.$this->member['id'].'-xlevel_';
		$posterid = input('param.posterid');
// 		var_dump($posterid);die;
		$memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$member['levelid'])->find();
		if($memberlevel['fxlianjie'] == 0)
		{
		    return $this->json(['status'=>0,'msg'=>'您的用户等级暂未开启等级海报']);
		}
		
		if($memberlevel['fxdengji'] != 0)
		{
		    $scene = 'pid_'.$this->member['id'].'-xlevel_'.$memberlevel['fxdengji'];
		    $memberlevel2 = Db::name('member_level')->where('aid',aid)->where('id',$memberlevel['fxdengji'])->find();
		    $levelname = $memberlevel2['name'];
		}else{
		    $memberlevel2 = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
		    $scene = 'pid_'.$this->member['id'].'-xlevel_'.$memberlevel2['id'];
		    $levelname = $memberlevel2['name'];
		}
// 		if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
// 			$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
// 		}
// var_dump($platform);die;
		if($posterid){
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','index')->where('platform',$platform)->where('id',$posterid)->find();
		}else{
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','index')->where('platform',$platform)->order('id')->find();
		}
// 		$posterdata = Db::name('member_poster')->where('aid',aid)->where(['mid'=>mid,'posterid'=>$posterset['id'],'qu'=>1])->find();
$posterdata=[];
		$sysset = Db::name('admin_set')->field('id,name,logo,desc')->where('aid',aid)->find();
		if(!$posterdata){
			$textReplaceArr = [
				'[头像]'=>$member['headimg'],
				'[昵称]'=>$member['nickname'],
				'[姓名]'=>$member['realname'],
				'[手机号]'=>$member['tel'],
				'[注册成为等级]'=>$levelname,
				'[商城名称]'=>$sysset['name'],
			];
			$poster = $this->_getposter(aid,$platform,$posterset['content'],$page,$scene,$textReplaceArr);
			$posterdata = [];
			$posterdata['aid'] = aid;
			$posterdata['mid'] = $member['id'];
			$posterdata['scene'] = $scene;
			$posterdata['page'] = $page;
			$posterdata['poster'] = $poster;
			$posterdata['posterid'] = $posterset['id'];
			$posterdata['qu'] =1;
			$posterdata['createtime'] = time();
			Db::name('member_poster')->insert($posterdata);
		}

		$posterlist = Db::name('admin_set_poster')->field('id')->where('aid',aid)->where('type','index')->where('platform',$platform)->order('id')->select()->toArray();

		$rdata = [];
		$rdata['status'] = 1;
		$rdata['poster'] = $posterdata['poster'];
		$rdata['guize'] = $posterset['guize'];
		$rdata['posterid'] = $posterset['id'];
		$rdata['posterlist'] = $posterlist;
		$rdata['postercount'] = count($posterlist);
		return $this->json($rdata);
	}
	
	//商家分享海报
	public function businessposter(){
		$business_id = input('param.business_id/d');
		if(!$business_id){
			return $this->json(['status'=>0,'msg'=>'请选择商家']);
		}
		
		$platform = platform;
		$page = 'pagesExt/business/detail?id='.$business_id;
		$scene = 'bid_'.$business_id;

		if(input('param.posterid')){
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','business')->where('platform',$platform)->where('id',input('param.posterid'))->find();
		}else{
			$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','business')->where('platform',$platform)->order('id')->find();
		}
		
		if(!$posterset){
			return $this->json(['status'=>0,'msg'=>'商家海报模板未设置']);
		}
		
		$posterdata = Db::name('business_poster')->where('aid',aid)->where(['bid'=>$business_id,'posterid'=>$posterset['id']])->find();
		$business = Db::name('business')->where('aid',aid)->where('id',$business_id)->find();
		
		if(!$business){
			return $this->json(['status'=>0,'msg'=>'商家不存在']);
		}
		
		if(!$posterdata){
			$textReplaceArr = [
				'[商家名称]'=>$business['name'],
				'[商家描述]'=>$business['desc'],
				'[商家地址]'=>$business['address'],
				'[联系电话]'=>$business['linktel'] ?: $business['tel'],
			];
			$poster = $this->_getposter(aid,$platform,$posterset['content'],$page,$scene,$textReplaceArr);
			$posterdata = [];
			$posterdata['aid'] = aid;
			$posterdata['bid'] = $business_id;
			$posterdata['scene'] = $scene;
			$posterdata['page'] = $page;
			$posterdata['poster'] = $poster;
			$posterdata['posterid'] = $posterset['id'];
			$posterdata['createtime'] = time();
			Db::name('business_poster')->insert($posterdata);
		}

		$posterlist = Db::name('admin_set_poster')->field('id')->where('aid',aid)->where('type','business')->where('platform',$platform)->order('id')->select()->toArray();

		$rdata = [];
		$rdata['poster'] = $posterdata['poster'];
		$rdata['guize'] = $posterset['guize'];
		$rdata['posterid'] = $posterset['id'];
		$rdata['posterlist'] = $posterlist;
		$rdata['postercount'] = count($posterlist);
		return $this->json($rdata);
	}
	
	//给余额
	public function givemoney(){
		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
		if($userlevel['team_givemoney'] == 0) return $this->json(['status'=>0,'msg'=>'给下级转账功能未开启']);
		$id = input('param.id/d');
		$money = input('param.money/f');
		if($money <= 0) return $this->json(['status'=>0,'msg'=>'转账金额必须大于0']);
		$tomember = Db::name('member')->where('aid',aid)->where('id',$id)->find();
		if(!$tomember) return $this->json(['status'=>0,'msg'=>'用户不存在']);
		if($money > $this->member['money']) return $this->json(['status'=>0,'msg'=>t('余额').'不足']);
		\app\common\Member::addmoney(aid,$id,$money,$this->member['nickname'].'-转账');
		\app\common\Member::addmoney(aid,mid,-$money,'转账给'.$tomember['nickname']);
		return $this->json(['status'=>1,'msg'=>'转账成功']);
	}
	//给积分
	public function givescore(){
		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
		if($userlevel['team_givescore'] == 0) return $this->json(['status'=>0,'msg'=>'给下级转账功能未开启']);
		$id = input('param.id/d');
		$score = input('param.score/d');
		if($score <= 0) return $this->json(['status'=>0,'msg'=>'转账数量必须大于0']);
		$tomember = Db::name('member')->where('aid',aid)->where('id',$id)->find();
		if(!$tomember) return $this->json(['status'=>0,'msg'=>'用户不存在']);
		if($score > $this->member['score']) return $this->json(['status'=>0,'msg'=>t('积分').'不足']);
		\app\common\Member::addscore(aid,$id,$score,$this->member['nickname'].'-转账');
		\app\common\Member::addscore(aid,mid,-$score,'转账给'.$tomember['nickname']);
		return $this->json(['status'=>1,'msg'=>'转账成功']);
	}

	//升级（分销商给下级升级
	public function levelUp()
    {
        if(!getcustom('plug_zhiming')) {
            return $this->json(['status'=>0,'msg'=>'操作失败']);
        }
        $mid = input('param.mid/d');
        $levelId = input('param.levelId/d');

        $tomember = Db::name('member')->where('aid',aid)->where('id',$mid)->find();
        if(!$tomember) return $this->json(['status'=>0,'msg'=>'用户不存在']);

        // 检查 path 是否为空，避免 explode() 报错
        $member_path = $tomember['path'] ?? '';
        if (empty($member_path) || !in_array(mid, explode(',', $member_path))) {
            return $this->json(['status'=>0,'msg'=>'无权限操作此用户']);
        }

        $memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$tomember['levelid'])->find();
        $tolevel = Db::name('member_level')->where('aid',aid)->where('id',$levelId)->find();
        if($tolevel['sort'] <= $memberlevel['sort']) {
            return $this->json(['status'=>0,'msg'=>'只能为用户升级']);
        }

        $userlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
        if($userlevel['sort'] <= $tolevel['sort']) return $this->json(['status'=>0,'msg'=>'给下级升级需低于推广员当前等级']);
        if($userlevel['team_levelup'] == 0) return $this->json(['status'=>0,'msg'=>'给下级升级功能未开启']);
        if($userlevel['team_levelup_num'] <= 0) return $this->json(['status'=>0,'msg'=>'给下级升级数量不足']);

        $count = Db::name('member_levelup_order')->where('aid', aid)->where('from_mid', mid)->count();
        if($count >= $userlevel['team_levelup_num']) {
            return $this->json(['status'=>0,'msg'=>'给下级升级数量不足']);
        }

        $order = [
            'aid' => aid,
            'mid' => $mid,
            'from_mid' => mid,
            'levelid' => $levelId,
            'title' => '推广员为下级升级，推广员：'.$this->member['nickname'],
            'totalprice' => 0,
            'createtime' => time(),
            'beforelevelid' => $tomember['levelid'],
            'form0' => '类型^_^推广员为下级升级',
            'form1' => '推广员^_^'.$this->member['nickname'],
            'platform' => platform,
            'status' => 2
        ];
        Db::name('member_levelup_order')->insert($order);

        $newlv = Db::name('member_level')->where('aid',aid)->where('id',$levelId)->find();
        if($newlv['yxqdate'] > 0){
            $levelendtime = strtotime(date('Y-m-d')) + 86400 + 86400 * $newlv['yxqdate'];
        }else{
            $levelendtime = 0;
        }
        Db::name('member')->where('id',$mid)->update(['levelid'=>$newlv['id'],'levelstarttime'=>time(),'levelendtime'=>$levelendtime]);

        \app\common\Wechat::updatemembercard(aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '恭喜您成功升级为'.$newlv['name'];
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $newlv['name']; //会员等级
        $tmplcontent['keyword2'] = '已生效';//审核状态
        \app\common\Wechat::sendtmpl(aid,$mid,'tmpl_uplv',$tmplcontent,m_url('pages/my/usercenter'));

        return $this->json(['status'=>1,'msg'=>'升级成功']);
    }


    //佣金明细
    public function orderYeji(){
        $st = input('param.st');
        $level = Db::name('member_level')->where('id', $this->member['levelid'])->find();
        if(empty($level['tongji_yeji']) || $level['tongji_yeji'] != 1) {
            $datalist = [];
            return $this->json(['status'=>1,'data'=>$datalist]);
        }
        $pernum = 15;
        $pagenum = input('post.pagenum');
        if(!$pagenum) $pagenum = 1;
        $childrenmids = \app\common\Member::getdownmids(aid,mid);
        $childrenmids[] = mid;
        $date = strtotime(date('Y-m-d'));

        for ($i=($pagenum-1)*$pernum;$i<$pagenum*$pernum;$i++) {
            $start = $date-$i*86400;
            $end = $start + 86399;
            $where = [];
            $where[] = ['aid','=',aid];
            $where[] = ['mid','in',$childrenmids];
            $where[] = ['status','in',[1,2,3]];
            $where[] = ['createtime','between',[$start,$end]];
            $num = Db::name('shop_order_goods')->where($where)->sum('num');
            $datalist[] = [
                'i'=>$i,
                'date' => date('Y-m-d',$start),
                'num' => $num
            ];
        }

        if(!$datalist) $datalist = [];
        return $this->json(['status'=>1,'data'=>$datalist]);
    }


	//我的上下级
	public function teamline(){
		
		$userinfo = Db::name('member')->field('id,nickname,headimg,levelid,path')->where('aid',aid)->where('id',mid)->find();
		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$userinfo['levelid'])->find();

		$pernum = 20;
		$pagenum = input('post.pagenum');
		$keyword = input('post.keyword');
		$where2 = "1=1";
		if($keyword) $where2 = "(nickname like '%{$keyword}%' or realname like '%{$keyword}%' or tel like '%{$keyword}%')";
		if(!$pagenum) $pagenum = 1;
		if(!mid){
			$datalist = [];
		}else{
			$datalist = Db::name('member')->where('aid',aid)->where('find_in_set('.mid.',path)'.($userinfo['path'] ? ' or id in ('.$userinfo['path'].')' : ''))->where($where2)->order('id')->page($pagenum,$pernum)->select()->toArray();
			//echo Db::getlastsql();
		}

		foreach($datalist as $k=>$v){
			$level = Db::name('member_level')->where('aid',aid)->where('id',$v['levelid'])->find();
            $datalist[$k]['levelname'] = $level['name'];
            $datalist[$k]['levelsort'] = $level['sort'];
			if($userlevel['team_showtel'] == 0){
				$datalist[$k]['tel'] = '';
			}
			
			// 查询是否有原推荐人记录
			$tuoliLog = Db::name('liandong_tuoli_log')->where('aid', aid)->where('mid', $v['id'])->where('type', 0)->order('id desc')->find();
			if ($tuoliLog && $tuoliLog['ytpid'] > 0) {
				// 获取原推荐人信息
				$originalRecommender = Db::name('member')->field('id,nickname,headimg,tel')->where('aid', aid)->where('id', $tuoliLog['ytpid'])->find();
				if ($originalRecommender) {
					$datalist[$k]['has_original_recommender'] = 1;
					$datalist[$k]['original_recommender'] = $originalRecommender;
				} else {
					$datalist[$k]['has_original_recommender'] = 0;
				}
			} else {
				$datalist[$k]['has_original_recommender'] = 0;
			}
		}
		
		$rdata = [];
		$rdata['datalist'] = $datalist;
		$rdata['userinfo'] = $userinfo;
		$rdata['userlevel'] = $userlevel;
		$rdata['st'] = $downdeep;
		return $this->json($rdata);
	}
	//我的同等级会员
	public function sameline(){
		$userinfo = Db::name('member')->field('id,nickname,headimg,levelid,path')->where('aid',aid)->where('id',mid)->find();
		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$userinfo['levelid'])->find();

		$pernum = 20;
		$pagenum = input('post.pagenum');
		$keyword = input('post.keyword');
		$where2 = "1=1";
		if($keyword) $where2 = "(nickname like '%{$keyword}%' or realname like '%{$keyword}%' or tel like '%{$keyword}%')";
		if(!$pagenum) $pagenum = 1;
		if(!mid){
			$datalist = [];
		}else{
			$datalist = Db::name('member')->where('aid',aid)->where('levelid',$userinfo['levelid'])->where($where2)->order('id')->page($pagenum,$pernum)->select()->toArray();
			//echo Db::getlastsql();
		}

		foreach($datalist as $k=>$v){
			$level = Db::name('member_level')->where('aid',aid)->where('id',$v['levelid'])->find();
            $datalist[$k]['levelname'] = $level['name'];
            $datalist[$k]['levelsort'] = $level['sort'];
			if($userlevel['team_showtel'] == 0){
				$datalist[$k]['tel'] = '';
			}
			
			// 查询是否有原推荐人记录
			$tuoliLog = Db::name('liandong_tuoli_log')->where('aid', aid)->where('mid', $v['id'])->where('type', 0)->order('id desc')->find();
			if ($tuoliLog && $tuoliLog['ytpid'] > 0) {
				// 获取原推荐人信息
				$originalRecommender = Db::name('member')->field('id,nickname,headimg,tel')->where('aid', aid)->where('id', $tuoliLog['ytpid'])->find();
				if ($originalRecommender) {
					$datalist[$k]['has_original_recommender'] = 1;
					$datalist[$k]['original_recommender'] = $originalRecommender;
				} else {
					$datalist[$k]['has_original_recommender'] = 0;
				}
			} else {
				$datalist[$k]['has_original_recommender'] = 0;
			}
		}
		
		$rdata = [];
		$rdata['datalist'] = $datalist;
		$rdata['userinfo'] = $userinfo;
		$rdata['userlevel'] = $userlevel;
		$rdata['st'] = $downdeep;
		return $this->json($rdata);
	}

	//佣金排行
	public function commissionrank(){
		$pernum = 20;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;

		$where = [];
		$where[] = ['aid','=',aid];
		$datalist = Db::name('member')->where($where)->page($pagenum,$pernum)->order('totalcommission desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		return $this->json(['status'=>1,'data'=>$datalist,'totalcommission'=>$this->member['totalcommission'],'commission'=>$this->member['commission']]);
    }
	/**
	 * 获取团队关系图数据
	 * 接收前端传递的 mid 参数
	 */
	public function getTeamRelationChart(){
		// 从请求参数获取要查看的成员ID，如果未提供，则默认为当前登录用户
		$rootMid = input('param.mid/d', mid);

        // 验证要查看的 rootMid 是否是当前用户或其下级 (可选的安全检查)
        $currentUserPath = Db::name('member')->where('id', mid)->value('path');
        $targetUser = Db::name('member')->where('id', $rootMid)->find();
        if (!$targetUser || ($rootMid != mid && !str_contains($targetUser['path'], ',' . mid))) {
           // return $this->json(['status' => 0, 'msg' => '无权查看该成员的关系图']);
            // 或者根据需求决定是否允许查看任何人的，这里暂时注释掉权限检查
        }


		// 1. 获取所有下级成员 (包括间接下级)
		$allMembers = Db::name('member')
			->where('aid', aid)
			->whereRaw("find_in_set(?, path) OR id = ?", [$rootMid, $rootMid]) // 获取所有下级及指定根成员
			->field('id, pid, nickname, headimg, levelid')
			->select()
			->toArray();

		if (empty($allMembers)) {
			return $this->json(['status' => 1, 'data' => null, 'msg' => '该成员没有团队成员']);
		}

		// 获取所有涉及的等级信息
		$levelIds = array_unique(array_column($allMembers, 'levelid'));
		$levels = Db::name('member_level')
			->where('aid', aid)
			->whereIn('id', $levelIds)
			->column('name', 'id');

		// 2. 将成员列表构建成树状结构
		$memberMap = [];
		foreach ($allMembers as $member) {
			$member['levelName'] = $levels[$member['levelid']] ?? '未知等级';
			$memberMap[$member['id']] = $member;
		}

		$tree = $this->buildTree($memberMap, $rootMid);

		return $this->json(['status' => 1, 'data' => $tree]);
	}



	/**
	 * 辅助函数：构建树状结构
	 * @param array $memberMap ID到成员信息的映射
	 * @param int $rootId 根节点ID
	 * @return array|null
	 */
	private function buildTree(array &$memberMap, $rootId) {
		$tree = null;
		// 确保根节点存在于映射中
		if (!isset($memberMap[$rootId])) {
			return null;
		}

        $tree = $memberMap[$rootId];
        $tree['children'] = []; // 初始化children数组

        // 遍历映射表构建子树
        foreach ($memberMap as $id => $member) {
            // 检查当前成员是否是根节点的直接子节点
            if ($member['pid'] == $rootId && $id != $rootId) { // 避免将根节点自身作为子节点加入
                $childNode = $this->buildTree($memberMap, $id); // 递归构建子树
                if ($childNode) {
                    $tree['children'][] = $childNode; // 添加构建好的子树到children数组
                }
            }
        }

        // 如果没有子节点，可以根据需要移除children键或保留空数组
        if (empty($tree['children'])) {
             unset($tree['children']);
             //$tree['children'] = []; // 或者保留空数组，取决于前端处理方式
        }

		return $tree;
	}

}
