-- 扣子(Coze) API集成系统 - 基础版数据库表
-- 只包含核心必需字段，确保最大兼容性

-- 1. 扣子API配置表（基础版）
CREATE TABLE `ddwx_coze_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL DEFAULT '默认配置' COMMENT '配置名称',
  `api_key` varchar(255) NOT NULL COMMENT 'Coze API密钥',
  `bot_id` varchar(100) DEFAULT NULL COMMENT '默认机器人ID',
  `base_url` varchar(255) DEFAULT 'https://api.coze.cn' COMMENT 'API基础URL',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子API配置表';

-- 2. 扣子API请求日志表
CREATE TABLE `ddwx_coze_api_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `method` varchar(10) NOT NULL DEFAULT 'POST' COMMENT '请求方法',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0=失败,1=成功)',
  `error_msg` text COMMENT '错误信息',
  `request_time` datetime NOT NULL COMMENT '请求时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `duration` int(11) DEFAULT '0' COMMENT '耗时(毫秒)',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_status` (`status`),
  KEY `idx_request_time` (`request_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子API请求日志表';

-- 3. 扣子对话记录表
CREATE TABLE `ddwx_coze_conversation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `conversation_id` varchar(100) NOT NULL COMMENT '对话ID',
  `bot_id` varchar(100) NOT NULL COMMENT '机器人ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=已结束,1=进行中)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_bot_id` (`bot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子对话记录表';

-- 4. 扣子消息记录表
CREATE TABLE `ddwx_coze_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `conversation_id` varchar(100) NOT NULL COMMENT '对话ID',
  `message_id` varchar(100) DEFAULT NULL COMMENT '消息ID',
  `role` varchar(20) NOT NULL COMMENT '角色(user/assistant)',
  `content` text NOT NULL COMMENT '消息内容',
  `message_type` varchar(20) DEFAULT 'text' COMMENT '消息类型',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子消息记录表';
