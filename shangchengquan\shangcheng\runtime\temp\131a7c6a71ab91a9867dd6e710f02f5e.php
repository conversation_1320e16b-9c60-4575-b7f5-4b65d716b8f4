<?php /*a:3:{s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\edit.html";i:1753869681;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php if($type=='workflow'): if(!$info['id']): ?>添加工作流<?php else: ?>编辑工作流<?php endif; else: if(!$info['id']): ?>添加配置<?php else: ?>编辑配置<?php endif; ?><?php endif; ?></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<?php if($type=='workflow'): if(!$info['id']): ?><i class="fa fa-plus"></i> 添加工作流<?php else: ?><i class="fa fa-pencil"></i> 编辑工作流<?php endif; else: if(!$info['id']): ?><i class="fa fa-plus"></i> 添加配置<?php else: ?><i class="fa fa-pencil"></i> 编辑配置<?php endif; ?>
					<?php endif; ?>
					<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
				</div>
				<div class="layui-card-body" pad15>
					<div class="layui-form">
						<?php if($type=='workflow'): ?>
						<!-- 工作流表单 -->
						<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
						<input type="hidden" name="type" value="workflow"/>
						<div class="layui-form-item">
							<label class="layui-form-label">工作流名称：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[name]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['name']; ?>" placeholder="请输入工作流名称">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">工作流ID：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[workflow_id]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['workflow_id']; ?>" placeholder="请输入Coze工作流ID">
							</div>
							<div class="layui-form-mid layui-word-aux">从Coze平台获取的工作流ID</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">描述：</label>
							<div class="layui-input-inline" style="width:400px">
								<textarea name="info[description]" class="layui-textarea" placeholder="请输入工作流描述"><?php echo $info['description']; ?></textarea>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">状态：</label>
							<div class="layui-input-block">
								<input type="radio" name="info[status]" value="1" title="启用" <?php if($info['status']==1 || !$info['id']): ?>checked<?php endif; ?>>
								<input type="radio" name="info[status]" value="0" title="禁用" <?php if($info['status']==0 && $info['id']): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php else: ?>
						<!-- 配置表单 -->
						<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
						<input type="hidden" name="type" value="config"/>
						<div class="layui-form-item">
							<label class="layui-form-label">API密钥：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[api_key]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['api_key']; ?>" placeholder="请输入Coze API密钥">
							</div>
							<div class="layui-form-mid layui-word-aux">从Coze平台获取的API密钥</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">API地址：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[base_url]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo (isset($info['base_url']) && ($info['base_url'] !== '')?$info['base_url']:'https://api.coze.cn'); ?>" placeholder="请输入API地址">
							</div>
							<div class="layui-form-mid layui-word-aux">默认：https://api.coze.cn</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">状态：</label>
							<div class="layui-input-block">
								<input type="radio" name="info[status]" value="1" title="启用" <?php if($info['status']==1 || !$info['id']): ?>checked<?php endif; ?>>
								<input type="radio" name="info[status]" value="0" title="禁用" <?php if($info['status']==0 && $info['id']): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; ?>
						<div class="layui-form-item">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
								<button type="reset" class="layui-btn layui-btn-primary">重置</button>
							</div>
						</div>
					</div>
				</div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  <script>
  layui.use(['form'], function(){
      var $ = layui.$
      ,form = layui.form;

      //监听提交
      form.on('submit(formSubmit)', function(data){
          var field = data.field;
          var index = layer.load();
          $.post("<?php echo url('save'); ?>", field, function(data){
              layer.close(index);
              if(data.status==1){
                  layer.msg(data.msg, {icon: 1});
                  setTimeout(function(){
                      parent.layer.close(parent.layer.getFrameIndex(window.name));
                      parent.tableIns.reload();
                  }, 1000);
              }else{
                  layer.msg(data.msg, {icon: 2});
              }
          });
          return false;
      });
  });
  </script>
</body>
</html>