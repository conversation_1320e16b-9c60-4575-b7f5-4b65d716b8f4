<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="76a7c043-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;">{{''+item.nickname+" "+item.showtime+'购买了该商品'}}</view></swiper-item></block></swiper></view></block><block wx:if="{{showtoptabbar==1&&toptabbar_show==1}}"><view class="toptabbar_tab"><view class="{{['item',toptabbar_index==0?'on':'']}}" style="{{'color:'+(toptabbar_index==0?$root.m2:'#333')+';'}}" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">商品<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view><view class="{{['item',toptabbar_index==1?'on':'']}}" style="{{'color:'+(toptabbar_index==1?$root.m4:'#333')+';'}}" data-index="1" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><view class="{{['item',toptabbar_index==2?'on':'']}}" style="{{'color:'+(toptabbar_index==2?$root.m6:'#333')+';'}}" data-index="2" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">详情<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view><block wx:if="{{$root.g1>0}}"><view class="{{['item',toptabbar_index==3?'on':'']}}" style="{{'color:'+(toptabbar_index==3?$root.m8:'#333')+';'}}" data-index="3" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">推荐<view class="after" style="{{'background:'+($root.m9)+';'}}"></view></view></block></view></block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab0"><block wx:if="{{isGameOpen}}"><view class="game"><block wx:for="{{gameList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="keyName"><view data-event-opts="{{[['tap',[['onClickGame',['$0'],[[['gameList','keyName',item.keyName]]]]]]]}}" class="game-grid" bindtap="__e"><image class="game-img" src="{{item.imgUrl}}" mode="widthFix"></image></view></block></view></block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix" data-url="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></swiper-item></block></block></swiper><block wx:if="{{product.diypics}}"><view class="imageCount" style="bottom:92rpx;width:140rpx;" data-url="{{'/pagesExt/shop/diylight?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">自助试灯</view></block><view class="imageCount">{{current+1+"/"+$root.g2}}</view><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="/static/img/video.png"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><block wx:if="{{isplay==1}}"><view class="videobox"><video class="video" autoplay="true" id="video" src="{{product.video}}"></video><view data-event-opts="{{[['tap',[['parsevideo',['$event']]]]]}}" class="parsevideo" bindtap="__e">退出播放</view></view></block><block wx:if="{{$root.g3}}"><view class="cuxiaopoint cuxiaoitem" style="background:#fff;padding:0 16rpx;"><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m10+',0.1)')+';'+('color:'+(item.m11)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{shopset.detail_guangao1}}"><view style="background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0;"><block wx:if="{{shopset.detail_guangao1}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao1}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg1Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao1&&shopset.detail_guangao1_t}}"><uni-popup class="vue-ref" vue-id="76a7c043-2" id="gg1Dialog" type="dialog" data-ref="gg1Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao1_t}}" data-url="{{shopset.detail_guangao1_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg1Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block><view class="header"><block wx:if="{{product.hide_price==1}}"><block><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m12)+';'}}"><text style="font-size:36rpx;">{{product.hide_price_detail_text}}</text></view></view></view><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view></block></block><block wx:else><block wx:if="{{product.price_type!=1||product.min_price>0}}"><block><view class="price_share"><view class="price"><block wx:if="{{product.is_newcustom==1&&product.is_member_yh==0}}"><view class="f1" style="{{'color:'+($root.m13)+';'}}"><block wx:if="{{product.is_newcustom==1}}"><text style="font-size:36rpx;">￥</text></block>{{product.yh_price+''}}</view></block><block wx:else><view class="f1" style="{{'color:'+($root.m14)+';'}}"><text style="font-size:36rpx;">￥</text>{{product.min_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>{{"-"+product.max_price}}</text></block></view></block><block wx:if="{{product.market_price*1>product.sell_price*1}}"><view class="f2">{{'￥'+product.market_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>起</text></block></view></block><block wx:if="{{product.huang_dx.types}}"><view class="huang_bz"><view class="huang_i"></view><view class="huang_nums">{{"可用红包："+product.huang_dx.nums}}</view></view></block></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view><block wx:if="{{product.yuanbao}}"><view class="sales_stock" style="margin:0;font-size:26rpx;margin-bottom:10rpx;"><view class="f2">{{"元宝价："+product.yuanbao}}</view></view></block><view class="title">{{product.name}}</view></block></block><block wx:else><block><block wx:if="{{product.xunjia_text}}"><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m15)+';'}}"><text style="font-size:36rpx;">询价</text></view></view></view></block><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view></block></block></block><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block><block wx:if="{{shopset.showcommission==1&&product.commission>0&&showjiesheng==0}}"><view class="sales_stock"><view class="f1">{{"奖励创业值："+product.chuangyezhi+''}}</view><view class="f2">{{"推广佣金："+product.commission+"元"}}</view></view></block><block wx:if="{{shopset.hide_sales!=1||shopset.hide_stock!=1}}"><view class="sales_stock"><block wx:if="{{shopset.hide_sales!=1}}"><view class="f1">{{"销量："+product.sales+''}}</view></block><block wx:if="{{shopset.hide_stock!=1}}"><view class="f2">{{"库存："+product.stock}}</view></block></view></block><block wx:if="{{shopset.showcommission==1&&product.commission>0&&showjiesheng==0}}"><view class="commission" style="{{'background:'+('rgba('+$root.m16+',0.1)')+';'+('color:'+($root.m17)+';')}}">{{'分享好友购买预计可得'+$root.m18+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.commission}}</text>{{product.commission_desc+''}}</view></block><block wx:if="{{product.balance_price>0}}"><view style="margin:20rpx 0;color:#333;font-size:22rpx;">{{'首付款金额：'+product.advance_price+"元，尾款金额："+product.balance_price+"元"}}</view></block><block wx:if="{{product.buyselect_commission>0}}"><view style="margin:20rpx 0;color:#666;font-size:22rpx;">{{'下单被选奖励预计可得'+$root.m19+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.buyselect_commission}}</text>元</view></block><block wx:if="{{product.upsavemoney>0}}"><view class="upsavemoney" style="{{'background:'+('linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)')+';'+('color:'+('#653a2b')+';')}}"><view class="flex1">{{"升级到 "+product.nextlevelname+" 预计可节省"}}<text style="font-weight:bold;padding:0 2px;color:#ca4312;">{{product.upsavemoney}}</text>元</view><view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312;" data-url="/pagesExa/my/levelup" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即升级<image style="width:30rpx;height:30rpx;" src="/static/img/arrowright2.png"></image></view></view></block></view><view class="choose" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><view class="f0">规格</view><view class="f1 flex1"><block wx:if="{{product.price_type==1}}"><block>查看规格</block></block><block wx:else><block>请选择商品规格及数量</block></block></view><image class="f2" src="/static/img/arrowright.png"></image></view><block wx:if="{{product.xiaofeizhi2>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m20}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m21+product.xiaofeizhi2+" 个"}}</view></view></view></block><block wx:if="{{product.chuangyezhi>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m22}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m23+product.chuangyezhi+" 个"}}</view></view></view></block><block wx:if="{{product.givescore>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m24}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m25+product.givescore+"个"}}</view></view></view></block><block wx:if="{{product.score_deduction_message}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"抵扣"+$root.m26}}</view><view class="f1" style="font-size:26rpx;">{{product.score_deduction_message}}</view></view></view></block><block wx:if="{{$root.g4}}"><view class="cuxiaodiv"><block wx:if="{{$root.g5>0}}"><view class="fuwupoint cuxiaoitem"><view class="f0">服务</view><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t">{{item.name}}</view></block></view><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{$root.g6>0}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">促销</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m27+',0.1)')+';'+('color:'+(item.m28)+';')}}"><text class="t0">{{item.$orig.tip}}</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{product.discount_tips!=''}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">折扣</view><view class="f1" style="padding-left:10rpx;">{{product.discount_tips}}</view><view class="f2" data-url="/pagesExa/my/levelinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{$root.g7}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">优惠</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m29+',0.1)')+';'+('color:'+(item.m30)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block></view></block><block wx:if="{{showfuwudialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">服务</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="fuwudialog-content"><view class="f1">{{item.name}}</view><text class="f2">{{item.desc}}</text></view></view></block></view></view></view></block><block wx:if="{{showcuxiaodialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{cuxiaolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="suffix"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.tip}}</text><text style="color:#333;margin-left:20rpx;">{{item.name}}</text></view></view></view></block><couponlist vue-id="76a7c043-3" couponlist="{{couponlist}}" data-event-opts="{{[['^getcoupon',[['getcoupon']]]]}}" bind:getcoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{shopset.detail_guangao2}}"><view style="width:100%;height:auto;padding:20rpx 0 0;"><block wx:if="{{shopset.detail_guangao2}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao2}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg2Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao2&&shopset.detail_guangao2_t}}"><uni-popup class="vue-ref" vue-id="76a7c043-4" id="gg2Dialog" type="dialog" data-ref="gg2Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao2_t}}" data-url="{{shopset.detail_guangao2_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg2Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block></view><view id="scroll_view_tab1"><block wx:if="{{shopset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评率<text style="{{'color:'+($root.m31)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g8>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block></view><block wx:if="{{$root.g9}}"><view class="notes-section"><view class="notes-title"><view class="f1">{{"笔记("+notes_total+")"}}</view><view class="f2" data-url="{{'notelist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部<image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="notes-list"><view class="waterfall-wrapper"><view class="waterfall-column"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index%3===0}}"><view class="note-item" data-url="{{'/daihuobiji/detail/index?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g10}}"><view class="note-images"><image src="{{item.$orig.pics[0]}}" mode="widthFix"></image></view></block><view class="note-content">{{item.$orig.title}}</view><view class="note-user"><image class="avatar" src="{{item.$orig.headimg}}"></image><view class="user-info"><text class="nickname">{{item.$orig.nickname}}</text></view><view class="note-likes"><image src="/static/img/like.png" mode="widthFix"></image><text>{{item.$orig.likes}}</text></view></view></view></block></block></view><view class="waterfall-column"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index%3===1}}"><view class="note-item" data-url="{{'/daihuobiji/detail/index?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g11}}"><view class="note-images"><image src="{{item.$orig.pics[0]}}" mode="widthFix"></image></view></block><view class="note-content">{{item.$orig.title}}</view><view class="note-user"><image class="avatar" src="{{item.$orig.headimg}}"></image><view class="user-info"><text class="nickname">{{item.$orig.nickname}}</text></view><view class="note-likes"><image src="/static/img/like.png" mode="widthFix"></image><text>{{item.$orig.likes}}</text></view></view></view></block></block></view><view class="waterfall-column"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index%3===2}}"><view class="note-item" data-url="{{'/daihuobiji/detail/index?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g12}}"><view class="note-images"><image src="{{item.$orig.pics[0]}}" mode="widthFix"></image></view></block><view class="note-content">{{item.$orig.title}}</view><view class="note-user"><image class="avatar" src="{{item.$orig.headimg}}"></image><view class="user-info"><text class="nickname">{{item.$orig.nickname}}</text></view><view class="note-likes"><image src="/static/img/like.png" mode="widthFix"></image><text>{{item.$orig.likes}}</text></view></view></view></block></block></view></view></view></view></block><view id="scroll_view_tab2"><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m32+' 0%,rgba('+$root.m33+',0.8) 100%)')+';'}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入首页</button></view></block><block wx:if="{{!$root.m34}}"><block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品参数</view><view class="t2"></view><view class="t1"></view></view><view class="paraminfo" style="background:#fff;padding:20rpx 40rpx;"><block wx:for="{{product.paramdata}}" wx:for-item="item" wx:for-index="index"><view class="paramitem"><view class="f1">{{index}}</view><view class="f2">{{item}}</view></view></block></view></block></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="76a7c043-5" pagecontent="{{pagecontent}}" bind:__l="__l"></dp><block wx:if="{{bottomImg}}"><image class="bottomimg" src="{{bottomImg}}" mode="widthFix"></image></block></view></view><view id="scroll_view_tab3"><block wx:if="{{$root.g13>0}}"><view><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="/static/img/xihuan.png"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view><view class="prolist"><dp-product-item vue-id="76a7c043-6" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-product-item></view></view></block></view><view style="width:100%;height:140rpx;"></view></scroll-view><block wx:if="{{product.status==1&&!showcuxiaodialog}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><block wx:if="{{visibleMenuItemsCount>0}}"><view class="f1" style="{{'width:'+(f1ContainerWidth)+';'}}"><block wx:if="{{shopdetail_menudataList}}"><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.$orig.isShow==1}}"><block><block wx:if="{{item.$orig.menuType==1}}"><block><block wx:if="{{item.$orig.useSystem==1}}"><block><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/kefu.png'}}"></image><view class="t1">{{item.$orig.text?item.$orig.text:'客服'}}</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/kefu.png'}}"></image><view class="t1">{{item.$orig.text?item.$orig.text:'客服'}}</view></button></block></block></block><block wx:else><block><block wx:if="{{item.$orig.pagePath=='contact::'}}"><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/kefu.png'}}"></image><view class="t1">{{item.$orig.text?item.$orig.text:'客服'}}</view></button></block><block wx:else><view data-event-opts="{{[['tap',[['addfavorite2',['$0'],[[['shopdetail_menudataList','',index]]]]]]]}}" class="item" bindtap="__e"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/kefu.png'}}"></image><view class="t1">{{item.$orig.text?item.$orig.text:'客服'}}</view></view></block></block></block></block></block><block wx:if="{{item.$orig.menuType==2}}"><block><view data-event-opts="{{[['tap',[['addfavorite2',['$0'],[[['shopdetail_menudataList','',index]]]]]]]}}" class="item" bindtap="__e"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/gwc.png'}}"></image><view class="t1">{{item.$orig.text?item.$orig.text:'购物车'}}</view><block wx:if="{{cartnum>0}}"><view class="cartnum" style="{{'background:'+('rgba('+item.m35+',0.8)')+';'}}">{{cartnum+''}}</view></block></view></block></block><block wx:if="{{item.$orig.menuType==3}}"><block><view data-event-opts="{{[['tap',[['addfavorite2',['$0'],[[['shopdetail_menudataList','',index]]]]]]]}}" class="item" bindtap="__e"><block wx:if="{{!isfavorite}}"><image class="img" src="{{item.$orig.iconPath?item.$orig.iconPath:pre_url+'/static/img/shoucang.png'}}"></image></block><block wx:else><image class="img" src="{{item.$orig.selectedIconPath?item.$orig.selectedIconPath:pre_url+'/static/img/shoucang.png'}}"></image></block><block wx:if="{{item.$orig.selectedtext&&item.$orig.text}}"><view class="t1">{{isfavorite?item.$orig.selectedtext:item.$orig.text}}</view></block><block wx:else><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></block></view></block></block><block wx:if="{{item.$orig.menuType==4}}"><block><view data-event-opts="{{[['tap',[['addfavorite2',['$0'],[[['shopdetail_menudataList','',index]]]]]]]}}" class="item" bindtap="__e"><image class="img" src="{{item.$orig.iconPath}}"></image><view class="t1">{{item.$orig.text}}</view></view></block></block></block></block></block></block><block wx:else><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view class="item" data-url="/pages/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/gwc.png'}}"></image><view class="t1">购物车</view><block wx:if="{{cartnum>0}}"><view class="cartnum" style="{{'background:'+('rgba('+$root.m36+',0.8)')+';'}}">{{cartnum}}</view></block></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></block></view></block><block wx:if="{{showjiesheng==1}}"><view class="op2"><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="tocart2" style="{{'background:'+($root.m37)+';'}}" bindtap="__e"><text>分享赚钱</text><text style="font-size:24rpx;">{{"赚￥"+product.commission}}</text></view><block wx:if="{{product.show_buybtn!==0}}"><view class="tobuy2" style="{{'background:'+($root.m38)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><text>立即购买</text><block wx:if="{{product.jiesheng_money>0}}"><text style="font-size:24rpx;">{{"省￥"+product.jiesheng_money}}</text></block></view></block></view></block><block wx:else><view class="op"><block wx:if="{{product.hide_price==1}}"><block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m39)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['hidePriceLink',['$event']]]]]}}" bindtap="__e">{{''+(product.hide_price_detail_text?product.hide_price_detail_text:'咨询')+''}}</view></block></block><block wx:else><block wx:if="{{product.price_type==1}}"><block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m40)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" bindtap="__e">{{(product.xunjia_text?product.xunjia_text:'联系TA')+''}}</view></block></block><block wx:else><block><block wx:if="{{product.show_addcartbtn!==0&&product.freighttype!=3&&product.freighttype!=4}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m41)+';'}}" bindtap="__e">{{''+(product.addcart_name||'加入购物车')+''}}</view></block><block wx:if="{{product.show_buybtn!==0}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m42)+';'}}" bindtap="__e">{{''+(product.buybtn_name||'立即购买')+''}}</view></block></block></block></block></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="76a7c043-7" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^showLinkChange',[['showLinkChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:showLinkChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view hidden="{{!(scrolltopshow)}}" class="scrolltop" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/gotop.png"></image></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m43=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m44=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m45=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view><block wx:if="{{$root.m46}}"><view data-event-opts="{{[['tap',[['shareScheme',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">小程序链接</text></view></block><view data-event-opts="{{[['tap',[['goEdit',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/edit2.png"></image><text class="t1">发布笔记</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><view style="display:flex;justify-content:space-between;padding:0 10px 10px 10px;"><button data-event-opts="{{[['tap',[['savePhoto',['$event']]]]]}}" class="pp4" style="{{'background:'+('linear-gradient(90deg,'+$root.m47+' 0%,rgba('+$root.m48+',0.8) 100%)')+';'}}" bindtap="__e">保存</button><block wx:if="{{$root.m49=='app'}}"><button data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="pp4" style="{{'background:'+('linear-gradient(90deg,'+$root.m50+' 0%,rgba('+$root.m51+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><block wx:if="{{$root.m52=='mp'}}"><button data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="pp4" style="{{'background:'+('linear-gradient(90deg,'+$root.m53+' 0%,rgba('+$root.m54+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><block wx:if="{{$root.m55=='h5'}}"><button data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="pp4" style="{{'background:'+('linear-gradient(90deg,'+$root.m56+' 0%,rgba('+$root.m57+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><button class="pp4" style="{{'background:'+('linear-gradient(90deg,'+$root.m58+' 0%,rgba('+$root.m59+',0.8) 100%)')+';'}}" open-type="share">转发</button></block></block></block></view></view></view></block><block wx:if="{{showScheme}}"><view class="posterDialog schemeDialog"><view class="main"><view class="schemecon"><view style="line-height:60rpx;">{{product.name+''}}</view><view>购买链接：<text style="color:#00A0E9;">{{schemeurl}}</text></view><view class="copybtn" style="{{'background:'+('linear-gradient(90deg,'+$root.m60+' 0%,rgba('+$root.m61+',0.8) 100%)')+';'}}" data-text="{{product.name+'购买链接：'+schemeurl}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">一键复制</view></view></view></view></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{sysset.name}}</view><block wx:if="{{product.bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+business.name+''}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{business.tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m62)+';'}}" data-url="{{'tel::'+business.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+business.tel+''}}<image class="copyicon" src="/static/img/copy.png" data-text="{{business.tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="76a7c043-8" bind:__l="__l"></loading></block><dp-tabbar vue-id="76a7c043-9" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="76a7c043-10" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="76a7c043-11" data-ref="popupLucky" bind:__l="__l" vue-slots="{{['default']}}"><view class="model show"><view class="luckBao-header"><image class="luckBao-img-close" src="/game/static/game/lucky/icon_close.png" mode="widthFix" data-event-opts="{{[['tap',[['onCloseLucky',['$event']]]]]}}" bindtap="__e"></image></view><view class="luckBao"><view class="luckBao-text">{{"中奖概率："+$root.m63+"%"}}</view><view class="luckBao-input-wrap"><text>幸运价</text><input data-event-opts="{{[['input',[['inputLuckyPrice',['$event']]]]]}}" class="luckBao-input-class" value="{{luckyPrice}}" bindinput="__e"/><picker value="{{priceIndex}}" range="{{luckyPriceArray}}" data-event-opts="{{[['change',[['pickerChangePrice',['$event']]]]]}}" bindchange="__e"><image class="img-cursor-2" src="/game/static/game/lucky/icon_cursor.png" mode="widthFix"></image></picker></view><view class="luckBao-tips-wrap1">点击输入框手动填写价格</view><view class="luckBao-tips-wrap2"><text>或者点击</text><image class="img-cursor-1" src="/game/static/game/lucky/icon_cursor.png" mode="widthFix"></image><text>选择价格</text></view></view><view data-event-opts="{{[['tap',[['onLuckyNow',['$event']]]]]}}" class="lucky-btn-now" bindtap="__e">立即夺宝</view><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="lucky-btn-origin-price" bindtap="__e">原价购买</view></view></uni-popup></view>