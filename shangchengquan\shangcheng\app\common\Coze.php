<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\common;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 扣子(Coze) API公共类
 * @package app\common
 */
class Coze
{
    /**
     * @var string Coze API的基础URL
     */
    protected static $baseUrl = 'https://api.coze.cn';
    
    /**
     * @var string Coze API的版本
     */
    protected static $apiVersion = 'v3';
    
    /**
     * @var string Coze API的密钥
     */
    protected static $apiKey = '';
    
    /**
     * @var string 当前使用的Bot ID
     */
    protected static $botId = '';
    
    /**
     * 初始化配置
     * 
     * @param int $aid 应用ID
     * @return bool
     */
    public static function init($aid)
    {
        // 获取Coze API配置
        $config = Db::name('coze_config')->where('aid', $aid)->find();
        if (!$config) {
            return false;
        }
        
        self::$apiKey = $config['api_key'] ?? '';
        self::$botId = $config['bot_id'] ?? '';
        self::$baseUrl = $config['base_url'] ?? 'https://api.coze.cn';
        self::$apiVersion = $config['api_version'] ?? 'v3';
        
        return !empty(self::$apiKey);
    }
    
    /**
     * 发送HTTP请求到Coze API
     * 
     * @param int $aid 应用ID
     * @param string $endpoint API端点
     * @param array $data 请求数据
     * @param string $method 请求方法
     * @param int $mid 会员ID
     * @return array 响应数据
     */
    public static function sendRequest($aid, $endpoint, $data = [], $method = 'POST', $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        
        // 初始化配置
        if (!self::init($aid)) {
            Log::error("{$requestTime}-ERROR-[Coze][sendRequest_001] 扣子API配置未找到或API密钥为空");
            return [
                'code' => 0,
                'msg' => 'API配置未找到或API密钥为空',
                'data' => null
            ];
        }
        
        // 构建完整URL
        $url = self::$baseUrl . '/' . self::$apiVersion . '/' . $endpoint;
        
        Log::info("{$requestTime}-INFO-[Coze][sendRequest_002] 准备发送请求: {$method} {$url}");
        
        // 数据类型转换和验证
        if (isset($data['bot_id']) && is_numeric($data['bot_id'])) {
            $data['bot_id'] = (string)$data['bot_id'];
        }
        if (isset($data['user_id']) && is_numeric($data['user_id'])) {
            $data['user_id'] = (string)$data['user_id'];
        }
        if (isset($data['stream'])) {
            $data['stream'] = (bool)$data['stream'];
        }
        if (isset($data['auto_save_history'])) {
            $data['auto_save_history'] = (bool)$data['auto_save_history'];
        }
        
        // 设置请求头
        $headers = [
            'Authorization: Bearer ' . self::$apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        // 初始化CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 根据请求方法设置不同的参数
        switch ($method) {
            case 'GET':
                if (!empty($data)) {
                    $url .= '?' . http_build_query($data);
                    curl_setopt($ch, CURLOPT_URL, $url);
                }
                break;
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
                }
                break;
        }
        
        // 执行请求
        $requestStartTime = microtime(true);
        $response = curl_exec($ch);
        $requestEndTime = microtime(true);
        $executionTime = round(($requestEndTime - $requestStartTime) * 1000, 2); // 毫秒
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        Log::info("{$requestTime}-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: {$httpCode}，耗时: {$executionTime}ms");
        
        // 处理CURL错误
        if (!empty($error)) {
            Log::error("{$requestTime}-ERROR-[Coze][sendRequest_004] 请求出错: {$error}");
            
            // 记录错误日志到数据库
            self::logRequest($aid, $mid, $endpoint, $method, $data, $httpCode, $response, $error, $executionTime, 0);
            
            return [
                'code' => 0,
                'msg' => '请求失败: ' . $error,
                'data' => null
            ];
        }
        
        // 解析响应
        $responseData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error("{$requestTime}-ERROR-[Coze][sendRequest_005] 响应解析失败: " . json_last_error_msg());
            
            // 记录解析错误日志
            self::logRequest($aid, $mid, $endpoint, $method, $data, $httpCode, $response, 'JSON解析失败: ' . json_last_error_msg(), $executionTime, 0);
            
            return [
                'code' => 0,
                'msg' => '响应解析失败: ' . json_last_error_msg(),
                'data' => $response
            ];
        }
        
        // 记录成功日志
        $status = ($httpCode >= 200 && $httpCode < 300) ? 1 : 0;
        self::logRequest($aid, $mid, $endpoint, $method, $data, $httpCode, $response, null, $executionTime, $status);
        
        // 检查HTTP状态码
        if ($httpCode < 200 || $httpCode >= 300) {
            Log::error("{$requestTime}-ERROR-[Coze][sendRequest_006] HTTP错误状态码: {$httpCode}");
            return [
                'code' => 0,
                'msg' => "HTTP错误: {$httpCode}",
                'data' => $responseData
            ];
        }
        
        return [
            'code' => 1,
            'msg' => '请求成功',
            'data' => $responseData
        ];
    }
    
    /**
     * 记录API请求日志
     * 
     * @param int $aid 应用ID
     * @param int $mid 会员ID
     * @param string $endpoint API端点
     * @param string $method 请求方法
     * @param array $requestData 请求数据
     * @param int $responseCode 响应状态码
     * @param string $responseData 响应数据
     * @param string $errorMsg 错误信息
     * @param float $executionTime 执行时间
     * @param int $status 状态
     */
    protected static function logRequest($aid, $mid, $endpoint, $method, $requestData, $responseCode, $responseData, $errorMsg, $executionTime, $status)
    {
        $logData = [
            'aid' => $aid,
            'mid' => $mid,
            'endpoint' => $endpoint,
            'method' => $method,
            'request_data' => json_encode($requestData),
            'response_code' => $responseCode,
            'response_data' => $responseData,
            'error_msg' => $errorMsg,
            'execution_time' => $executionTime,
            'ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'status' => $status,
            'create_time' => time()
        ];
        
        try {
            Db::name('coze_api_log')->insert($logData);
        } catch (\Exception $e) {
            Log::error('记录扣子API日志失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取机器人列表
     * 
     * @param int $aid 应用ID
     * @param int $mid 会员ID
     * @return array
     */
    public static function getBots($aid, $mid = 0)
    {
        return self::sendRequest($aid, 'bots', [], 'GET', $mid);
    }
    
    /**
     * 获取机器人详情
     * 
     * @param int $aid 应用ID
     * @param string $botId 机器人ID
     * @param int $mid 会员ID
     * @return array
     */
    public static function getBotDetail($aid, $botId, $mid = 0)
    {
        return self::sendRequest($aid, 'bots/' . $botId, [], 'GET', $mid);
    }
    
    /**
     * 发送聊天消息
     * 
     * @param int $aid 应用ID
     * @param string $botId 机器人ID
     * @param string $userId 用户ID
     * @param string $message 消息内容
     * @param int $mid 会员ID
     * @param array $additionalMessages 额外消息
     * @return array
     */
    public static function chat($aid, $botId, $userId, $message, $mid = 0, $additionalMessages = [])
    {
        $data = [
            'bot_id' => $botId,
            'user_id' => $userId,
            'stream' => false,
            'auto_save_history' => true,
            'additional_messages' => array_merge([
                [
                    'role' => 'user',
                    'content' => $message,
                    'content_type' => 'text'
                ]
            ], $additionalMessages)
        ];
        
        return self::sendRequest($aid, 'chat', $data, 'POST', $mid);
    }

    /**
     * 上传文件到Coze
     *
     * @param int $aid 应用ID
     * @param string $filePath 文件路径
     * @param int $mid 会员ID
     * @return array
     */
    public static function uploadFile($aid, $filePath, $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');

        // 初始化配置
        if (!self::init($aid)) {
            Log::error("{$requestTime}-ERROR-[Coze][uploadFile_001] 扣子API配置未找到或API密钥为空");
            return [
                'code' => 0,
                'msg' => 'API配置未找到或API密钥为空',
                'data' => null
            ];
        }

        // 检查文件是否存在
        if (!file_exists($filePath)) {
            return [
                'code' => 0,
                'msg' => '文件不存在',
                'data' => null
            ];
        }

        // 构建上传URL
        $url = self::$baseUrl . '/v1/files/upload';

        Log::info("{$requestTime}-INFO-[Coze][uploadFile_002] 准备上传文件: {$filePath}");

        // 设置请求头
        $headers = [
            'Authorization: Bearer ' . self::$apiKey
        ];

        // 初始化CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 文件上传需要更长时间
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_POST, true);

        // 设置文件上传
        $postData = [
            'file' => new \CURLFile($filePath)
        ];
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        // 执行请求
        $requestStartTime = microtime(true);
        $response = curl_exec($ch);
        $requestEndTime = microtime(true);
        $executionTime = round(($requestEndTime - $requestStartTime) * 1000, 2);

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        Log::info("{$requestTime}-INFO-[Coze][uploadFile_003] 文件上传完成，HTTP状态码: {$httpCode}，耗时: {$executionTime}ms");

        // 处理CURL错误
        if (!empty($error)) {
            Log::error("{$requestTime}-ERROR-[Coze][uploadFile_004] 文件上传出错: {$error}");
            return [
                'code' => 0,
                'msg' => '文件上传失败: ' . $error,
                'data' => null
            ];
        }

        // 解析响应
        $responseData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error("{$requestTime}-ERROR-[Coze][uploadFile_005] 响应解析失败: " . json_last_error_msg());
            return [
                'code' => 0,
                'msg' => '响应解析失败: ' . json_last_error_msg(),
                'data' => $response
            ];
        }

        // 检查HTTP状态码
        if ($httpCode < 200 || $httpCode >= 300) {
            Log::error("{$requestTime}-ERROR-[Coze][uploadFile_006] HTTP错误状态码: {$httpCode}");
            return [
                'code' => 0,
                'msg' => "文件上传失败: HTTP {$httpCode}",
                'data' => $responseData
            ];
        }

        return [
            'code' => 1,
            'msg' => '文件上传成功',
            'data' => $responseData
        ];
    }

    /**
     * 运行工作流
     *
     * @param int $aid 应用ID
     * @param string $workflowId 工作流ID
     * @param array $parameters 工作流参数
     * @param int $mid 会员ID
     * @return array
     */
    public static function runWorkflow($aid, $workflowId, $parameters = [], $mid = 0)
    {
        $data = [
            'workflow_id' => $workflowId,
            'parameters' => $parameters
        ];

        return self::sendRequest($aid, 'workflow/run', $data, 'POST', $mid);
    }

    /**
     * 运行工作流（带文件支持）
     *
     * @param int $aid 应用ID
     * @param string $workflowId 工作流ID
     * @param array $parameters 工作流参数（文本参数）
     * @param array $files 文件参数 ['param_name' => 'file_path']
     * @param int $mid 会员ID
     * @return array
     */
    public static function runWorkflowWithFiles($aid, $workflowId, $parameters = [], $files = [], $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        Log::info("{$requestTime}-INFO-[Coze][runWorkflowWithFiles_001] 开始运行工作流，ID: {$workflowId}");

        // 处理文件上传
        $fileParameters = [];
        foreach ($files as $paramName => $filePath) {
            Log::info("{$requestTime}-INFO-[Coze][runWorkflowWithFiles_002] 上传文件参数: {$paramName}");

            $uploadResult = self::uploadFile($aid, $filePath, $mid);
            if ($uploadResult['code'] != 1) {
                Log::error("{$requestTime}-ERROR-[Coze][runWorkflowWithFiles_003] 文件上传失败: " . $uploadResult['msg']);
                return $uploadResult;
            }

            // 获取file_id
            $fileId = $uploadResult['data']['data']['id'] ?? '';
            if (empty($fileId)) {
                Log::error("{$requestTime}-ERROR-[Coze][runWorkflowWithFiles_004] 未获取到file_id");
                return [
                    'code' => 0,
                    'msg' => '文件上传成功但未获取到file_id',
                    'data' => $uploadResult['data']
                ];
            }

            // 将file_id格式化为工作流需要的格式
            $fileParameters[$paramName] = json_encode(['file_id' => $fileId]);
            Log::info("{$requestTime}-INFO-[Coze][runWorkflowWithFiles_005] 文件参数处理完成: {$paramName} = {$fileId}");
        }

        // 合并文本参数和文件参数
        $allParameters = array_merge($parameters, $fileParameters);

        Log::info("{$requestTime}-INFO-[Coze][runWorkflowWithFiles_006] 最终参数: " . json_encode($allParameters));

        // 运行工作流
        return self::runWorkflow($aid, $workflowId, $allParameters, $mid);
    }

    /**
     * 创建对话
     *
     * @param int $aid 应用ID
     * @param string $botId Bot ID
     * @param int $mid 用户ID
     * @return array
     */
    public static function createConversation($aid, $botId, $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        Log::info("{$requestTime}-INFO-[Coze][createConversation_001] 开始创建对话, aid: {$aid}, botId: {$botId}");

        $data = [
            'bot_id' => $botId
        ];

        $response = self::sendRequest($aid, 'conversation/create', $data, 'POST', $mid);

        if ($response['code'] == 1 && isset($response['data']['id'])) {
            // 保存对话记录
            $conversationData = [
                'aid' => $aid,
                'conversation_id' => $response['data']['id'],
                'bot_id' => $botId,
                'mid' => $mid,
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ];

            Db::name('coze_conversation')->insert($conversationData);

            Log::info("{$requestTime}-INFO-[Coze][createConversation_002] 对话创建成功: " . $response['data']['id']);
            return ['code' => 1, 'msg' => '对话创建成功', 'data' => $response['data']];
        }

        Log::error("{$requestTime}-ERROR-[Coze][createConversation_003] 对话创建失败: " . json_encode($response));
        return ['code' => 0, 'msg' => '对话创建失败: ' . ($response['msg'] ?? '未知错误')];
    }

    /**
     * 发送消息
     *
     * @param int $aid 应用ID
     * @param string $conversationId 对话ID
     * @param string $botId Bot ID
     * @param string $message 消息内容
     * @param int $mid 用户ID
     * @return array
     */
    public static function sendMessage($aid, $conversationId, $botId, $message, $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        Log::info("{$requestTime}-INFO-[Coze][sendMessage_001] 开始发送消息, aid: {$aid}, conversationId: {$conversationId}");

        $data = [
            'conversation_id' => $conversationId,
            'bot_id' => $botId,
            'user' => 'user',
            'query' => $message,
            'stream' => false
        ];

        $response = self::sendRequest($aid, 'conversation/message/create', $data, 'POST', $mid);

        if ($response['code'] == 1) {
            // 保存消息记录
            $messageData = [
                'aid' => $aid,
                'conversation_id' => $conversationId,
                'bot_id' => $botId,
                'mid' => $mid,
                'user_message' => $message,
                'bot_response' => json_encode($response['data']),
                'create_time' => time()
            ];

            Db::name('coze_message')->insert($messageData);

            Log::info("{$requestTime}-INFO-[Coze][sendMessage_002] 消息发送成功");
            return ['code' => 1, 'msg' => '消息发送成功', 'data' => $response['data']];
        }

        Log::error("{$requestTime}-ERROR-[Coze][sendMessage_003] 消息发送失败: " . json_encode($response));
        return ['code' => 0, 'msg' => '消息发送失败: ' . ($response['msg'] ?? '未知错误')];
    }

    /**
     * 获取工作流运行状态
     *
     * @param int $aid 应用ID
     * @param string $runId 运行ID
     * @param int $mid 用户ID
     * @return array
     */
    public static function getWorkflowRunStatus($aid, $runId, $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        Log::info("{$requestTime}-INFO-[Coze][getWorkflowRunStatus_001] 开始获取运行状态, aid: {$aid}, runId: {$runId}");

        $response = self::sendRequest($aid, 'workflow/run/' . $runId, [], 'GET', $mid);

        if ($response['code'] == 1) {
            Log::info("{$requestTime}-INFO-[Coze][getWorkflowRunStatus_002] 状态获取成功");
            return ['code' => 1, 'msg' => '状态获取成功', 'data' => $response['data']];
        }

        Log::error("{$requestTime}-ERROR-[Coze][getWorkflowRunStatus_003] 状态获取失败: " . json_encode($response));
        return ['code' => 0, 'msg' => '状态获取失败: ' . ($response['msg'] ?? '未知错误')];
    }

    /**
     * 代理请求
     *
     * @param int $aid 应用ID
     * @param string $apiToken API令牌
     * @param string $endpoint 端点
     * @param string $method 请求方法
     * @param array $data 请求数据
     * @param int $mid 用户ID
     * @return array
     */
    public static function proxyRequest($aid, $apiToken, $endpoint, $method = 'POST', $data = [], $mid = 0)
    {
        $requestTime = date('Y-m-d H:i:s');
        Log::info("{$requestTime}-INFO-[Coze][proxyRequest_001] 开始代理请求, aid: {$aid}, endpoint: {$endpoint}");

        // 临时设置API密钥
        $originalApiKey = self::$apiKey;
        self::$apiKey = $apiToken;

        // 使用自定义端点
        $response = self::sendRequest($aid, ltrim($endpoint, '/'), $data, $method, $mid);

        // 恢复原始API密钥
        self::$apiKey = $originalApiKey;

        if ($response['code'] == 1) {
            Log::info("{$requestTime}-INFO-[Coze][proxyRequest_002] 代理请求成功");
            return ['code' => 1, 'msg' => '代理请求成功', 'data' => $response['data']];
        }

        Log::error("{$requestTime}-ERROR-[Coze][proxyRequest_003] 代理请求失败: " . json_encode($response));
        return ['code' => 0, 'msg' => '代理请求失败: ' . ($response['msg'] ?? '未知错误')];
    }
}
