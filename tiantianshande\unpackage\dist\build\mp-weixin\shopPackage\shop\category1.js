(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["shopPackage/shop/category1"],{"7a32":function(t,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:[],bid:0}},onLoad:function(t){this.opt=e.getopts(t),this.bid=this.opt.bid?this.opt.bid:0,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,e.get("ApiShop/category1",{bid:t.bid},(function(n){t.loading=!1,t.data=n.data,t.loaded()}))}}};n.default=o},8138:function(t,n,a){"use strict";a.d(n,"b",(function(){return o})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return e}));var e={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this.$createElement;this._self._c},i=[]},"8e1f":function(t,n,a){"use strict";a.r(n);var e=a("7a32"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},"8fb9":function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("d507"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},a8df:function(t,n,a){"use strict";var e=a("b2a4"),o=a.n(e);o.a},b2a4:function(t,n,a){},d507:function(t,n,a){"use strict";a.r(n);var e=a("8138"),o=a("8e1f");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("a8df");var u=a("828b"),d=Object(u["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=d.exports}},[["8fb9","common/runtime","common/vendor"]]]);