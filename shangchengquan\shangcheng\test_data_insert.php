<?php
/**
 * 通过Web访问插入扣子工作流测试数据
 * 访问方式：http://域名/test_data_insert.php
 */

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = 'root123';
$database = 'qixian_zhongheng';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>插入扣子工作流测试数据</h1>";
    
    // 设置aid为1
    $aid = 1;
    
    // 检查并清空现有数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_coze_workflow WHERE aid = ?");
    $stmt->execute([$aid]);
    $existingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($existingCount > 0) {
        echo "<p>已存在 {$existingCount} 条工作流数据，先清空...</p>";
        $stmt = $pdo->prepare("DELETE FROM ddwx_coze_workflow WHERE aid = ?");
        $stmt->execute([$aid]);
    }
    
    // 插入工作流测试数据
    $workflowData = [
        [
            'aid' => $aid,
            'name' => '文档分析工作流',
            'workflow_id' => '7476690468139860006',
            'description' => '用于分析PDF文档内容的工作流',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ],
        [
            'aid' => $aid,
            'name' => '图片处理工作流',
            'workflow_id' => '7476690468139860007',
            'description' => '用于处理和分析图片的工作流',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ],
        [
            'aid' => $aid,
            'name' => '文本生成工作流',
            'workflow_id' => '7476690468139860008',
            'description' => '用于生成文本内容的工作流',
            'status' => 0,
            'create_time' => time(),
            'update_time' => time()
        ]
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO ddwx_coze_workflow 
        (aid, name, workflow_id, description, status, create_time, update_time) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    echo "<h2>插入工作流数据：</h2>";
    foreach ($workflowData as $data) {
        $result = $insertStmt->execute([
            $data['aid'],
            $data['name'],
            $data['workflow_id'],
            $data['description'],
            $data['status'],
            $data['create_time'],
            $data['update_time']
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ 成功插入工作流: {$data['name']}</p>";
        } else {
            echo "<p style='color: red;'>❌ 插入工作流失败: {$data['name']}</p>";
        }
    }
    
    // 插入API配置数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_coze_config WHERE aid = ?");
    $stmt->execute([$aid]);
    $existingConfigCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($existingConfigCount > 0) {
        echo "<p>已存在 {$existingConfigCount} 条API配置数据，先清空...</p>";
        $stmt = $pdo->prepare("DELETE FROM ddwx_coze_config WHERE aid = ?");
        $stmt->execute([$aid]);
    }
    
    $configData = [
        'aid' => $aid,
        'name' => '默认配置',
        'api_key' => 'pat_WFZvXcAixTK2EStyRXGQzhLV3qSR5RzsKA4ycvKmvsVkXn4CAg9FGQoaXZpdHuDR',
        'bot_id' => '7476690468139860006',
        'workflow_id' => '7476690468139860006',
        'base_url' => 'https://api.coze.cn',
        'api_version' => 'v1',
        'timeout' => 30,
        'max_retries' => 3,
        'remark' => '测试配置，用于开发和调试',
        'status' => 1,
        'create_time' => time(),
        'update_time' => time()
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO ddwx_coze_config 
        (aid, name, api_key, bot_id, workflow_id, base_url, api_version, timeout, max_retries, remark, status, create_time, update_time) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    echo "<h2>插入API配置数据：</h2>";
    $result = $insertStmt->execute([
        $configData['aid'],
        $configData['name'],
        $configData['api_key'],
        $configData['bot_id'],
        $configData['workflow_id'],
        $configData['base_url'],
        $configData['api_version'],
        $configData['timeout'],
        $configData['max_retries'],
        $configData['remark'],
        $configData['status'],
        $configData['create_time'],
        $configData['update_time']
    ]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ 成功插入API配置: {$configData['name']}</p>";
    } else {
        echo "<p style='color: red;'>❌ 插入API配置失败</p>";
    }
    
    // 验证插入结果
    echo "<h2>验证插入结果：</h2>";
    
    $stmt = $pdo->prepare("SELECT * FROM ddwx_coze_workflow WHERE aid = ?");
    $stmt->execute([$aid]);
    $workflows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>工作流数据 (aid={$aid}):</h3>";
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>名称</th><th>工作流ID</th><th>描述</th><th>状态</th>";
    echo "</tr>";
    
    foreach ($workflows as $workflow) {
        $statusText = $workflow['status'] ? '启用' : '禁用';
        echo "<tr>";
        echo "<td>{$workflow['id']}</td>";
        echo "<td>{$workflow['name']}</td>";
        echo "<td>{$workflow['workflow_id']}</td>";
        echo "<td>{$workflow['description']}</td>";
        echo "<td style='color: " . ($workflow['status'] ? 'green' : 'red') . ";'>{$statusText}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $stmt = $pdo->prepare("SELECT * FROM ddwx_coze_config WHERE aid = ?");
    $stmt->execute([$aid]);
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>API配置数据 (aid={$aid}):</h3>";
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>名称</th><th>状态</th><th>备注</th>";
    echo "</tr>";
    
    foreach ($configs as $config) {
        $statusText = $config['status'] ? '启用' : '禁用';
        echo "<tr>";
        echo "<td>{$config['id']}</td>";
        echo "<td>{$config['name']}</td>";
        echo "<td style='color: " . ($config['status'] ? 'green' : 'red') . ";'>{$statusText}</td>";
        echo "<td>{$config['remark']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2 style='color: green;'>✅ 测试数据插入完成！</h2>";
    echo "<p>现在可以访问后台扣子管理页面查看数据了。</p>";
    echo "<p><a href='admin.php?s=/Coze/workflow' target='_blank'>点击这里访问工作流管理页面</a></p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ 数据库连接失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库配置是否正确：</p>";
    echo "<ul>";
    echo "<li>主机: {$host}</li>";
    echo "<li>用户名: {$username}</li>";
    echo "<li>数据库: {$database}</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 插入数据失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
}
?>
