<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>扣子配置管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-cog"></i> 扣子配置管理</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('{:url('edit')}')">添加配置</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">启用</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">禁用</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">配置名</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">启用</option>
										<option value="0">禁用</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-user-front-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<table class="layui-hide" id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
						<script type="text/html" id="table-useradmin-admin">
							<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
							<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="test"><i class="layui-icon layui-icon-play"></i>测试</a>
						</script>
					</div>
        </div>
    </div>
  </div>
  {include file="public/js"/}
  <script>
    layui.config({
        base: '/static/admin/'
    }).extend({
        index: 'lib/index'
    }).use(['index', 'user'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table;

        table.render({
            elem: '#LAY-user-manage'
            ,url: '{:url("index")}'
            ,cols: [[
                {type: 'checkbox', fixed: 'left'}
                ,{field: 'id', width: 80, title: 'ID', sort: true}
                ,{field: 'name', title: '配置名称', minWidth: 100}
                ,{field: 'api_key', title: 'API密钥', minWidth: 200, templet: function(d){
                    return d.api_key ? d.api_key.substr(0, 20) + '...' : '';
                }}
                ,{field: 'base_url', title: 'API地址', minWidth: 150}
                ,{field: 'status', title: '状态', width: 80, templet: function(d){
                    return d.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge">禁用</span>';
                }}
                ,{field: 'create_time', title: '创建时间', width: 160}
                ,{title: '操作', width: 200, align:'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
            ]]
            ,page: true
            ,limit: 30
            ,height: 'full-220'
            ,text: '对不起，加载出现异常！'
        });

        // 监听搜索操作
        form.on('submit(LAY-user-front-search)', function(data){
            var field = data.field;
            table.reload('LAY-user-manage', {
                where: field
            });
        });

        // 监听工具条
        table.on('tool(LAY-user-manage)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('真的删除行么', function(index){
                    obj.del();
                    layer.close(index);
                    $.post('{:url("del")}', {id: data.id}, function(res){
                        if(res.status == 1){
                            layer.msg(res.msg, {icon: 1});
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                });
            } else if(obj.event === 'edit'){
                openmax('{:url("edit")}?id=' + data.id);
            } else if(obj.event === 'test'){
                layer.msg('正在测试连接...', {icon: 16, shade: 0.01, time: 0});
                $.post('{:url("test")}', {id: data.id}, function(res){
                    layer.closeAll();
                    if(res.status == 1){
                        layer.msg('连接测试成功', {icon: 1});
                    } else {
                        layer.msg('连接测试失败：' + res.msg, {icon: 2});
                    }
                });
            }
        });
    });

    // 删除选中
    function datadel(type) {
        var checkStatus = layui.table.checkStatus('LAY-user-manage');
        var data = checkStatus.data;
        if(data.length === 0){
            layer.msg('请选择要删除的数据');
            return;
        }
        layer.confirm('确定删除选中的数据吗？', function(index){
            var ids = [];
            layui.each(data, function(index, item){
                ids.push(item.id);
            });
            $.post('{:url("del")}', {id: ids.join(',')}, function(res){
                if(res.status == 1){
                    layer.msg(res.msg, {icon: 1});
                    layui.table.reload('LAY-user-manage');
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    }

    // 批量设置状态
    function setst(type, status) {
        var checkStatus = layui.table.checkStatus('LAY-user-manage');
        var data = checkStatus.data;
        if(data.length === 0){
            layer.msg('请选择要操作的数据');
            return;
        }
        var ids = [];
        layui.each(data, function(index, item){
            ids.push(item.id);
        });
        $.post('{:url("setst")}', {id: ids.join(','), status: status}, function(res){
            if(res.status == 1){
                layer.msg(res.msg, {icon: 1});
                layui.table.reload('LAY-user-manage');
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
    }
  </script>
</body>
</html>
