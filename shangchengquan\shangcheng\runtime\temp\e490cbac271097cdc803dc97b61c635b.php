<?php /*a:4:{s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\logs.html";i:1745486434;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Coze API请求日志</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-history"></i> Coze API请求日志</div>
          <div class="layui-card-body" pad15>
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户</th>
                        <th>请求时间</th>
                        <th>请求IP</th>
                        <th>请求URL</th>
                        <th>请求数据</th>
                        <th>用户代理</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(is_array($logs) || $logs instanceof \think\Collection || $logs instanceof \think\Paginator): $i = 0; $__LIST__ = $logs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$log): $mod = ($i % 2 );++$i;?>
                    <tr>
                        <td><?php echo $log['id']; ?></td>
                        <td><?php echo $log['member_name']; ?></td>
                        <td><?php echo $log['request_time']; ?></td>
                        <td><?php echo $log['request_ip']; ?></td>
                        <td><?php echo $log['request_url']; ?></td>
                        <td>
                            <button class="table-btn view-data" data-data="<?php echo $log['request_data']; ?>">查看</button>
                        </td>
                        <td><?php echo $log['user_agent']; ?></td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </tbody>
            </table>
            
            <div id="page"></div>
          </div>
        </div>
    </div>
  </div>
  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  <script>
  layui.use(['laypage', 'layer'], function(){
      var laypage = layui.laypage;
      var layer = layui.layer;
      var $ = layui.jquery;
      
      // 分页
      laypage.render({
          elem: 'page',
          count: <?php echo $count; ?>,
          limit: <?php echo $limit; ?>,
          curr: <?php echo $page; ?>,
          layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
          jump: function(obj, first){
              if(!first){
                  location.href = '?page=' + obj.curr + '&limit=' + obj.limit;
              }
          }
      });
      
      // 查看数据
      $(document).on('click', '.view-data', function(){
          var data = $(this).data('data');
          try {
              // 尝试格式化JSON
              var jsonData = JSON.parse(data);
              data = JSON.stringify(jsonData, null, 2);
          } catch (e) {
              // 如果不是有效的JSON，直接显示原始数据
          }
          
          layer.open({
              type: 1,
              title: '请求数据',
              area: ['600px', '400px'],
              content: '<pre style="padding: 10px; white-space: pre-wrap; word-wrap: break-word;">' + data + '</pre>'
          });
      });
  });
  </script>
  
</body>
</html> 