<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="3a303d63-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/pages/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;">{{item.nickname+" "+item.showtime+"购买了该商品"}}</view></swiper-item></block></swiper></view></block><block wx:if="{{showtoptabbar==1&&toptabbar_show==1}}"><view class="toptabbar_tab"><view class="{{['item',toptabbar_index==0?'on':'']}}" style="{{'color:'+(toptabbar_index==0?$root.m2:'#333')+';'}}" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">商品<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view><view class="{{['item',toptabbar_index==1?'on':'']}}" style="{{'color:'+(toptabbar_index==1?$root.m4:'#333')+';'}}" data-index="1" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><view class="{{['item',toptabbar_index==2?'on':'']}}" style="{{'color:'+(toptabbar_index==2?$root.m6:'#333')+';'}}" data-index="2" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">详情<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view><block wx:if="{{$root.g1>0}}"><view class="{{['item',toptabbar_index==3?'on':'']}}" style="{{'color:'+(toptabbar_index==3?$root.m8:'#333')+';'}}" data-index="3" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">推荐<view class="after" style="{{'background:'+($root.m9)+';'}}"></view></view></block></view></block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab0"><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix" data-url="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></swiper-item></block></block></swiper><block wx:if="{{product.diypics}}"><view class="imageCount" style="bottom:92rpx;width:140rpx;" data-url="{{'/pagesExt/shop/diylight?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">自助试灯</view></block><view class="imageCount">{{current+1+"/"+$root.g2}}</view><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="/static/img/video.png"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><block wx:if="{{isplay==1}}"><view class="videobox"><video class="video" autoplay="true" id="video" src="{{product.video}}"></video><view data-event-opts="{{[['tap',[['parsevideo',['$event']]]]]}}" class="parsevideo" bindtap="__e">退出播放</view></view></block><block wx:if="{{$root.g3}}"><view class="cuxiaopoint cuxiaoitem" style="background:#fff;padding:0 16rpx;"><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m10+',0.1)')+';'+('color:'+(item.m11)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{shopset.detail_guangao1}}"><view style="background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0;"><block wx:if="{{shopset.detail_guangao1}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao1}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg1Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao1&&shopset.detail_guangao1_t}}"><uni-popup class="vue-ref" vue-id="3a303d63-2" id="gg1Dialog" type="dialog" data-ref="gg1Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao1_t}}" data-url="{{shopset.detail_guangao1_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg1Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block><view class="header"><block wx:if="{{product.price_type!=1||product.min_price>0}}"><block><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m12)+';'}}"><text style="font-size:36rpx;">￥</text>{{product.min_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>{{"-"+product.max_price}}</text></block></view><block wx:if="{{product.market_price*1>product.sell_price*1}}"><view class="f2">{{"￥"+product.market_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>起</text></block></view></block><block wx:if="{{product.huang_dx.types}}"><view class="huang_bz"><view class="huang_i"></view><view class="huang_nums">{{"可用红包："+product.huang_dx.nums}}</view></view></block></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view><block wx:if="{{product.yuanbao}}"><view class="sales_stock" style="margin:0;font-size:26rpx;margin-bottom:10rpx;"><view class="f2">{{"元宝价："+product.yuanbao}}</view></view></block><view class="title">{{product.name}}</view></block></block><block wx:else><block><block wx:if="{{product.xunjia_text}}"><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m13)+';'}}"><text style="font-size:36rpx;">询价</text></view></view></view></block><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view></block></block><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block><block wx:if="{{shopset.showcommission==1&&product.commission>0&&showjiesheng==0}}"><view class="sales_stock"><view class="f2">{{"推广佣金："+product.commission+"元"}}</view></view></block><block wx:if="{{shopset.hide_sales!=1||shopset.hide_stock!=1}}"><view class="sales_stock"><block wx:if="{{shopset.hide_sales!=1}}"><view class="f1">{{"销量："+product.sales+''}}</view></block><block wx:if="{{shopset.hide_stock!=1}}"><view class="f2">{{"库存："+product.stock}}</view></block></view></block><block wx:if="{{shopset.showcommission==1&&product.commission>0&&showjiesheng==0}}"><view class="commission" style="{{'background:'+('rgba('+$root.m14+',0.1)')+';'+('color:'+($root.m15)+';')}}">{{"分享好友购买预计可得"+$root.m16+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.commission}}</text>{{product.commission_desc}}</view></block><block wx:if="{{product.balance_price>0}}"><view style="margin:20rpx 0;color:#333;font-size:22rpx;">{{"首付款金额："+product.advance_price+"元，尾款金额："+product.balance_price+"元"}}</view></block><block wx:if="{{product.buyselect_commission>0}}"><view style="margin:20rpx 0;color:#666;font-size:22rpx;">{{"下单被选奖励预计可得"+$root.m17+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.buyselect_commission}}</text>元</view></block><block wx:if="{{product.upsavemoney>0}}"><view class="upsavemoney" style="{{'background:'+('linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)')+';'+('color:'+('#653a2b')+';')}}"><view class="flex1">{{"升级到 "+product.nextlevelname+" 预计可节省"}}<text style="font-weight:bold;padding:0 2px;color:#ca4312;">{{product.upsavemoney}}</text>元</view><view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312;" data-url="/pagesExa/my/levelup" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即升级<image style="width:30rpx;height:30rpx;" src="/static/img/arrowright2.png"></image></view></view></block></view><view class="choose" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><view class="f0">规格</view><view class="f1 flex1"><block wx:if="{{product.price_type==1}}"><block>查看规格</block></block><block wx:else><block>请选择商品规格及数量</block></block></view><image class="f2" src="/static/img/arrowright.png"></image></view><block wx:if="{{product.xiaofeizhi2>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m18}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m19+product.xiaofeizhi2+" 个"}}</view></view></view></block><block wx:if="{{product.chuangyezhi>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m20}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m21+product.chuangyezhi+" 个"}}</view></view></view></block><block wx:if="{{product.givescore>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m22}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m23+product.givescore+"个"}}</view></view></view></block><block wx:if="{{$root.g4}}"><view class="cuxiaodiv"><block wx:if="{{$root.g5>0}}"><view class="fuwupoint cuxiaoitem"><view class="f0">服务</view><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t">{{item.name}}</view></block></view><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{$root.g6>0}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">促销</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m24+',0.1)')+';'+('color:'+(item.m25)+';')}}"><text class="t0">{{item.$orig.tip}}</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{product.discount_tips!=''}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">折扣</view><view class="f1" style="padding-left:10rpx;">{{product.discount_tips}}</view><view class="f2" data-url="/pagesExa/my/levelinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block><block wx:if="{{$root.g7}}"><view class="cuxiaopoint cuxiaoitem"><view class="f0">优惠</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m26+',0.1)')+';'+('color:'+(item.m27)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="/static/img/arrow-point.png" mode="widthFix"></image></view></view></block></view></block><block wx:if="{{showfuwudialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">服务</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="fuwudialog-content"><view class="f1">{{item.name}}</view><text class="f2">{{item.desc}}</text></view></view></block></view></view></view></block><block wx:if="{{showcuxiaodialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{cuxiaolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="suffix"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.tip}}</text><text style="color:#333;margin-left:20rpx;">{{item.name}}</text></view></view></view></block><couponlist vue-id="3a303d63-3" couponlist="{{couponlist}}" data-event-opts="{{[['^getcoupon',[['getcoupon']]]]}}" bind:getcoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{shopset.detail_guangao2}}"><view style="width:100%;height:auto;padding:20rpx 0 0;"><block wx:if="{{shopset.detail_guangao2}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao2}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg2Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao2&&shopset.detail_guangao2_t}}"><uni-popup class="vue-ref" vue-id="3a303d63-4" id="gg2Dialog" type="dialog" data-ref="gg2Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao2_t}}" data-url="{{shopset.detail_guangao2_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg2Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block></view><view id="scroll_view_tab1"><block wx:if="{{shopset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评率<text style="{{'color:'+($root.m28)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g8>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block></view><view id="scroll_view_tab2"><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m29+' 0%,rgba('+$root.m30+',0.8) 100%)')+';'}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入首页</button></view></block><block wx:if="{{!$root.m31}}"><block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品参数</view><view class="t2"></view><view class="t1"></view></view><view class="paraminfo" style="background:#fff;padding:20rpx 40rpx;"><block wx:for="{{product.paramdata}}" wx:for-item="item" wx:for-index="index"><view class="paramitem"><view class="f1">{{index}}</view><view class="f2">{{item}}</view></view></block></view></block></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="3a303d63-5" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><view id="scroll_view_tab3"><block wx:if="{{$root.g9>0}}"><view><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="/static/img/xihuan.png"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view><view class="prolist"><dp-product-item vue-id="3a303d63-6" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-product-item></view></view></block></view><view style="width:100%;height:140rpx;"></view></scroll-view><block wx:if="{{product.status==1&&!showcuxiaodialog}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view class="item flex1" data-url="/pages/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/gwc.png"></image><view class="t1">购物车</view><block wx:if="{{cartnum>0}}"><view class="cartnum" style="{{'background:'+('rgba('+$root.m32+',0.8)')+';'}}">{{cartnum}}</view></block></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><block wx:if="{{showjiesheng==1}}"><view class="op2"><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="tocart2" style="{{'background:'+($root.m33)+';'}}" bindtap="__e"><text>分享赚钱</text><text style="font-size:24rpx;">{{"赚￥"+product.commission}}</text></view><view class="tobuy2" style="{{'background:'+($root.m34)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><text>立即购买</text><block wx:if="{{product.jiesheng_money>0}}"><text style="font-size:24rpx;">{{"省￥"+product.jiesheng_money}}</text></block></view></view></block><block wx:else><view class="op"><block wx:if="{{product.price_type==1}}"><block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m35)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" bindtap="__e">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view></block></block><block wx:else><block><block wx:if="{{product.freighttype!=3&&product.freighttype!=4}}"><view class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m36)+';'}}" data-btntype="1" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">加入购物车</view></block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m37)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">立即购买</view></block></block></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="3a303d63-7" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^showLinkChange',[['showLinkChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:showLinkChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view hidden="{{!(scrolltopshow)}}" class="scrolltop" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/gotop.png"></image></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m38=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m39=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m40=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view><block wx:if="{{$root.m41}}"><view data-event-opts="{{[['tap',[['shareScheme',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">小程序链接</text></view></block></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{showScheme}}"><view class="posterDialog schemeDialog"><view class="main"><view class="schemecon"><view style="line-height:60rpx;">{{product.name+''}}</view><view>购买链接：<text style="color:#00A0E9;">{{schemeurl}}</text></view><view class="copybtn" style="{{'background:'+('linear-gradient(90deg,'+$root.m42+' 0%,rgba('+$root.m43+',0.8) 100%)')+';'}}" data-text="{{product.name+'购买链接：'+schemeurl}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">一键复制</view></view></view></view></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{sysset.name}}</view><block wx:if="{{product.bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{business.name}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{business.tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m44)+';'}}" data-url="{{'tel::'+business.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{business.tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{business.tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="3a303d63-8" bind:__l="__l"></loading></block><dp-tabbar vue-id="3a303d63-9" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3a303d63-10" data-ref="popmsg" bind:__l="__l"></popmsg></view>