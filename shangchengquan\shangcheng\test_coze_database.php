<?php
// 测试扣子数据库表和数据
require_once 'vendor/autoload.php';

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = 'root123';
$database = 'qixian_zhongheng';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "数据库连接成功！\n";
    
    // 检查扣子相关表是否存在
    $tables = [
        'ddwx_coze_config',
        'ddwx_coze_workflow',
        'ddwx_coze_workflow_log',
        'ddwx_coze_conversation',
        'ddwx_coze_message'
    ];
    
    echo "\n=== 检查数据库表 ===\n";
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "✅ 表 {$table} 存在\n";
            
            // 查询表中的数据数量
            $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$table}");
            $countStmt->execute();
            $count = $countStmt->fetch(PDO::FETCH_ASSOC);
            echo "   数据量: {$count['count']} 条\n";
            
        } else {
            echo "❌ 表 {$table} 不存在\n";
        }
    }
    
    // 检查ddwx_coze_workflow表的数据
    echo "\n=== 检查工作流数据 ===\n";
    try {
        $stmt = $pdo->prepare("SELECT * FROM ddwx_coze_workflow LIMIT 5");
        $stmt->execute();
        $workflows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($workflows)) {
            echo "⚠️  ddwx_coze_workflow 表为空，正在添加测试数据...\n";
            
            // 添加测试工作流数据
            $testData = [
                [
                    'aid' => 1,
                    'name' => '测试工作流1',
                    'workflow_id' => '7476690468139860006',
                    'description' => '这是一个测试工作流',
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ],
                [
                    'aid' => 1,
                    'name' => '测试工作流2',
                    'workflow_id' => '7476690468139860007',
                    'description' => '这是第二个测试工作流',
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO ddwx_coze_workflow 
                (aid, name, workflow_id, description, status, create_time, update_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($testData as $data) {
                $insertStmt->execute([
                    $data['aid'],
                    $data['name'],
                    $data['workflow_id'],
                    $data['description'],
                    $data['status'],
                    $data['create_time'],
                    $data['update_time']
                ]);
            }
            
            echo "✅ 已添加 " . count($testData) . " 条测试数据\n";
            
        } else {
            echo "✅ 找到 " . count($workflows) . " 条工作流数据:\n";
            foreach ($workflows as $workflow) {
                echo "   ID: {$workflow['id']}, 名称: {$workflow['name']}, 工作流ID: {$workflow['workflow_id']}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 查询工作流数据失败: " . $e->getMessage() . "\n";
    }
    
    // 检查API配置数据
    echo "\n=== 检查API配置数据 ===\n";
    try {
        $stmt = $pdo->prepare("SELECT * FROM ddwx_coze_config LIMIT 5");
        $stmt->execute();
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($configs)) {
            echo "⚠️  ddwx_coze_config 表为空，正在添加测试数据...\n";
            
            // 添加测试API配置数据
            $configData = [
                'aid' => 1,
                'name' => '默认配置',
                'api_key' => 'pat_WFZvXcAixTK2EStyRXGQzhLV3qSR5RzsKA4ycvKmvsVkXn4CAg9FGQoaXZpdHuDR',
                'bot_id' => '7476690468139860006',
                'workflow_id' => '7476690468139860006',
                'base_url' => 'https://api.coze.cn',
                'api_version' => 'v1',
                'timeout' => 30,
                'max_retries' => 3,
                'remark' => '测试配置',
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO ddwx_coze_config 
                (aid, name, api_key, bot_id, workflow_id, base_url, api_version, timeout, max_retries, remark, status, create_time, update_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $insertStmt->execute([
                $configData['aid'],
                $configData['name'],
                $configData['api_key'],
                $configData['bot_id'],
                $configData['workflow_id'],
                $configData['base_url'],
                $configData['api_version'],
                $configData['timeout'],
                $configData['max_retries'],
                $configData['remark'],
                $configData['status'],
                $configData['create_time'],
                $configData['update_time']
            ]);
            
            echo "✅ 已添加API配置测试数据\n";
            
        } else {
            echo "✅ 找到 " . count($configs) . " 条API配置数据:\n";
            foreach ($configs as $config) {
                echo "   ID: {$config['id']}, 名称: {$config['name']}, 状态: " . ($config['status'] ? '启用' : '禁用') . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 查询API配置数据失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
?>
