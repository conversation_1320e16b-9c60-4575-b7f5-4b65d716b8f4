# 扣子(Coze)工作流系统说明

## 系统概述

扣子工作流系统是一个完整的AI工作流管理和执行平台，集成了Coze API，提供了工作流配置、管理、执行和监控的完整功能。

## 功能特性

### 1. 工作流管理
- ✅ 工作流列表展示
- ✅ 工作流添加/编辑
- ✅ 工作流删除
- ✅ 工作流状态管理（启用/禁用）
- ✅ 工作流搜索和筛选

### 2. API配置管理
- ✅ Coze API密钥配置
- ✅ API地址配置
- ✅ 配置状态管理
- ✅ API连接测试

### 3. 工作流执行
- ✅ 工作流运行
- ✅ 参数传递支持
- ✅ 文件上传支持
- ✅ 执行状态监控
- ✅ 执行历史记录

### 4. 演示和测试
- ✅ 工作流测试界面
- ✅ 实时执行监控
- ✅ 响应结果展示
- ✅ 调试信息显示

## 文件结构

### 后端控制器
```
app/controller/Coze.php          # 后端管理控制器
app/controller/ApiCoze.php       # 前端API控制器
```

### 公共类库
```
app/common/Coze.php              # Coze API封装类
```

### 后端模板
```
app/home/<USER>/index.html         # 配置管理页面
app/home/<USER>/workflow.html      # 工作流管理页面
app/home/<USER>/edit.html          # 编辑页面（配置/工作流）
app/home/<USER>/demo.html          # 演示测试页面
app/home/<USER>/logs.html          # 日志查看页面
```

### 数据库表
```
ddwx_coze_config                 # API配置表
ddwx_coze_workflow               # 工作流配置表
ddwx_coze_workflow_log           # 工作流执行日志表
ddwx_coze_conversation           # 对话记录表
ddwx_coze_message                # 消息记录表
```

## 主要功能说明

### 1. 工作流管理 (/coze/workflow)
- 显示所有工作流列表
- 支持按名称搜索
- 支持按状态筛选
- 提供添加、编辑、删除、测试操作

### 2. 工作流编辑 (/coze/edit/type/workflow)
- 工作流名称设置
- Coze工作流ID配置
- 工作流描述
- 状态设置（启用/禁用）

### 3. API配置管理 (/coze/index)
- API密钥配置
- API地址设置
- 配置状态管理
- API连接测试

### 4. 工作流测试 (/coze/demo)
- 实时工作流测试
- 参数配置界面
- 执行结果展示
- 状态监控

## API接口说明

### 前端API接口 (ApiCoze.php)
```php
/api/coze/runWorkflow            # 运行工作流
/api/coze/runWorkflowWithFiles   # 运行工作流（带文件）
/api/coze/getWorkflowList        # 获取工作流列表
/api/coze/getWorkflowHistory     # 获取执行历史
```

### 后端管理接口 (Coze.php)
```php
/coze/workflow                   # 工作流管理页面
/coze/edit                       # 编辑页面
/coze/save                       # 保存配置/工作流
/coze/del                        # 删除配置/工作流
/coze/setst                      # 设置状态
/coze/runWorkflowDemo            # 工作流演示
```

## 使用流程

### 1. 配置API
1. 进入"扣子配置"页面
2. 添加Coze API密钥
3. 设置API地址（默认：https://api.coze.cn）
4. 测试API连接

### 2. 添加工作流
1. 进入"工作流管理"页面
2. 点击"添加工作流"
3. 填写工作流名称和Coze工作流ID
4. 设置工作流描述和状态
5. 保存配置

### 3. 测试工作流
1. 在工作流列表中点击"测试"
2. 进入演示页面
3. 配置工作流参数
4. 点击"运行工作流"
5. 查看执行结果

### 4. 前端调用
```javascript
// 运行工作流
$.post('/api/coze/runWorkflow', {
    workflow_id: 'your_workflow_id',
    parameters: {
        input: 'your_input_data'
    }
}, function(res){
    if(res.code == 1){
        console.log('工作流执行成功', res.data);
    }
});
```

## 技术特点

### 1. 代码结构一致性
- 遵循ThinkPHP 6.0框架规范
- 与现有系统代码风格保持一致
- 统一的错误处理和日志记录

### 2. 安全性
- API密钥安全存储
- 请求参数验证
- 权限控制

### 3. 可扩展性
- 模块化设计
- 易于添加新功能
- 支持多种工作流类型

### 4. 用户体验
- 直观的管理界面
- 实时状态监控
- 详细的错误提示

## 注意事项

1. **API密钥安全**：请妥善保管Coze API密钥，避免泄露
2. **工作流ID**：确保从Coze平台获取正确的工作流ID
3. **参数格式**：工作流参数必须为有效的JSON格式
4. **网络连接**：确保服务器能够访问Coze API地址
5. **日志监控**：定期查看执行日志，及时发现问题

## 故障排除

### 1. API连接失败
- 检查API密钥是否正确
- 确认API地址是否可访问
- 检查网络连接状态

### 2. 工作流执行失败
- 验证工作流ID是否正确
- 检查参数格式是否为有效JSON
- 查看执行日志获取详细错误信息

### 3. 页面访问异常
- 确认菜单权限配置
- 检查路由配置是否正确
- 验证模板文件是否存在

### 4. 工作流列表没有数据
- **问题原因**: 数据库表为空或aid值不匹配
- **解决方案**:
  - 访问 `test_data_insert.php` 插入测试数据
  - 检查aid常量是否正确定义
  - 确认数据库连接正常

### 5. layui配置错误
- **问题原因**: 使用了错误的layui.config配置
- **解决方案**:
  - 移除 `layui.config({base: '/static/admin/js/'})` 配置
  - 直接使用 `layui.use(['table', 'form'], function(){})`
  - 参考其他页面的正确配置

## 更新日志

- **2025-01-30**: 修复扣子工作流系统关键问题
  - 修复layui配置错误导致页面无法正常加载
  - 添加测试数据插入脚本解决列表为空问题
  - 优化模板文件结构，保持与系统一致性
  - 完善故障排除文档和解决方案

- **2025-07-30**: 完成扣子工作流系统开发
  - 实现工作流管理功能
  - 添加API配置管理
  - 完善演示测试功能
  - 修复ThinkPHP 6.0兼容性问题
  - 统一代码结构和风格
